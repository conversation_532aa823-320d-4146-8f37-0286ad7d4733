<template>
  <view class="container">
    <view class="header">
      <view class="header-content">
        <image src="/static/logo.jpg" mode="aspectFit" class="logo"></image>
        <text class="title">重置密码</text>
      </view>
    </view>

    <view class="form-container">
      <view class="form-content">
        <view class="form-item">
          <text class="label">用户名</text>
          <input 
            type="text" 
            v-model="username" 
            placeholder="请输入用户名"
            class="input-field"
            @blur="validateUsername"
          />
          <text v-if="errors.username" class="error-text">{{ errors.username }}</text>
        </view>
        
        <view class="form-item">
          <text class="label">邮箱</text>
          <input 
            type="text" 
            v-model="email" 
            placeholder="请输入注册时使用的邮箱"
            class="input-field"
            @blur="validateEmail"
          />
          <text v-if="errors.email" class="error-text">{{ errors.email }}</text>
        </view>
        
        <view v-if="step === 1" class="form-item">
          <button 
            class="submit-button" 
            @click="handleVerify"
            :disabled="isLoading || hasErrors"
          >
            {{ isLoading ? '验证中...' : '验证身份' }}
          </button>
        </view>
        
        <view v-if="step === 2">
          <view class="form-item">
            <text class="label">新密码</text>
            <input 
              type="password" 
              v-model="newPassword" 
              placeholder="请输入新密码"
              class="input-field"
              @blur="validateNewPassword"
            />
            <text v-if="errors.newPassword" class="error-text">{{ errors.newPassword }}</text>
          </view>
          
          <view class="form-item">
            <text class="label">确认密码</text>
            <input 
              type="password" 
              v-model="confirmPassword" 
              placeholder="请再次输入新密码"
              class="input-field"
              @blur="validateConfirmPassword"
            />
            <text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
          </view>
          
          <button 
            class="submit-button" 
            @click="handleResetPassword"
            :disabled="isLoading || hasErrors"
          >
            {{ isLoading ? '重置中...' : '重置密码' }}
          </button>
        </view>

        <view class="back-link" @click="goToLogin">
          返回登录
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'

export default {
  setup() {
    const store = useStore()
    const username = ref('')
    const email = ref('')
    const newPassword = ref('')
    const confirmPassword = ref('')
    const isLoading = ref(false)
    const step = ref(1) // 1: 验证身份, 2: 重置密码
    const errors = ref({
      username: '',
      email: '',
      newPassword: '',
      confirmPassword: ''
    })

    const validateUsername = () => {
      if (!username.value) {
        errors.value.username = '用户名不能为空'
      } else if (username.value.length < 3) {
        errors.value.username = '用户名至少需要3个字符'
      } else {
        errors.value.username = ''
      }
    }

    const validateEmail = () => {
      if (!email.value) {
        errors.value.email = '邮箱不能为空'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
        errors.value.email = '请输入有效的邮箱地址'
      } else {
        errors.value.email = ''
      }
    }

    const validateNewPassword = () => {
      if (!newPassword.value) {
        errors.value.newPassword = '新密码不能为空'
      } else if (newPassword.value.length < 6) {
        errors.value.newPassword = '新密码至少需要6个字符'
      } else {
        errors.value.newPassword = ''
      }
    }

    const validateConfirmPassword = () => {
      if (!confirmPassword.value) {
        errors.value.confirmPassword = '确认密码不能为空'
      } else if (confirmPassword.value !== newPassword.value) {
        errors.value.confirmPassword = '两次输入的密码不一致'
      } else {
        errors.value.confirmPassword = ''
      }
    }

    const hasErrors = computed(() => {
      if (step.value === 1) {
        return Object.values(errors.value).some(error => error !== '') ||
               !username.value ||
               !email.value
      } else {
        return Object.values(errors.value).some(error => error !== '') ||
               !username.value ||
               !email.value ||
               !newPassword.value ||
               !confirmPassword.value
      }
    })

    const handleVerify = async () => {
      validateUsername()
      validateEmail()
      
      if (hasErrors.value) {
        return
      }

      isLoading.value = true
      try {
        const result = await store.dispatch('verifyUserEmail', {
          username: username.value,
          email: email.value
        })
        
        if (result.success) {
          step.value = 2
          uni.showToast({
            title: '身份验证成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('身份验证失败:', error)
        let errorMessage = '身份验证失败'
        
        if (error.message.includes('用户不存在')) {
          errorMessage = '用户名不存在'
          errors.value.username = errorMessage
        } else if (error.message.includes('邮箱不匹配')) {
          errorMessage = '邮箱与用户名不匹配'
          errors.value.email = errorMessage
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        })
      } finally {
        isLoading.value = false
      }
    }

    const handleResetPassword = async () => {
      validateUsername()
      validateEmail()
      validateNewPassword()
      validateConfirmPassword()
      
      if (hasErrors.value) {
        return
      }

      isLoading.value = true
      try {
        await store.dispatch('resetPassword', {
          username: username.value,
          email: email.value,
          newPassword: newPassword.value
        })
        
        uni.showToast({
          title: '密码重置成功',
          icon: 'success'
        })
        
        // 重置成功后跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/login'
          })
        }, 1500)
      } catch (error) {
        console.error('密码重置失败:', error)
        uni.showToast({
          title: error.message || '密码重置失败，请重试',
          icon: 'none',
          duration: 2000
        })
      } finally {
        isLoading.value = false
      }
    }

    const goToLogin = () => {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    }

    return {
      username,
      email,
      newPassword,
      confirmPassword,
      isLoading,
      step,
      errors,
      hasErrors,
      handleVerify,
      handleResetPassword,
      goToLogin,
      validateUsername,
      validateEmail,
      validateNewPassword,
      validateConfirmPassword
    }
  }
}
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0;
  box-sizing: border-box;
}

.header {
  background: #4a90e2;
  padding: 30px 16px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 30px;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-top: 15px;
  }

  .logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    margin-right: 12px;
  }

  .title {
    color: white;
    font-size: 20px;
    font-weight: 500;
  }
}

.form-container {
  flex: 1;
  padding: 0 16px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.form-content {
  width: 100%;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.form-item {
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
  
  .label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
  }

  .input-field {
    width: 100%;
    height: 40px;
    padding: 0 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;

    &:focus {
      border-color: #4a90e2;
      outline: none;
      box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
    }
  }

  .error-text {
    display: block;
    margin-top: 4px;
    color: #ff4d4f;
    font-size: 12px;
  }
}

.submit-button {
  width: 100%;
  height: 40px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  margin-top: 24px;
  transition: background-color 0.3s ease;

  &:hover {
    background: #357abd;
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
}

.back-link {
  text-align: center;
  margin-top: 16px;
  color: #4a90e2;
  font-size: 14px;
  cursor: pointer;
  
  &:hover {
    text-decoration: underline;
  }
}
</style>
