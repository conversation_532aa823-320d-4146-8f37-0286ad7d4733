{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__14D4300", "name": "记账", "version": {"name": "1.0.0", "code": "100"}, "description": "一个简单易用的个人记账应用", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"SQLite": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"render": "always", "id": "1", "kernel": "WKWebview"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "distribute": {"icons": {"android": {"hdpi": "C:/Users/<USER>/Pictures/72.png", "xhdpi": "C:/Users/<USER>/Pictures/96.png", "xxhdpi": "C:/Users/<USER>/Pictures/144.png", "xxxhdpi": "C:/Users/<USER>/Pictures/192.png"}}, "google": {"permissions": ["<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a"], "autoSdkPermissions": true, "packagingOptions": {"exclude": ["META-INF/LICENSE.txt", "META-INF/NOTICE.txt"]}, "icons": {"hdpi": "nativeResources/android/res/drawable-hdpi/icon.png", "xhdpi": "nativeResources/android/res/drawable-xhdpi/icon.png", "xxhdpi": "nativeResources/android/res/drawable-xxhdpi/icon.png", "xxxhdpi": "nativeResources/android/res/drawable-xxxhdpi/icon.png"}}, "apple": {"dSYMs": false, "icons": {"20": "unpackage/res/icons/40x40.png", "40": "unpackage/res/icons/120x120.png", "60": "unpackage/res/icons/180x180.png", "76": "unpackage/res/icons/152x152.png"}}, "plugins": {"ad": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "statusbar": {"immersed": "supportedDevice", "style": "light", "background": "#4a90e2"}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "uni-app": {"control": "uni-v3", "vueVersion": "3", "compilerVersion": "4.57", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal", "webView": {"minUserAgentVersion": "49.0"}}}, "app-harmony": {"useragent": {"value": "uni-app", "concatenate": true}, "distribute": {"icons": {"foreground": "C:/Users/<USER>/Pictures/account.png"}}, "uniStatistics": {"enable": false}}, "launch_path": "__uniappview.html"}