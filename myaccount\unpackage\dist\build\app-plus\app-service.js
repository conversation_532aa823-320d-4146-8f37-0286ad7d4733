if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((a=>t.resolve(e()).then((()=>a))),(a=>t.resolve(e()).then((()=>{throw a}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16<PERSON>rray,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.<PERSON>Int64Array,BigUint64Array=e.<PERSON>Uint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...a){uni.__log__?uni.__log__(e,t,...a):console[e].apply(console,[...a,t])}function a(){return"undefined"!=typeof navigator&&"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}}const r="function"==typeof Proxy;class o{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const a={};if(e.settings)for(const n in e.settings){const t=e.settings[n];a[n]=t.defaultValue}const r=`__vue-devtools-plugin-settings__${e.id}`;let o={...a};try{const e=localStorage.getItem(r),t=JSON.parse(e);Object.assign(o,t)}catch(s){}this.fallbacks={getSettings:()=>o,setSettings(e){try{localStorage.setItem(r,JSON.stringify(e))}catch(s){}o=e}},t.on("plugin:settings:set",((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((a=>{this.targetQueue.push({method:t,args:e,resolve:a})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function s(e,t){const s=a(),n=a().__VUE_DEVTOOLS_GLOBAL_HOOK__,l=r&&e.enableEarlyProxy;if(!n||!s.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&l){const a=l?new o(e,n):null;(s.__VUE_DEVTOOLS_PLUGINS__=s.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:e,setupFn:t,proxy:a}),a&&t(a.proxiedTarget)}else n.emit("devtools-plugin:setup",e,t)}
/*!
   * vuex v4.1.0
   * (c) 2022 Evan You
   * @license MIT
   */var n="store";function l(t){return void 0===t&&(t=null),e.inject(null!==t?t:n)}function i(e,t){Object.keys(e).forEach((function(a){return t(e[a],a)}))}function c(e,t,a){return t.indexOf(e)<0&&(a&&a.prepend?t.unshift(e):t.push(e)),function(){var a=t.indexOf(e);a>-1&&t.splice(a,1)}}function u(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var a=e.state;m(e,a,[],e._modules.root,!0),d(e,a,t)}function d(t,a,r){var o=t._state,s=t._scope;t.getters={},t._makeLocalGettersCache=Object.create(null);var n=t._wrappedGetters,l={},c={},u=e.effectScope(!0);u.run((function(){i(n,(function(a,r){l[r]=function(e,t){return function(){return e(t)}}(a,t),c[r]=e.computed((function(){return l[r]()})),Object.defineProperty(t.getters,r,{get:function(){return c[r].value},enumerable:!0})}))})),t._state=e.reactive({data:a}),t._scope=u,t.strict&&function(t){e.watch((function(){return t._state.data}),(function(){}),{deep:!0,flush:"sync"})}(t),o&&r&&t._withCommit((function(){o.data=null})),s&&s.stop()}function m(e,t,a,r,o){var s=!a.length,n=e._modules.getNamespace(a);if(r.namespaced&&(e._modulesNamespaceMap[n],e._modulesNamespaceMap[n]=r),!s&&!o){var l=p(t,a.slice(0,-1)),i=a[a.length-1];e._withCommit((function(){l[i]=r.state}))}var c=r.context=function(e,t,a){var r=""===t,o={dispatch:r?e.dispatch:function(a,r,o){var s=h(a,r,o),n=s.payload,l=s.options,i=s.type;return l&&l.root||(i=t+i),e.dispatch(i,n)},commit:r?e.commit:function(a,r,o){var s=h(a,r,o),n=s.payload,l=s.options,i=s.type;l&&l.root||(i=t+i),e.commit(i,n,l)}};return Object.defineProperties(o,{getters:{get:r?function(){return e.getters}:function(){return g(e,t)}},state:{get:function(){return p(e.state,a)}}}),o}(e,n,a);r.forEachMutation((function(t,a){!function(e,t,a,r){(e._mutations[t]||(e._mutations[t]=[])).push((function(t){a.call(e,r.state,t)}))}(e,n+a,t,c)})),r.forEachAction((function(t,a){var r=t.root?a:n+a,o=t.handler||t;!function(e,t,a,r){(e._actions[t]||(e._actions[t]=[])).push((function(t){var o,s=a.call(e,{dispatch:r.dispatch,commit:r.commit,getters:r.getters,state:r.state,rootGetters:e.getters,rootState:e.state},t);return(o=s)&&"function"==typeof o.then||(s=Promise.resolve(s)),e._devtoolHook?s.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):s}))}(e,r,o,c)})),r.forEachGetter((function(t,a){!function(e,t,a,r){if(e._wrappedGetters[t])return;e._wrappedGetters[t]=function(e){return a(r.state,r.getters,e.state,e.getters)}}(e,n+a,t,c)})),r.forEachChild((function(r,s){m(e,t,a.concat(s),r,o)}))}function g(e,t){if(!e._makeLocalGettersCache[t]){var a={},r=t.length;Object.keys(e.getters).forEach((function(o){if(o.slice(0,r)===t){var s=o.slice(r);Object.defineProperty(a,s,{get:function(){return e.getters[o]},enumerable:!0})}})),e._makeLocalGettersCache[t]=a}return e._makeLocalGettersCache[t]}function p(e,t){return t.reduce((function(e,t){return e[t]}),e)}function h(e,t,a){var r;return null!==(r=e)&&"object"==typeof r&&e.type&&(a=t,t=e,e=e.type),{type:e,payload:t,options:a}}var v="vuex:mutations",f="vuex:actions",w="vuex",y=0;function E(e,t){s({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:["vuex bindings"]},(function(a){a.addTimelineLayer({id:v,label:"Vuex Mutations",color:b}),a.addTimelineLayer({id:f,label:"Vuex Actions",color:b}),a.addInspector({id:w,label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),a.on.getInspectorTree((function(a){if(a.app===e&&a.inspectorId===w)if(a.filter){var r=[];x(r,t._modules.root,a.filter,""),a.rootNodes=r}else a.rootNodes=[_(t._modules.root,"")]})),a.on.getInspectorState((function(a){if(a.app===e&&a.inspectorId===w){var r=a.nodeId;g(t,r),a.state=function(e,t,a){t="root"===a?t:t[a];var r=Object.keys(t),o={state:Object.keys(e.state).map((function(t){return{key:t,editable:!0,value:e.state[t]}}))};if(r.length){var s=function(e){var t={};return Object.keys(e).forEach((function(a){var r=a.split("/");if(r.length>1){var o=t,s=r.pop();r.forEach((function(e){o[e]||(o[e]={_custom:{value:{},display:e,tooltip:"Module",abstract:!0}}),o=o[e]._custom.value})),o[s]=S((function(){return e[a]}))}else t[a]=S((function(){return e[a]}))})),t}(t);o.getters=Object.keys(s).map((function(e){return{key:e.endsWith("/")?V(e):e,editable:!1,value:S((function(){return s[e]}))}}))}return o}((o=t._modules,(n=(s=r).split("/").filter((function(e){return e}))).reduce((function(e,t,a){var r=e[t];if(!r)throw new Error('Missing module "'+t+'" for path "'+s+'".');return a===n.length-1?r:r._children}),"root"===s?o:o.root._children)),"root"===r?t.getters:t._makeLocalGettersCache,r)}var o,s,n})),a.on.editInspectorState((function(a){if(a.app===e&&a.inspectorId===w){var r=a.nodeId,o=a.path;"root"!==r&&(o=r.split("/").filter(Boolean).concat(o)),t._withCommit((function(){a.set(t._state.data,o,a.state.value)}))}})),t.subscribe((function(e,t){var r={};e.payload&&(r.payload=e.payload),r.state=t,a.notifyComponentUpdate(),a.sendInspectorTree(w),a.sendInspectorState(w),a.addTimelineEvent({layerId:v,event:{time:Date.now(),title:e.type,data:r}})})),t.subscribeAction({before:function(e,t){var r={};e.payload&&(r.payload=e.payload),e._id=y++,e._time=Date.now(),r.state=t,a.addTimelineEvent({layerId:f,event:{time:e._time,title:e.type,groupId:e._id,subtitle:"start",data:r}})},after:function(e,t){var r={},o=Date.now()-e._time;r.duration={_custom:{type:"duration",display:o+"ms",tooltip:"Action duration",value:o}},e.payload&&(r.payload=e.payload),r.state=t,a.addTimelineEvent({layerId:f,event:{time:Date.now(),title:e.type,groupId:e._id,subtitle:"end",data:r}})}})}))}var b=8702998,N={label:"namespaced",textColor:16777215,backgroundColor:6710886};function V(e){return e&&"root"!==e?e.split("/").slice(-2,-1)[0]:"Root"}function _(e,t){return{id:t||"root",label:V(t),tags:e.namespaced?[N]:[],children:Object.keys(e._children).map((function(a){return _(e._children[a],t+a+"/")}))}}function x(e,t,a,r){r.includes(a)&&e.push({id:r||"root",label:r.endsWith("/")?r.slice(0,r.length-1):r||"Root",tags:t.namespaced?[N]:[]}),Object.keys(t._children).forEach((function(o){x(e,t._children[o],a,r+o+"/")}))}function S(e){try{return e()}catch(t){return t}}var C=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var a=e.state;this.state=("function"==typeof a?a():a)||{}},T={namespaced:{configurable:!0}};T.namespaced.get=function(){return!!this._rawModule.namespaced},C.prototype.addChild=function(e,t){this._children[e]=t},C.prototype.removeChild=function(e){delete this._children[e]},C.prototype.getChild=function(e){return this._children[e]},C.prototype.hasChild=function(e){return e in this._children},C.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},C.prototype.forEachChild=function(e){i(this._children,e)},C.prototype.forEachGetter=function(e){this._rawModule.getters&&i(this._rawModule.getters,e)},C.prototype.forEachAction=function(e){this._rawModule.actions&&i(this._rawModule.actions,e)},C.prototype.forEachMutation=function(e){this._rawModule.mutations&&i(this._rawModule.mutations,e)},Object.defineProperties(C.prototype,T);var k=function(e){this.register([],e,!1)};function D(e,t,a){if(t.update(a),a.modules)for(var r in a.modules){if(!t.getChild(r))return;D(e.concat(r),t.getChild(r),a.modules[r])}}k.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},k.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,a){return e+((t=t.getChild(a)).namespaced?a+"/":"")}),"")},k.prototype.update=function(e){D([],this.root,e)},k.prototype.register=function(e,t,a){var r=this;void 0===a&&(a=!0);var o=new C(t,a);0===e.length?this.root=o:this.get(e.slice(0,-1)).addChild(e[e.length-1],o);t.modules&&i(t.modules,(function(t,o){r.register(e.concat(o),t,a)}))},k.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),a=e[e.length-1],r=t.getChild(a);r&&r.runtime&&t.removeChild(a)},k.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),a=e[e.length-1];return!!t&&t.hasChild(a)};var j=function(e){var t=this;void 0===e&&(e={});var a=e.plugins;void 0===a&&(a=[]);var r=e.strict;void 0===r&&(r=!1);var o=e.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new k(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=o;var s=this,n=this.dispatch,l=this.commit;this.dispatch=function(e,t){return n.call(s,e,t)},this.commit=function(e,t,a){return l.call(s,e,t,a)},this.strict=r;var i=this._modules.root.state;m(this,i,[],this._modules.root),d(this,i),a.forEach((function(e){return e(t)}))},A={state:{configurable:!0}};j.prototype.install=function(e,t){e.provide(t||n,this),e.config.globalProperties.$store=this,void 0!==this._devtools&&this._devtools&&E(e,this)},A.state.get=function(){return this._state.data},A.state.set=function(e){},j.prototype.commit=function(e,t,a){var r=this,o=h(e,t,a),s=o.type,n=o.payload,l={type:s,payload:n},i=this._mutations[s];i&&(this._withCommit((function(){i.forEach((function(e){e(n)}))})),this._subscribers.slice().forEach((function(e){return e(l,r.state)})))},j.prototype.dispatch=function(e,t){var a=this,r=h(e,t),o=r.type,s=r.payload,n={type:o,payload:s},l=this._actions[o];if(l){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(n,a.state)}))}catch(c){}var i=l.length>1?Promise.all(l.map((function(e){return e(s)}))):l[0](s);return new Promise((function(e,t){i.then((function(t){try{a._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(n,a.state)}))}catch(c){}e(t)}),(function(e){try{a._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(n,a.state,e)}))}catch(c){}t(e)}))}))}},j.prototype.subscribe=function(e,t){return c(e,this._subscribers,t)},j.prototype.subscribeAction=function(e,t){return c("function"==typeof e?{before:e}:e,this._actionSubscribers,t)},j.prototype.watch=function(t,a,r){var o=this;return e.watch((function(){return t(o.state,o.getters)}),a,Object.assign({},r))},j.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._state.data=e}))},j.prototype.registerModule=function(e,t,a){void 0===a&&(a={}),"string"==typeof e&&(e=[e]),this._modules.register(e,t),m(this,this.state,e,this._modules.get(e),a.preserveState),d(this,this.state)},j.prototype.unregisterModule=function(e){var t=this;"string"==typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){delete p(t.state,e.slice(0,-1))[e[e.length-1]]})),u(this)},j.prototype.hasModule=function(e){return"string"==typeof e&&(e=[e]),this._modules.isRegistered(e)},j.prototype.hotUpdate=function(e){this._modules.update(e),u(this,!0)},j.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(j.prototype,A);const L="myaccount.db",I="_doc/myaccount.db";let P=!1;const M=async()=>{if(!P)return new Promise(((e,a)=>{t("log","at utils/db.js:15","尝试打开数据库"),plus.sqlite.openDatabase({name:L,path:I,success:()=>{t("log","at utils/db.js:20","数据库打开成功"),P=!0,e()},fail:r=>{if(-1402===r.code)return t("log","at utils/db.js:27","数据库已经打开"),P=!0,void e();t("error","at utils/db.js:32","数据库打开失败，错误详情:",r),a(new Error("数据库打开失败: "+JSON.stringify(r)))}})}));t("log","at utils/db.js:10","数据库已经打开")},O=async()=>{if(P)return new Promise(((e,a)=>{t("log","at utils/db.js:47","尝试关闭数据库"),plus.sqlite.closeDatabase({name:L,success:()=>{t("log","at utils/db.js:51","数据库关闭成功"),P=!1,e()},fail:r=>{if(-1401===r.code)return t("log","at utils/db.js:58","数据库已经关闭"),P=!1,void e();t("error","at utils/db.js:63","数据库关闭失败，错误详情:",r),a(new Error("数据库关闭失败: "+JSON.stringify(r)))}})}));t("log","at utils/db.js:42","数据库已经关闭")},B=async(e,a=[])=>{try{t("log","at utils/db.js:73","准备执行SQL:",{sql:e,params:a}),P||await M();const r=Array.isArray(a)?a:[a];t("log","at utils/db.js:83","SQL执行详情:",{sql:e,params:r,paramsType:r.map((e=>typeof e)),paramsLength:r.length});let o=e;return r.forEach(((e,t)=>{const a=o.indexOf("?");if(-1!==a){const t="string"==typeof e?`'${e}'`:e;o=o.substring(0,a)+t+o.substring(a+1)}})),t("log","at utils/db.js:101","最终执行的SQL:",o),new Promise(((e,a)=>{plus.sqlite.executeSql({name:L,sql:o,success:a=>{t("log","at utils/db.js:108","SQL执行成功:",{sql:o,result:a}),e(a)},fail:e=>{t("error","at utils/db.js:112","SQL执行失败:",{sql:o,error:e,errorCode:e.code,errorMessage:e.message}),a(new Error("SQL执行失败: "+JSON.stringify(e)))}})}))}catch(r){throw t("error","at utils/db.js:123","执行SQL失败:",r),r}},U=async(e,a=[])=>{try{t("log","at utils/db.js:131","准备执行查询:",{sql:e,params:a}),P||await M();const r=Array.isArray(a)?a:[a];t("log","at utils/db.js:141","查询详情:",{sql:e,params:r,paramsType:r.map((e=>typeof e)),paramsLength:r.length});let o=e;return r.forEach(((e,t)=>{const a=o.indexOf("?");if(-1!==a){const t="string"==typeof e?`'${e}'`:e;o=o.substring(0,a)+t+o.substring(a+1)}})),t("log","at utils/db.js:159","最终执行的查询:",o),new Promise(((e,a)=>{plus.sqlite.selectSql({name:L,sql:o,success:a=>{t("log","at utils/db.js:166","查询成功:",{sql:o,result:a}),e(a)},fail:e=>{t("error","at utils/db.js:170","查询失败:",{sql:o,error:e,errorCode:e.code,errorMessage:e.message}),a(new Error("查询失败: "+JSON.stringify(e)))}})}))}catch(r){throw t("error","at utils/db.js:181","查询SQL失败:",r),r}},R=async e=>{try{t("log","at utils/db.js:270",`开始获取表 ${e} 的创建语句`);const a=await U("SELECT sql FROM sqlite_master WHERE type='table' AND name=?",[e]);if(a.length>0)return t("log","at utils/db.js:276",`表 ${e} 创建语句:`,a[0].sql),a[0].sql;throw new Error(`表 ${e} 不存在`)}catch(a){throw t("error","at utils/db.js:281",`获取表 ${e} 创建语句失败:`,a),a}},q=async()=>{try{t("log","at utils/db.js:802","开始获取数据库文件路径"),P||await M();const e=plus.io.convertLocalFileSystemURL(I);return t("log","at utils/db.js:811","数据库文件的实际路径:",e),e}catch(e){throw t("error","at utils/db.js:815","获取数据库文件路径失败:",e),e}},F={openDatabase:M,closeDatabase:O,executeSql:B,selectSql:U,initDatabase:async()=>{try{t("log","at utils/db.js:189","开始初始化数据库"),await M(),await B("\n            CREATE TABLE IF NOT EXISTS users (\n                user_id TEXT PRIMARY KEY,\n                username TEXT NOT NULL CHECK(username != ''),\n                password TEXT NOT NULL,\n                email TEXT NOT NULL,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n                UNIQUE(username),\n                UNIQUE(email)\n            )\n        "),await B("\n            CREATE TABLE IF NOT EXISTS categories (\n                category_id TEXT PRIMARY KEY,\n                user_id TEXT NOT NULL,\n                parent_id TEXT,\n                name TEXT NOT NULL,\n                level INTEGER NOT NULL,\n                sort_order INTEGER NOT NULL,\n                total_amount REAL DEFAULT 0,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n            )\n        "),await B("\n            CREATE TABLE IF NOT EXISTS expenses (\n                record_id TEXT PRIMARY KEY,\n                user_id TEXT NOT NULL,\n                category_id TEXT NOT NULL,\n                target TEXT,\n                amount REAL NOT NULL,\n                record_date DATE NOT NULL,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n            )\n        "),t("log","at utils/db.js:232","数据库表初始化成功")}catch(e){throw t("error","at utils/db.js:234","数据库初始化失败:",e),e}},getAllTables:async()=>{try{t("log","at utils/db.js:242","开始获取所有表名");const e=await U("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");return t("log","at utils/db.js:246","获取到的表名:",e),e.map((e=>e.name))}catch(e){throw t("error","at utils/db.js:249","获取表名失败:",e),e}},getAllFromTable:async e=>{try{t("log","at utils/db.js:257",`开始获取表 ${e} 的所有数据`);const a=await U(`SELECT * FROM ${e}`);return t("log","at utils/db.js:259",`表 ${e} 数据获取成功:`,a.length,"条记录"),a}catch(a){throw t("error","at utils/db.js:262",`获取表 ${e} 数据失败:`,a),a}},getTableCreateStatement:R,createBackupDatabase:async e=>{try{t("log","at utils/db.js:289","开始创建备份数据库:",e);try{await new Promise((a=>{plus.io.resolveLocalFileSystemURL(e,(e=>{e.remove((()=>{t("log","at utils/db.js:296","已删除旧的备份数据库文件"),a()}),(e=>{t("log","at utils/db.js:299","删除旧文件失败，可能不存在:",e),a()}))}),(e=>{t("log","at utils/db.js:303","旧文件不存在，继续创建新文件"),a()}))})),await new Promise((e=>setTimeout(e,200)))}catch(a){t("log","at utils/db.js:311","处理旧文件时出错，继续创建新文件:",a)}await new Promise(((a,r)=>{plus.sqlite.openDatabase({name:"backup_db",path:e,success:function(e){t("log","at utils/db.js:320","备份数据库创建成功"),a()},fail:function(e){t("error","at utils/db.js:324","备份数据库创建失败:",e),r(new Error("创建备份数据库失败: "+JSON.stringify(e)))}})}));const r=[{sql:"PRAGMA synchronous=NORMAL",message:"已设置同步模式为NORMAL"},{sql:"PRAGMA journal_mode=DELETE",message:"已设置日志模式为DELETE"},{sql:"PRAGMA page_size=4096",message:"已设置页面大小为4KB"},{sql:"PRAGMA cache_size=2000",message:"已设置缓存大小"}];for(const e of r)try{await new Promise((a=>{plus.sqlite.executeSql({name:"backup_db",sql:e.sql,success:function(){t("log","at utils/db.js:362",e.message),a()},fail:function(r){t("log","at utils/db.js:366",`设置 ${e.sql} 失败，继续执行:`,r),a()}})}))}catch(a){t("log","at utils/db.js:372",`执行 ${e.sql} 时出错，继续执行:`,a)}return"backup_db"}catch(a){throw t("error","at utils/db.js:378","创建备份数据库失败:",a),a}},recreateTableInBackup:async(e,a,r)=>{try{t("log","at utils/db.js:485",`开始在备份数据库中重建表 ${a}`);const s=await R(a);if(t("log","at utils/db.js:489",`表 ${a} 的创建语句:`,s),await new Promise(((r,o)=>{plus.sqlite.executeSql({name:e,sql:`DROP TABLE IF EXISTS ${a}`,success:function(){t("log","at utils/db.js:497",`已删除旧的 ${a} 表`),r()},fail:function(e){t("log","at utils/db.js:501",`删除表 ${a} 时出错:`,e),r()}})})),await new Promise(((r,o)=>{plus.sqlite.executeSql({name:e,sql:s,success:function(e){t("log","at utils/db.js:513",`表 ${a} 创建成功`),r(e)},fail:function(e){t("error","at utils/db.js:517",`创建表 ${a} 失败:`,e),o(e)}})})),r&&r.length>0){t("log","at utils/db.js:525",`开始插入表 ${a} 的数据:`,r.length,"条记录"),await new Promise(((r,o)=>{plus.sqlite.executeSql({name:e,sql:"BEGIN TRANSACTION",success:function(){t("log","at utils/db.js:533",`开始事务: 表 ${a}`),r()},fail:o})}));try{const o=100;let s=0;for(let n=0;n<r.length;n+=o){const l=r.slice(n,n+o);s++,t("log","at utils/db.js:548",`开始处理第 ${s} 批数据，共 ${l.length} 条`);for(const r of l){const o=Object.keys(r),s=o.map((e=>{const t=r[e];return null==t?"NULL":"string"==typeof t?`'${t.replace(/'/g,"''")}'`:t instanceof Date?`'${t.toISOString()}'`:t.toString()})),n=`INSERT INTO ${a} (${o.join(",")}) VALUES (${s.join(",")})`;t("log","at utils/db.js:570","执行SQL:",n),await new Promise(((a,r)=>{plus.sqlite.executeSql({name:e,sql:n,success:function(){a()},fail:function(e){t("error","at utils/db.js:580","插入数据失败:",{error:e,sql:n}),r(e)}})}))}n+o<r.length&&(await new Promise(((a,r)=>{plus.sqlite.executeSql({name:e,sql:"COMMIT",success:function(){t("log","at utils/db.js:598",`提交第 ${s} 批数据事务`),a()},fail:function(e){t("error","at utils/db.js:602",`提交第 ${s} 批数据事务失败:`,e),r(e)}})})),await new Promise(((a,r)=>{plus.sqlite.executeSql({name:e,sql:"BEGIN TRANSACTION",success:function(){t("log","at utils/db.js:614",`开始第 ${s+1} 批数据事务`),a()},fail:function(e){t("error","at utils/db.js:618",`开始第 ${s+1} 批数据事务失败:`,e),r(e)}})})),await new Promise((e=>setTimeout(e,100))))}await new Promise(((r,o)=>{plus.sqlite.executeSql({name:e,sql:"COMMIT",success:function(){t("log","at utils/db.js:635",`提交最后一批数据事务: 表 ${a}`),r()},fail:function(e){t("error","at utils/db.js:639","提交最后一批数据事务失败:",e),o(e)}})})),t("log","at utils/db.js:645",`表 ${a} 数据插入完成`)}catch(o){throw t("error","at utils/db.js:648","插入数据出错，准备回滚:",o),await new Promise((r=>{plus.sqlite.executeSql({name:e,sql:"ROLLBACK",success:function(){t("log","at utils/db.js:654",`事务已回滚: 表 ${a}`),r()},fail:function(e){t("error","at utils/db.js:658","回滚事务失败:",e),r()}})})),o}}}catch(o){throw t("error","at utils/db.js:667",`重建表 ${a} 失败:`,o),o}},closeBackupDatabase:async e=>{try{t("log","at utils/db.js:675","开始关闭备份数据库");const r=[{sql:"COMMIT",message:"确保没有活跃事务",errorMessage:"没有活跃事务需要提交"},{sql:"PRAGMA locking_mode=EXCLUSIVE",message:"已设置独占锁定模式",errorMessage:"设置锁定模式失败"},{sql:"PRAGMA synchronous=FULL",message:"已设置同步模式为FULL",errorMessage:"设置同步模式失败"},{sql:"PRAGMA journal_mode=DELETE",message:"已切换到DELETE日志模式",errorMessage:"切换日志模式失败"},{sql:"PRAGMA wal_checkpoint(FULL)",message:"检查点完成",errorMessage:"检查点操作失败"}];for(const n of r)try{await new Promise((a=>{plus.sqlite.executeSql({name:e,sql:n.sql,success:function(){t("log","at utils/db.js:719",n.message),a()},fail:function(e){t("log","at utils/db.js:723",`${n.errorMessage}，继续执行:`,e),a()}})})),await new Promise((e=>setTimeout(e,100)))}catch(a){t("log","at utils/db.js:731",`执行 ${n.sql} 时出错，继续执行:`,a)}try{await new Promise((a=>{plus.sqlite.executeSql({name:e,sql:"PRAGMA optimize",success:function(){t("log","at utils/db.js:742","数据库优化完成"),a()},fail:function(e){t("log","at utils/db.js:746","数据库优化失败，继续执行:",e),a()}})}))}catch(a){t("log","at utils/db.js:752","数据库优化时出错，继续执行:",a)}await new Promise((e=>setTimeout(e,500)));let o=0;const s=3;for(;o<s;){try{await new Promise(((a,r)=>{plus.sqlite.closeDatabase({name:e,success:function(){t("log","at utils/db.js:768",`备份数据库关闭成功 (尝试 ${o+1}/${s})`),a()},fail:function(e){t("error","at utils/db.js:772",`备份数据库关闭失败 (尝试 ${o+1}/${s}):`,e),o===s-1?r(new Error("关闭备份数据库失败: "+JSON.stringify(e))):a(!1)}})}));break}catch(a){if(o===s-1)throw a}o++,await new Promise((e=>setTimeout(e,300)))}}catch(a){throw t("error","at utils/db.js:794","关闭备份数据库失败:",a),a}},getDatabaseFilePath:q,isDatabaseOpen:()=>P,backupDatabaseByFileCopy:async e=>{let a=null,r=null,o=null,s=null;try{t("log","at utils/db.js:877","开始直接复制数据库文件到:",e);const n=await q();t("log","at utils/db.js:881","原始数据库文件路径:",n),await(async()=>{P&&(await O(),await new Promise((e=>setTimeout(e,1e3))))})();const l=plus.android.importClass("java.io.File"),i=plus.android.importClass("java.io.FileInputStream"),c=plus.android.importClass("java.io.FileOutputStream"),u=(plus.android.importClass("java.nio.channels.FileChannel"),new l(n));if(!u.exists())throw new Error("源数据库文件不存在");const d=new l(e);d.exists()&&d.delete();const m=d.getParentFile();return m.exists()||m.mkdirs(),o=new i(u),s=new c(d),a=o.getChannel(),r=s.getChannel(),r.transferFrom(a,0,a.size()),t("log","at utils/db.js:922","文件复制成功，大小:",d.length()),d.length()}catch(n){throw t("error","at utils/db.js:926","直接复制数据库文件失败:",n),n}finally{((e,a)=>{try{e&&e.close(),a&&a.close()}catch(r){t("error","at utils/db.js:865","关闭通道失败:",r)}})(a,r),o&&o.close(),s&&s.close(),await(async()=>{try{await M()}catch(e){t("error","at utils/db.js:855","重新打开数据库失败:",e)}})()}},isSupportVacuumInto:async()=>{try{const e=(await U("SELECT sqlite_version() as version"))[0].version;t("log","at utils/db.js:949","SQLite版本:",e);const a=e.split("."),r=parseInt(a[0]),o=parseInt(a[1]);return r>3||3===r&&o>=27}catch(e){return t("error","at utils/db.js:959","检查SQLite版本失败:",e),!1}},isHarmonyOS:()=>{try{const e=plus.android.importClass("android.os.Build"),a=e.MANUFACTURER,r=e.MODEL,o=e.BRAND;return t("log","at utils/db.js:828","设备信息:",{manufacturer:a,model:r,brand:o}),a.toLowerCase().includes("huawei")||o.toLowerCase().includes("huawei")||r.toLowerCase().includes("harmony")||r.toLowerCase().includes("hongmeng")}catch(e){return t("error","at utils/db.js:836","检测设备系统时出错:",e),!1}}},$="/static/logo.jpg",G=(e,t)=>{const a=e.__vccOpts||e;for(const[r,o]of t)a[r]=o;return a};const H=G({setup(){const a=l(),r=e.ref(!1),o=e.ref(new Map),s=e.ref(new Map),n=e.ref(!1),i=e.ref(null),c=e.ref(!1),u=e.ref(!1),d=e.ref(""),m=e.computed((()=>(t("log","at pages/index/index.vue:148","Computing categories from store:",a.state.categories),a.state.categories))),g=e=>{const t=o.value.get(e),a=v(e);if(!a)return 0;const r=(null==t?void 0:t.amount)?parseFloat(t.amount):0;return(a.total_amount||0)+r},p=e=>{if(!e.children||0===e.children.length){const t=s.value.get(e.category_id);return void 0!==t?t:e.total_amount||0}return e.children.reduce(((e,t)=>e+p(t)),0)},h=()=>m.value&&0!==m.value.length?m.value.reduce(((e,t)=>e+p(t)),0):0,v=e=>{const t=a=>{for(const r of a){if(r.category_id===e)return r;if(r.children&&r.children.length>0){const e=t(r.children);if(e)return e}}return null};return t(m.value)},f=async()=>{try{const e=await F.selectSql("SELECT MIN(record_date) as earliest_date FROM expenses WHERE user_id = ?",[a.state.user.user_id]);if(e&&e.length>0&&e[0].earliest_date){const t=new Date(e[0].earliest_date),a=t.getFullYear(),r=t.getMonth()+1,o=t.getDate();i.value=`${a}年${r}月${o}日`}else i.value="暂无记录"}catch(e){t("error","at pages/index/index.vue:333","获取最早日期失败:",e),i.value="暂无记录"}},w=async()=>{if(!n.value)try{t("log","at pages/index/index.vue:343","开始获取分类数据...");const e=await a.dispatch("fetchCategories");t("log","at pages/index/index.vue:345","获取到的分类数据:",e),e&&e.length>0?(t("log","at pages/index/index.vue:348","构建的分类树:",e),a.commit("updateCategories",e),t("log","at pages/index/index.vue:350","分类数据已更新到store")):t("log","at pages/index/index.vue:352","未获取到分类数据"),n.value=!0}catch(e){t("error","at pages/index/index.vue:356","获取分类数据失败:",e)}},y=async()=>{if(!d.value.trim())return void uni.showToast({title:"请输入消费记录",icon:"none"});const e=(()=>{const e=[],t=a=>{for(const r of a)r.children&&r.children.length>0?t(r.children):e.push(r.name)};return t(m.value),e})();t("log","at pages/index/index.vue:424","可用的三级分类:",e);try{const o=await uni.request({url:"https://api.deepseek.com/v1/chat/completions",method:"POST",data:{messages:[{role:"system",content:`你是一个专业的消费记录分析助手。请分析用户输入的消费记录，并返回一个JSON数组，每个元素包含以下字段：\n{\n  "category_id": "分类ID",\n  "target": "消费标的",\n  "amount": 金额\n}\n\n可用的分类ID包括：\n${e.map((e=>`'${e}'`)).join(", ")}\n\n非常重要的要求：\n1. 只返回JSON数组，不要包含其他文字\n2. 【特别重要】当用户输入多个同类消费时（如"早饭20，午饭30，晚饭30"），你必须将它们合并为一个元素，将金额相加（如80），并将消费标的合并（如"早饭,午饭,晚饭"）\n3. 【特别重要】如果多个消费项目属于同一个分类ID，必须将它们合并为一个元素，将金额相加\n4. 金额必须是数字类型，可以是负数（表示对冲错误记录），不要包含货币符号或单位\n5. 分类ID必须从上述列表中选择，不要创建新的分类ID\n6. 如果无法确定具体分类，使用列表中的最后一个分类作为默认分类\n\n示例输入："早饭20，午饭30，晚饭30"\n正确的输出（假设这些都属于"餐饮"分类）：\n[{"category_id":"餐饮","target":"早饭,午饭,晚饭","amount":80}]\n\n错误的输出（不要这样做）：\n[{"category_id":"餐饮","target":"早饭","amount":20},{"category_id":"餐饮","target":"午饭","amount":30},{"category_id":"餐饮","target":"晚饭","amount":30}]`},{role:"user",content:d.value}],model:"deepseek-chat",temperature:.2},header:{"Content-Type":"application/json",Authorization:"Bearer sk-38ff2a6b9f754b059fa42839d5a4b426"}});if(200!==o.statusCode||!o.data)throw new Error(`API 请求失败: ${o.statusCode}`);try{const r=o.data.choices[0].message.content.replace(/```json\n|\n```/g,"").trim();let s;t("log","at pages/index/index.vue:479","API 返回的原始数据:",r);try{s=JSON.parse(r)}catch(a){const e=r.match(/\[.*\]/);if(!e)throw new Error("无法解析返回的 JSON 数据");s=JSON.parse(e[0])}if(Array.isArray(s)){const t=s.filter((t=>t.category_id&&t.target&&"number"==typeof t.amount&&e.includes(t.category_id)));if(t.length>0)return E(t),!0;throw new Error("API 返回的数据格式不正确：缺少必要字段或分类ID无效")}throw new Error("API 返回的数据不是数组格式")}catch(r){throw t("error","at pages/index/index.vue:514","JSON 解析错误:",r),new Error("API 返回数据格式不正确")}}catch(o){throw t("error","at pages/index/index.vue:521","智能输入处理失败:",o),uni.showToast({title:o.message||"处理失败，请重试",icon:"none",duration:3e3}),o}},E=e=>{const a=new Map;e.forEach((e=>{const r=b(e.category_id);if(!r)return void t("warn","at pages/index/index.vue:540",`未找到匹配的分类: ${e.category_id}`);const o=r.category_id;if(a.has(o)){const t=a.get(o);t.amount+=e.amount,t.target!==e.target&&(t.target=`${t.target}, ${e.target}`)}else a.set(o,{categoryId:o,target:e.target,amount:e.amount})})),a.forEach(((e,a)=>{const r=o.value.get(a)||{target:"",amount:""};if(r.amount){const t=parseFloat(r.amount)||0;e.amount+=t,r.target&&r.target!==e.target&&(e.target=`${r.target}, ${e.target}`)}r.target=e.target,r.amount=e.amount.toString(),o.value.set(a,r);const n=g(a);s.value.set(a,n);const l=v(a);t("log","at pages/index/index.vue:590",`已填充数据到分类 ${l?l.name:a}:`,r)}))},b=e=>(t=>{for(const a of t)if(a.children&&0!==a.children.length){if(a.children)for(const t of a.children)if(t.children&&0!==t.children.length){if(t.children)for(const a of t.children)if(a.name===e)return a}else if(t.name===e)return t}else if(a.name===e)return a;return null})(m.value);return e.onMounted((async()=>{if(t("log","at pages/index/index.vue:635","页面加载，开始初始化数据..."),t("log","at pages/index/index.vue:636","当前用户状态:",a.state.user),!a.state.user)return t("log","at pages/index/index.vue:639","用户未登录，跳转到登录页面"),void uni.reLaunch({url:"/pages/login/login"});uni.setNavigationBarTitle({title:`${a.state.user.username}的账本`}),await Promise.all([w(),f()]),t("log","at pages/index/index.vue:655","数据初始化完成")})),{store:a,categories:m,isLoading:r,currentInputs:o,tempTotals:s,calculateCategoryTotal:p,calculatePercentage:e=>{const t=h();return t>0?(e/t*100).toFixed(2):"0"},calculateTotalAmount:h,handleAmountInput:(e,a)=>{t("log","at pages/index/index.vue:227","Handling amount input:",{categoryId:e,value:a});const r=o.value.get(e)||{target:"",amount:""};r.amount=a,o.value.set(e,r);const n=g(e);s.value.set(e,n)},handleTargetInput:(e,a)=>{t("log","at pages/index/index.vue:239","Handling target input:",{categoryId:e,value:a});const r=o.value.get(e)||{target:"",amount:""};r.target=a,o.value.set(e,r)},saveExpenses:async()=>{const e=[];if(o.value.forEach(((t,a)=>{t.amount&&0!==parseFloat(t.amount)&&e.push({category_id:a,target:t.target||"",amount:parseFloat(t.amount)})})),0!==e.length){r.value=!0;try{const t=new Date,n=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0"),i=`${n}-${l}-${String(t.getDate()).padStart(2,"0")}`;await a.dispatch("saveExpense",{date:i,records:e})&&(o.value.clear(),s.value.clear(),await f(),uni.showToast({title:"保存成功",icon:"success"}))}catch(n){t("error","at pages/index/index.vue:305","保存支出失败:",n),uni.showToast({title:"保存失败，请重试",icon:"none"})}finally{r.value=!1}}else uni.showToast({title:"请至少输入一条支出记录",icon:"none"})},handleLogout:async()=>{try{r.value=!0,await a.dispatch("logout"),uni.navigateTo({url:"/pages/login/login"})}catch(e){uni.showToast({title:"退出失败，请重试",icon:"none"})}finally{r.value=!1}},goToDetails:()=>{uni.navigateTo({url:"/pages/details/details"})},goToSettings:()=>{t("log","at pages/index/index.vue:177","点击设置按钮"),uni.navigateTo({url:"/pages/settings/settings",success:()=>{t("log","at pages/index/index.vue:181","跳转成功")},fail:e=>{t("error","at pages/index/index.vue:184","跳转失败:",e)}})},earliestDate:i,showModal:c,isProcessing:u,aiInputText:d,showAiInputDialog:()=>{c.value=!0,d.value=""},closeModal:()=>{u.value||(c.value=!1,d.value="")},handleConfirm:async()=>{if(d.value.trim()){u.value=!0;try{await y(),c.value=!1,d.value=""}catch(e){t("error","at pages/index/index.vue:390","处理失败:",e)}finally{u.value=!1}}else uni.showToast({title:"请输入消费记录",icon:"none"})}}}},[["render",function(t,a,r,o,s,n){var l;return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("view",{class:"start-date"},"始于:"+e.toDisplayString(o.earliestDate||"暂无记录"),1),e.createElementVNode("image",{src:$,mode:"aspectFit",class:"logo"}),e.createElementVNode("text",{class:"title"},e.toDisplayString((null==(l=o.store.state.user)?void 0:l.username)||"未登录")+"的账本",1),e.createElementVNode("view",{class:"header-buttons"},[e.createElementVNode("view",{class:"header-button",onClick:a[0]||(a[0]=(...e)=>o.goToDetails&&o.goToDetails(...e))},"明细"),e.createElementVNode("view",{class:"header-button",onClick:a[1]||(a[1]=(...e)=>o.goToSettings&&o.goToSettings(...e))},"设置")])])]),e.createElementVNode("scroll-view",{"scroll-y":"",class:"expense-list"},[e.createElementVNode("view",{class:"expense-row header-row"},[e.createElementVNode("view",{class:"cell category-cell"},"分类"),e.createElementVNode("view",{class:"cell target-cell"},"标的"),e.createElementVNode("view",{class:"cell amount-cell"},"支出"),e.createElementVNode("view",{class:"cell total-cell"},"总额"),e.createElementVNode("view",{class:"cell percentage-cell"},"占比")]),e.createElementVNode("view",{class:"expense-row total-row"},[e.createElementVNode("view",{class:"cell category-cell"},"总支出"),e.createElementVNode("view",{class:"cell target-cell"},"-"),e.createElementVNode("view",{class:"cell amount-cell"},"-"),e.createElementVNode("view",{class:"cell total-cell"},e.toDisplayString(o.calculateTotalAmount().toFixed(2)),1),e.createElementVNode("view",{class:"cell percentage-cell"},"100%")]),e.createElementVNode("view",{class:"expense-content"},[o.categories&&o.categories.length?(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:0},e.renderList(o.categories,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:t.category_id,class:"category-group"},[e.createElementVNode("view",{class:"expense-row level-1"},[e.createElementVNode("view",{class:"cell category-cell"},e.toDisplayString(a+1+". "+t.name),1),e.createElementVNode("view",{class:"cell target-cell"},"-"),e.createElementVNode("view",{class:"cell amount-cell"},"-"),e.createElementVNode("view",{class:"cell total-cell"},e.toDisplayString(o.calculateCategoryTotal(t).toFixed(2)),1),e.createElementVNode("view",{class:"cell percentage-cell"},e.toDisplayString(o.calculatePercentage(o.calculateCategoryTotal(t)))+"%",1)]),t.children&&t.children.length?(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:0},e.renderList(t.children,((t,r)=>(e.openBlock(),e.createElementBlock("view",{key:t.category_id,class:"category-group"},[e.createElementVNode("view",{class:"expense-row level-2"},[e.createElementVNode("view",{class:"cell category-cell"},e.toDisplayString(a+1+"."+(r+1)+" "+t.name),1),e.createElementVNode("view",{class:"cell target-cell"},"-"),e.createElementVNode("view",{class:"cell amount-cell"},"-"),e.createElementVNode("view",{class:"cell total-cell"},e.toDisplayString(o.calculateCategoryTotal(t).toFixed(2)),1),e.createElementVNode("view",{class:"cell percentage-cell"},e.toDisplayString(o.calculatePercentage(o.calculateCategoryTotal(t)))+"%",1)]),t.children&&t.children.length?(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:0},e.renderList(t.children,((t,a)=>{var r,s;return e.openBlock(),e.createElementBlock("view",{key:t.category_id,class:"expense-row level-3"},[e.createElementVNode("view",{class:"cell category-cell"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"cell target-cell"},[e.createElementVNode("input",{type:"text",value:(null==(r=o.currentInputs.get(t.category_id))?void 0:r.target)||"",onInput:e=>o.handleTargetInput(t.category_id,e.detail.value),class:"input-field",placeholder:"输入标的"},null,40,["value","onInput"])]),e.createElementVNode("view",{class:"cell amount-cell"},[e.createElementVNode("input",{type:"digit",value:(null==(s=o.currentInputs.get(t.category_id))?void 0:s.amount)||"",onInput:e=>o.handleAmountInput(t.category_id,e.detail.value),class:"input-field",placeholder:"输入金额"},null,40,["value","onInput"])]),e.createElementVNode("view",{class:"cell total-cell"},e.toDisplayString((o.tempTotals.get(t.category_id)||t.total_amount||0).toFixed(2)),1),e.createElementVNode("view",{class:"cell percentage-cell"},e.toDisplayString(o.calculatePercentage(o.tempTotals.get(t.category_id)||t.total_amount||0))+"%",1)])})),128)):e.createCommentVNode("",!0)])))),128)):e.createCommentVNode("",!0)])))),128)):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"footer"},[e.createElementVNode("view",{class:"footer-content"},[e.createElementVNode("view",{class:"tip-text"},"提示：可输入负数对冲错误记录"),e.createElementVNode("view",{class:"button-row"},[e.createElementVNode("button",{class:"ai-input-button",onClick:a[2]||(a[2]=(...e)=>o.showAiInputDialog&&o.showAiInputDialog(...e))},"智能输入"),e.createElementVNode("button",{class:"save-button",onClick:a[3]||(a[3]=(...e)=>o.saveExpenses&&o.saveExpenses(...e)),disabled:o.isLoading},e.toDisplayString(o.isLoading?"保存中...":"保存记录"),9,["disabled"])])])]),o.showModal?(e.openBlock(),e.createElementBlock("view",{key:0,class:"ai-input-modal"},[e.createElementVNode("view",{class:"modal-mask",onClick:a[4]||(a[4]=(...e)=>o.closeModal&&o.closeModal(...e))}),e.createElementVNode("view",{class:"modal-content"},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"智能输入"),e.createElementVNode("text",{class:"modal-close",onClick:a[5]||(a[5]=(...e)=>o.closeModal&&o.closeModal(...e))},"×")]),e.createElementVNode("view",{class:"modal-body"},[e.withDirectives(e.createElementVNode("textarea",{class:"ai-input-textarea","onUpdate:modelValue":a[6]||(a[6]=e=>o.aiInputText=e),placeholder:"请输入消费记录，例如：今天在超市买了水果花了50元，在餐厅吃饭花了100元"},null,512),[[e.vModelText,o.aiInputText]])]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-button cancel",onClick:a[7]||(a[7]=(...e)=>o.closeModal&&o.closeModal(...e))},"取消"),e.createElementVNode("button",{class:"modal-button confirm",onClick:a[8]||(a[8]=(...e)=>o.handleConfirm&&o.handleConfirm(...e)),disabled:o.isProcessing},e.toDisplayString(o.isProcessing?"处理中...":"确定"),9,["disabled"])])])])):e.createCommentVNode("",!0)])}]]);const W=G({setup(){const a=l(),r=e.ref(""),o=e.ref(""),s=e.ref(!1),n=e.ref({username:"",password:""}),i=()=>{r.value?r.value.length<3?n.value.username="用户名至少需要3个字符":n.value.username="":n.value.username="用户名不能为空"},c=()=>{o.value?o.value.length<6?n.value.password="密码至少需要6个字符":n.value.password="":n.value.password="密码不能为空"},u=e.computed((()=>Object.values(n.value).some((e=>""!==e))||!r.value||!o.value));return{username:r,password:o,isLoading:s,errors:n,hasErrors:u,handleLogin:async()=>{if(i(),c(),!u.value){s.value=!0;try{await a.dispatch("login",{username:r.value,password:o.value}),uni.showToast({title:"登录成功",icon:"success"}),uni.reLaunch({url:"/pages/index/index"})}catch(e){t("error","at pages/login/login.vue:122","登录失败，完整错误:",e);let a="登录失败";e.message.includes("用户不存在")?(a="用户名不存在",n.value.username=a):e.message.includes("密码错误")&&(a="密码错误",n.value.password=a),uni.showToast({title:a,icon:"none",duration:2e3})}finally{s.value=!1}}},goToRegister:()=>{uni.navigateTo({url:"/pages/register/register"})},goToForgotPassword:()=>{uni.navigateTo({url:"/pages/forgot-password/forgot-password"})},validateUsername:i,validatePassword:c}}},[["render",function(t,a,r,o,s,n){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("image",{src:$,mode:"aspectFit",class:"logo"}),e.createElementVNode("text",{class:"title"},"账户登录")])]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"form-content"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"用户名"),e.withDirectives(e.createElementVNode("input",{type:"text","onUpdate:modelValue":a[0]||(a[0]=e=>o.username=e),placeholder:"请输入用户名",class:"input-field",onBlur:a[1]||(a[1]=(...e)=>o.validateUsername&&o.validateUsername(...e))},null,544),[[e.vModelText,o.username]]),o.errors.username?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(o.errors.username),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"密码"),e.withDirectives(e.createElementVNode("input",{type:"password","onUpdate:modelValue":a[2]||(a[2]=e=>o.password=e),placeholder:"请输入密码",class:"input-field",onBlur:a[3]||(a[3]=(...e)=>o.validatePassword&&o.validatePassword(...e))},null,544),[[e.vModelText,o.password]]),o.errors.password?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(o.errors.password),1)):e.createCommentVNode("",!0)]),e.createElementVNode("button",{class:"submit-button",onClick:a[4]||(a[4]=(...e)=>o.handleLogin&&o.handleLogin(...e)),disabled:o.isLoading||o.hasErrors},e.toDisplayString(o.isLoading?"登录中...":"登录"),9,["disabled"]),e.createElementVNode("view",{class:"links-container"},[e.createElementVNode("view",{class:"register-link",onClick:a[5]||(a[5]=(...e)=>o.goToRegister&&o.goToRegister(...e))}," 还没有账号？点击注册 "),e.createElementVNode("view",{class:"forgot-password-link",onClick:a[6]||(a[6]=(...e)=>o.goToForgotPassword&&o.goToForgotPassword(...e))}," 忘记密码？ ")])])])])}]]);const K=G({setup(){const a=l(),r=e.ref(""),o=e.ref(""),s=e.ref(""),n=e.ref(""),i=e.ref(!1),c=e.ref({username:"",password:"",confirmPassword:"",email:""}),u=()=>{r.value?r.value.length<3?c.value.username="用户名至少需要3个字符":c.value.username="":c.value.username="用户名不能为空"},d=()=>{o.value?o.value.length<6?c.value.password="密码至少需要6个字符":c.value.password="":c.value.password="密码不能为空",s.value&&m()},m=()=>{s.value?s.value!==o.value?c.value.confirmPassword="两次输入的密码不一致":c.value.confirmPassword="":c.value.confirmPassword="请确认密码"},g=()=>{n.value?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n.value)?c.value.email="":c.value.email="请输入有效的邮箱地址":c.value.email="邮箱不能为空"},p=e.computed((()=>Object.values(c.value).some((e=>""!==e))||!r.value||!o.value||!s.value||!n.value));return{username:r,password:o,confirmPassword:s,email:n,isLoading:i,errors:c,hasErrors:p,handleRegister:async()=>{if(u(),d(),m(),g(),!p.value){i.value=!0;try{await a.dispatch("register",{username:r.value,password:o.value,email:n.value}),uni.showToast({title:"注册成功",icon:"success"}),setTimeout((()=>{uni.redirectTo({url:"/pages/login/login"})}),1500)}catch(e){t("error","at pages/register/register.vue:180","注册失败，完整错误:",e);let a="注册失败";e.message.includes("用户名已存在")?(a="用户名已被注册",c.value.username=a):e.message.includes("邮箱已存在")&&(a="邮箱已被注册",c.value.email=a),uni.showToast({title:a,icon:"none",duration:2e3})}finally{i.value=!1}}},goToLogin:()=>{uni.navigateTo({url:"/pages/login/login"})},validateUsername:u,validatePassword:d,validateConfirmPassword:m,validateEmail:g}}},[["render",function(t,a,r,o,s,n){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("image",{src:$,mode:"aspectFit",class:"logo"}),e.createElementVNode("text",{class:"title"},"账户注册")])]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"form-content"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"用户名"),e.withDirectives(e.createElementVNode("input",{type:"text","onUpdate:modelValue":a[0]||(a[0]=e=>o.username=e),placeholder:"请输入用户名（至少3个字符）",class:"input-field",onBlur:a[1]||(a[1]=(...e)=>o.validateUsername&&o.validateUsername(...e))},null,544),[[e.vModelText,o.username]]),o.errors.username?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(o.errors.username),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"密码"),e.withDirectives(e.createElementVNode("input",{type:"password","onUpdate:modelValue":a[2]||(a[2]=e=>o.password=e),placeholder:"请输入密码（至少6个字符）",class:"input-field",onBlur:a[3]||(a[3]=(...e)=>o.validatePassword&&o.validatePassword(...e))},null,544),[[e.vModelText,o.password]]),o.errors.password?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(o.errors.password),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"确认密码"),e.withDirectives(e.createElementVNode("input",{type:"password","onUpdate:modelValue":a[4]||(a[4]=e=>o.confirmPassword=e),placeholder:"请再次输入密码",class:"input-field",onBlur:a[5]||(a[5]=(...e)=>o.validateConfirmPassword&&o.validateConfirmPassword(...e))},null,544),[[e.vModelText,o.confirmPassword]]),o.errors.confirmPassword?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(o.errors.confirmPassword),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"邮箱"),e.withDirectives(e.createElementVNode("input",{type:"text","onUpdate:modelValue":a[6]||(a[6]=e=>o.email=e),placeholder:"请输入有效的邮箱地址",class:"input-field",onBlur:a[7]||(a[7]=(...e)=>o.validateEmail&&o.validateEmail(...e))},null,544),[[e.vModelText,o.email]]),o.errors.email?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(o.errors.email),1)):e.createCommentVNode("",!0)]),e.createElementVNode("button",{class:"submit-button",onClick:a[8]||(a[8]=(...e)=>o.handleRegister&&o.handleRegister(...e)),disabled:o.isLoading||o.hasErrors},e.toDisplayString(o.isLoading?"注册中...":"注册"),9,["disabled"]),e.createElementVNode("view",{class:"login-link",onClick:a[9]||(a[9]=(...e)=>o.goToLogin&&o.goToLogin(...e))}," 已有账号？点击登录 ")])])])}]]);const J=G({setup(){const a=l(),r=e.ref(""),o=e.ref(""),s=e.ref(""),n=e.ref(""),i=e.ref(!1),c=e.ref(1),u=e.ref({username:"",email:"",newPassword:"",confirmPassword:""}),d=()=>{r.value?r.value.length<3?u.value.username="用户名至少需要3个字符":u.value.username="":u.value.username="用户名不能为空"},m=()=>{o.value?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o.value)?u.value.email="":u.value.email="请输入有效的邮箱地址":u.value.email="邮箱不能为空"},g=()=>{s.value?s.value.length<6?u.value.newPassword="新密码至少需要6个字符":u.value.newPassword="":u.value.newPassword="新密码不能为空"},p=()=>{n.value?n.value!==s.value?u.value.confirmPassword="两次输入的密码不一致":u.value.confirmPassword="":u.value.confirmPassword="确认密码不能为空"},h=e.computed((()=>1===c.value?Object.values(u.value).some((e=>""!==e))||!r.value||!o.value:Object.values(u.value).some((e=>""!==e))||!r.value||!o.value||!s.value||!n.value));return{username:r,email:o,newPassword:s,confirmPassword:n,isLoading:i,step:c,errors:u,hasErrors:h,handleVerify:async()=>{if(d(),m(),!h.value){i.value=!0;try{(await a.dispatch("verifyUserEmail",{username:r.value,email:o.value})).success&&(c.value=2,uni.showToast({title:"身份验证成功",icon:"success"}))}catch(e){t("error","at pages/forgot-password/forgot-password.vue:185","身份验证失败:",e);let a="身份验证失败";e.message.includes("用户不存在")?(a="用户名不存在",u.value.username=a):e.message.includes("邮箱不匹配")&&(a="邮箱与用户名不匹配",u.value.email=a),uni.showToast({title:a,icon:"none",duration:2e3})}finally{i.value=!1}}},handleResetPassword:async()=>{if(d(),m(),g(),p(),!h.value){i.value=!0;try{await a.dispatch("resetPassword",{username:r.value,email:o.value,newPassword:s.value}),uni.showToast({title:"密码重置成功",icon:"success"}),setTimeout((()=>{uni.reLaunch({url:"/pages/login/login"})}),1500)}catch(e){t("error","at pages/forgot-password/forgot-password.vue:236","密码重置失败:",e),uni.showToast({title:e.message||"密码重置失败，请重试",icon:"none",duration:2e3})}finally{i.value=!1}}},goToLogin:()=>{uni.navigateTo({url:"/pages/login/login"})},validateUsername:d,validateEmail:m,validateNewPassword:g,validateConfirmPassword:p}}},[["render",function(t,a,r,o,s,n){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("image",{src:$,mode:"aspectFit",class:"logo"}),e.createElementVNode("text",{class:"title"},"重置密码")])]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"form-content"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"用户名"),e.withDirectives(e.createElementVNode("input",{type:"text","onUpdate:modelValue":a[0]||(a[0]=e=>o.username=e),placeholder:"请输入用户名",class:"input-field",onBlur:a[1]||(a[1]=(...e)=>o.validateUsername&&o.validateUsername(...e))},null,544),[[e.vModelText,o.username]]),o.errors.username?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(o.errors.username),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"邮箱"),e.withDirectives(e.createElementVNode("input",{type:"text","onUpdate:modelValue":a[2]||(a[2]=e=>o.email=e),placeholder:"请输入注册时使用的邮箱",class:"input-field",onBlur:a[3]||(a[3]=(...e)=>o.validateEmail&&o.validateEmail(...e))},null,544),[[e.vModelText,o.email]]),o.errors.email?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(o.errors.email),1)):e.createCommentVNode("",!0)]),1===o.step?(e.openBlock(),e.createElementBlock("view",{key:0,class:"form-item"},[e.createElementVNode("button",{class:"submit-button",onClick:a[4]||(a[4]=(...e)=>o.handleVerify&&o.handleVerify(...e)),disabled:o.isLoading||o.hasErrors},e.toDisplayString(o.isLoading?"验证中...":"验证身份"),9,["disabled"])])):e.createCommentVNode("",!0),2===o.step?(e.openBlock(),e.createElementBlock("view",{key:1},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"新密码"),e.withDirectives(e.createElementVNode("input",{type:"password","onUpdate:modelValue":a[5]||(a[5]=e=>o.newPassword=e),placeholder:"请输入新密码",class:"input-field",onBlur:a[6]||(a[6]=(...e)=>o.validateNewPassword&&o.validateNewPassword(...e))},null,544),[[e.vModelText,o.newPassword]]),o.errors.newPassword?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(o.errors.newPassword),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"确认密码"),e.withDirectives(e.createElementVNode("input",{type:"password","onUpdate:modelValue":a[7]||(a[7]=e=>o.confirmPassword=e),placeholder:"请再次输入新密码",class:"input-field",onBlur:a[8]||(a[8]=(...e)=>o.validateConfirmPassword&&o.validateConfirmPassword(...e))},null,544),[[e.vModelText,o.confirmPassword]]),o.errors.confirmPassword?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(o.errors.confirmPassword),1)):e.createCommentVNode("",!0)]),e.createElementVNode("button",{class:"submit-button",onClick:a[9]||(a[9]=(...e)=>o.handleResetPassword&&o.handleResetPassword(...e)),disabled:o.isLoading||o.hasErrors},e.toDisplayString(o.isLoading?"重置中...":"重置密码"),9,["disabled"])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"back-link",onClick:a[10]||(a[10]=(...e)=>o.goToLogin&&o.goToLogin(...e))}," 返回登录 ")])])])}]]),Q={setup(){const a=l(),r=e.ref(""),o=e.ref(""),s=e.ref(null),n=e.ref(""),i=e.ref([]),c=e.ref([]),u=e.ref(!1),d=()=>{const e=a.state.categories,t=[];e.forEach((e=>{e.children&&e.children.forEach((a=>{a.children&&a.children.forEach((r=>{t.push({id:r.category_id,name:`${e.name} / ${a.name} / ${r.name}`})}))}))})),c.value=[{id:"",name:"全部"},...t]},m=async()=>{var e;try{let t=["e.user_id = ?"],l=[a.state.user.user_id];r.value&&(t.push("e.record_date >= ?"),l.push(r.value)),o.value&&(t.push("e.record_date <= ?"),l.push(o.value)),(null==(e=s.value)?void 0:e.id)&&(t.push("e.category_id = ?"),l.push(s.value.id)),n.value&&(t.push("e.target LIKE ?"),l.push(`%${n.value}%`));const c=`\n          SELECT\n            e.*,\n            c1.name as level1_name,\n            c2.name as level2_name,\n            c3.name as level3_name\n          FROM expenses e\n          LEFT JOIN categories c3 ON e.category_id = c3.category_id\n          LEFT JOIN categories c2 ON c3.parent_id = c2.category_id\n          LEFT JOIN categories c1 ON c2.parent_id = c1.category_id\n          WHERE ${t.join(" AND ")}\n          ORDER BY e.record_date DESC, e.created_at DESC\n          LIMIT 40\n        `,u=await F.selectSql(c,l);i.value=u.map((e=>({...e,created_at:e.record_date,amount:parseFloat(e.amount),category_name:e.level3_name||"未知分类"})))}catch(l){t("error","at pages/details/details.vue:165","获取数据失败:",l),uni.showToast({title:"获取数据失败",icon:"none"})}};return e.onMounted((async()=>{a.state.user?await Promise.all([a.dispatch("fetchCategories"),d(),m()]):uni.reLaunch({url:"/pages/login/login"})})),{startDate:r,endDate:o,selectedCategory:s,targetFilter:n,expenses:i,categoryOptions:c,isLoading:u,formatDate:e=>{const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`},onStartDateChange:e=>{r.value=e.detail.value},onEndDateChange:e=>{o.value=e.detail.value},onCategoryChange:e=>{s.value=c.value[e.detail.value]},applyFilters:()=>{m()}}}};const X=G(Q,[["render",function(t,a,r,o,s,n){var l;return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("image",{src:$,mode:"aspectFit",class:"logo"}),e.createElementVNode("text",{class:"title"},"支出明细")])]),e.createElementVNode("view",{class:"filter-section"},[e.createElementVNode("view",{class:"filter-item"},[e.createElementVNode("text",{class:"filter-label"},"日期区间"),e.createElementVNode("view",{class:"date-range"},[e.createElementVNode("picker",{mode:"date",value:o.startDate,onChange:a[0]||(a[0]=(...e)=>o.onStartDateChange&&o.onStartDateChange(...e))},[e.createElementVNode("view",{class:"picker-item"},e.toDisplayString(o.startDate||"开始日期"),1)],40,["value"]),e.createElementVNode("text",{class:"date-separator"},"至"),e.createElementVNode("picker",{mode:"date",value:o.endDate,onChange:a[1]||(a[1]=(...e)=>o.onEndDateChange&&o.onEndDateChange(...e))},[e.createElementVNode("view",{class:"picker-item"},e.toDisplayString(o.endDate||"结束日期"),1)],40,["value"])])]),e.createElementVNode("view",{class:"filter-item"},[e.createElementVNode("text",{class:"filter-label"},"三级分类"),e.createElementVNode("picker",{range:o.categoryOptions,"range-key":"name",onChange:a[2]||(a[2]=(...e)=>o.onCategoryChange&&o.onCategoryChange(...e))},[e.createElementVNode("view",{class:"picker-item"},e.toDisplayString((null==(l=o.selectedCategory)?void 0:l.name)||"全部"),1)],40,["range"])]),e.createElementVNode("view",{class:"filter-item"},[e.createElementVNode("text",{class:"filter-label"},"标的"),e.withDirectives(e.createElementVNode("input",{type:"text","onUpdate:modelValue":a[3]||(a[3]=e=>o.targetFilter=e),placeholder:"请输入标的",class:"filter-input"},null,512),[[e.vModelText,o.targetFilter]])]),e.createElementVNode("button",{class:"filter-button",onClick:a[4]||(a[4]=(...e)=>o.applyFilters&&o.applyFilters(...e))},"筛选")]),e.createElementVNode("scroll-view",{"scroll-y":"",class:"table-container"},[e.createElementVNode("view",{class:"table"},[e.createElementVNode("view",{class:"table-header"},[e.createElementVNode("view",{class:"table-cell date-cell"},"时间"),e.createElementVNode("view",{class:"table-cell category-cell"},"分类"),e.createElementVNode("view",{class:"table-cell target-cell"},"标的"),e.createElementVNode("view",{class:"table-cell amount-cell"},"支出")]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.expenses,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:"table-row"},[e.createElementVNode("view",{class:"table-cell date-cell"},e.toDisplayString(o.formatDate(t.created_at)),1),e.createElementVNode("view",{class:"table-cell category-cell"},e.toDisplayString(t.category_name),1),e.createElementVNode("view",{class:"table-cell target-cell"},e.toDisplayString(t.target),1),e.createElementVNode("view",{class:"table-cell amount-cell"},e.toDisplayString(t.amount.toFixed(2)),1)])))),128)),0===o.expenses.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-state"}," 暂无数据 ")):e.createCommentVNode("",!0)])])])}]]);const Y=G({setup(){const a=l(),r=e.ref(!1);return{goToChangePassword:()=>{uni.navigateTo({url:"/pages/change-password/change-password"})},handleBackup:async()=>{try{r.value=!0;const a=`myaccount_backup_${(new Date).toISOString().slice(0,10)}.db`;t("log","at pages/settings/settings.vue:68","准备备份数据，文件名:",a);try{t("log","at pages/settings/settings.vue:71","开始请求存储权限"),await new Promise(((e,a)=>{plus.android.requestPermissions(["android.permission.WRITE_EXTERNAL_STORAGE"],(function(r){t("log","at pages/settings/settings.vue:78","权限请求结果:",r),1===r.granted.length?e():a(new Error("未授予存储权限"))}),(function(e){t("error","at pages/settings/settings.vue:86","权限请求失败:",e),a(e)}))})),t("log","at pages/settings/settings.vue:92","已获得存储权限，准备导出数据");const e=plus.android.importClass("android.os.Environment"),o=plus.android.importClass("java.io.File"),s=(plus.android.runtimeMainActivity(),e.getExternalStorageDirectory().getAbsolutePath()+"/Download/"+a);t("log","at pages/settings/settings.vue:104","使用外部存储路径:",s);const n=new o(s).getParentFile();n.exists()||n.mkdirs(),t("log","at pages/settings/settings.vue:113","使用直接复制方法备份数据库到:",s);if(!(await F.backupDatabaseByFileCopy(s)>0))throw new Error("文件复制失败，复制了 0 字节");await new Promise((e=>{uni.showModal({title:"备份成功",content:`数据已备份到以下路径：\n${s}`,showCancel:!1,success:()=>{e()}})})),uni.showToast({title:"备份成功",icon:"success",duration:2e3})}catch(e){t("error","at pages/settings/settings.vue:139","备份过程出错:",e),uni.showToast({title:"备份失败: "+e.message,icon:"none",duration:2e3})}finally{r.value=!1}}catch(e){t("error","at pages/settings/settings.vue:149","备份操作失败:",e),uni.showToast({title:"备份失败，请重试",icon:"none",duration:2e3}),r.value=!1}},goToContact:()=>{uni.navigateTo({url:"/pages/contact/contact"})},goToApiSettings:()=>{uni.navigateTo({url:"/pages/api-settings/api-settings"})},handleLogout:async()=>{try{r.value=!0,await a.dispatch("logout"),uni.reLaunch({url:"/pages/login/login"})}catch(e){uni.showToast({title:"退出失败，请重试",icon:"none"})}finally{r.value=!1}},goToCategoryManagement:()=>{uni.navigateTo({url:"/pages/category-management/category-management"})},handleClearData:async()=>{uni.showModal({title:"警告",content:"此操作将清空所有支出记录，但保留用户账户和分类信息。\n\n清空前将自动备份数据到下载文件夹。\n\n此操作不可恢复，确定要继续吗？",confirmText:"确定清空",confirmColor:"#f56c6c",cancelText:"取消",success:async e=>{if(e.confirm)try{r.value=!0,uni.showModal({title:"再次确认",content:"您真的要清空所有支出记录吗？此操作不可恢复！",confirmText:"确定清空",confirmColor:"#f56c6c",cancelText:"取消",success:async e=>{if(e.confirm)try{const e=await a.dispatch("clearUserData");if(!e||!e.success)throw new Error("清空操作失败");uni.showModal({title:"清空成功",content:`所有支出记录已清空。\n\n数据备份已保存到下载文件夹：${e.backupFile}`,showCancel:!1,success:()=>{uni.switchTab({url:"/pages/index/index"})}})}catch(o){t("error","at pages/settings/settings.vue:241","清空数据失败:",o),uni.showToast({title:o.message||"清空失败，请重试",icon:"none",duration:2e3})}finally{r.value=!1}else r.value=!1}})}catch(o){t("error","at pages/settings/settings.vue:256","清空数据失败:",o),uni.showToast({title:"操作失败，请重试",icon:"none",duration:2e3}),r.value=!1}}})}}}},[["render",function(t,a,r,o,s,n){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("text",{class:"title"},"设置")])]),e.createElementVNode("view",{class:"settings-list"},[e.createElementVNode("view",{class:"settings-item",onClick:a[0]||(a[0]=(...e)=>o.goToCategoryManagement&&o.goToCategoryManagement(...e))},[e.createElementVNode("text",{class:"item-label"},"分类管理"),e.createElementVNode("text",{class:"item-arrow"},">")]),e.createElementVNode("view",{class:"settings-item",onClick:a[1]||(a[1]=(...e)=>o.goToChangePassword&&o.goToChangePassword(...e))},[e.createElementVNode("text",{class:"item-label"},"修改密码"),e.createElementVNode("text",{class:"item-arrow"},">")]),e.createElementVNode("view",{class:"settings-item",onClick:a[2]||(a[2]=(...e)=>o.handleBackup&&o.handleBackup(...e))},[e.createElementVNode("text",{class:"item-label"},"备份数据库"),e.createElementVNode("text",{class:"item-arrow"},">")]),e.createElementVNode("view",{class:"settings-item danger",onClick:a[3]||(a[3]=(...e)=>o.handleClearData&&o.handleClearData(...e))},[e.createElementVNode("text",{class:"item-label danger-text"},"清空数据"),e.createElementVNode("text",{class:"item-arrow"},">")]),e.createElementVNode("view",{class:"settings-item",onClick:a[4]||(a[4]=(...e)=>o.goToApiSettings&&o.goToApiSettings(...e))},[e.createElementVNode("text",{class:"item-label"},"DeepSeek API 设置"),e.createElementVNode("text",{class:"item-arrow"},">")]),e.createElementVNode("view",{class:"settings-item",onClick:a[5]||(a[5]=(...e)=>o.goToContact&&o.goToContact(...e))},[e.createElementVNode("text",{class:"item-label"},"联系我们"),e.createElementVNode("text",{class:"item-arrow"},">")]),e.createElementVNode("view",{class:"settings-item",onClick:a[6]||(a[6]=(...e)=>o.handleLogout&&o.handleLogout(...e))},[e.createElementVNode("text",{class:"item-label"},"退出账户"),e.createElementVNode("text",{class:"item-arrow"},">")])])])}]]);const z=G({setup(){const t=l(),a=e.ref(""),r=e.ref(""),o=e.ref(""),s=e.ref(!1);return{oldPassword:a,newPassword:r,confirmPassword:o,isLoading:s,handleSubmit:async()=>{if(a.value&&r.value&&o.value)if(r.value===o.value)if(r.value.length<6)uni.showToast({title:"新密码长度不能少于6位",icon:"none"});else{s.value=!0;try{await t.dispatch("changePassword",{oldPassword:a.value,newPassword:r.value})&&(uni.showToast({title:"密码修改成功",icon:"success"}),setTimeout((()=>{uni.navigateBack()}),1500))}catch(e){uni.showToast({title:e.message||"密码修改失败",icon:"none"})}finally{s.value=!1}}else uni.showToast({title:"两次输入的新密码不一致",icon:"none"});else uni.showToast({title:"请填写完整信息",icon:"none"})}}}},[["render",function(t,a,r,o,s,n){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("text",{class:"title"},"修改密码")])]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"input-label"},"旧密码"),e.withDirectives(e.createElementVNode("input",{type:"password","onUpdate:modelValue":a[0]||(a[0]=e=>o.oldPassword=e),class:"input-field",placeholder:"请输入旧密码"},null,512),[[e.vModelText,o.oldPassword]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"input-label"},"新密码"),e.withDirectives(e.createElementVNode("input",{type:"password","onUpdate:modelValue":a[1]||(a[1]=e=>o.newPassword=e),class:"input-field",placeholder:"请输入新密码"},null,512),[[e.vModelText,o.newPassword]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"input-label"},"确认密码"),e.withDirectives(e.createElementVNode("input",{type:"password","onUpdate:modelValue":a[2]||(a[2]=e=>o.confirmPassword=e),class:"input-field",placeholder:"请再次输入新密码"},null,512),[[e.vModelText,o.confirmPassword]])]),e.createElementVNode("button",{class:"submit-button",onClick:a[3]||(a[3]=(...e)=>o.handleSubmit&&o.handleSubmit(...e)),disabled:o.isLoading},e.toDisplayString(o.isLoading?"提交中...":"确认修改"),9,["disabled"])])])}]]);const Z=G({setup:()=>({copyEmail:()=>{uni.setClipboardData({data:"<EMAIL>",success:()=>{uni.showToast({title:"邮箱已复制",icon:"success"})}})},previewImage:()=>{uni.previewImage({urls:["/static/weixin.png"]})}})},[["render",function(t,a,r,o,s,n){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("text",{class:"title"},"联系我们")])]),e.createElementVNode("view",{class:"contact-list"},[e.createElementVNode("view",{class:"contact-item"},[e.createElementVNode("text",{class:"contact-label"},"QQ邮箱"),e.createElementVNode("text",{class:"contact-value",onClick:a[0]||(a[0]=(...e)=>o.copyEmail&&o.copyEmail(...e))},"<EMAIL>")]),e.createElementVNode("view",{class:"contact-item"},[e.createElementVNode("text",{class:"contact-label"},"微信"),e.createElementVNode("image",{src:"/static/weixin.png",mode:"aspectFit",class:"qr-code",onClick:a[1]||(a[1]=(...e)=>o.previewImage&&o.previewImage(...e))})]),e.createElementVNode("view",{class:"contact-item"},[e.createElementVNode("text",{class:"contact-label"},"使用说明"),e.createElementVNode("view",{class:"user-guide"},[e.createElementVNode("view",{class:"guide-section"},[e.createElementVNode("text",{class:"guide-title"},"应用简介"),e.createElementVNode("text",{class:"guide-content"},"本应用是一个完全本地化的记账应用，所有数据存储在您的手机上，不会上传到云端，保证您的数据安全和隐私。")]),e.createElementVNode("view",{class:"guide-section"},[e.createElementVNode("text",{class:"guide-title"},"主要功能"),e.createElementVNode("view",{class:"guide-list"},[e.createElementVNode("text",{class:"guide-item"},"1. 支出记录：在首页输入支出金额和标的，可输入负数对冲错误记录"),e.createElementVNode("text",{class:"guide-item"},"2. 智能输入：点击“智能输入”按钮，输入自然语言描述的消费记录"),e.createElementVNode("text",{class:"guide-item"},"3. 明细查询：在明细页面查看所有支出记录，可按日期和分类筛选"),e.createElementVNode("text",{class:"guide-item"},"4. 分类管理：在设置页面中管理自定义分类"),e.createElementVNode("text",{class:"guide-item"},"5. 数据备份：在设置页面中备份数据到手机存储")])]),e.createElementVNode("view",{class:"guide-section"},[e.createElementVNode("text",{class:"guide-title"},"注意事项"),e.createElementVNode("view",{class:"guide-list"},[e.createElementVNode("text",{class:"guide-item"},"1. 定期备份数据，防止意外丢失"),e.createElementVNode("text",{class:"guide-item"},"2. 清空数据前会自动备份，但仍请谨慎操作"),e.createElementVNode("text",{class:"guide-item"},"3. 如需导入备份数据，请联系开发者"),e.createElementVNode("text",{class:"guide-item"},"4. 如忘记密码，可在登录页面点击“忘记密码”，通过注册邮箱重置"),e.createElementVNode("text",{class:"guide-item"},"5. 如发现问题或有功能建议，请通过上方联系方式反馈")])]),e.createElementVNode("view",{class:"guide-section"},[e.createElementVNode("text",{class:"guide-title"},"隐私声明"),e.createElementVNode("text",{class:"guide-content"},"本应用不会收集或上传您的个人数据，所有数据仅存储在您的设备上。智能输入功能会将您输入的文本发送到AI服务器进行处理，但不会存储您的数据。")])])])])])}]]);const ee=G({setup(){const t=l(),a=e.ref({categoryId:"",name:""}),r=e.ref({parentId:null,level:1,name:""}),o=e.ref(!1),s=e.ref(!1),n=e.ref(!1),i=e.computed((()=>t.state.categories)),c=()=>{s.value=!1},u=()=>{n.value=!1},d=e=>{const t=a=>{for(const r of a){if(r.category_id===e)return r;if(r.children&&r.children.length>0){const e=t(r.children);if(e)return e}}return null};return t(i.value)};return e.onMounted((async()=>{t.state.user?await t.dispatch("fetchCategories"):uni.reLaunch({url:"/pages/login/login"})})),{categories:i,editForm:a,addForm:r,isLoading:o,showEditModal:s,showAddModal:n,showEditDialog:e=>{a.value={categoryId:e.category_id,name:e.name},s.value=!0},closeEditDialog:c,handleEditConfirm:async()=>{try{if(!a.value.name.trim())return void uni.showToast({title:"分类名称不能为空",icon:"none"});await t.dispatch("updateCategory",{categoryId:a.value.categoryId,name:a.value.name.trim()}),c(),await t.dispatch("fetchCategories"),uni.showToast({title:"修改成功",icon:"success"})}catch(e){uni.showToast({title:e.message||"修改失败",icon:"none"})}},showAddDialog:(e,t)=>{r.value={parentId:e,level:t,name:""},n.value=!0},closeAddDialog:u,handleAddConfirm:async()=>{try{if(!r.value.name.trim())return void uni.showToast({title:"分类名称不能为空",icon:"none"});let e=1;if(r.value.parentId){const t=d(r.value.parentId);if(t&&t.children){e=Math.max(...t.children.map((e=>e.sort_order||0)),0)+1}}else{e=Math.max(...i.value.map((e=>e.sort_order||0)),0)+1}await t.dispatch("addCategory",{name:r.value.name.trim(),parentId:r.value.parentId,level:r.value.level,sortOrder:e}),u(),await t.dispatch("fetchCategories"),uni.showToast({title:"添加成功",icon:"success"})}catch(e){uni.showToast({title:e.message||"添加失败",icon:"none"})}},confirmDelete:e=>{uni.showModal({title:"确认删除",content:"删除分类后无法恢复，确定要删除吗？",success:async a=>{if(a.confirm)try{await t.dispatch("deleteCategory",e),await t.dispatch("fetchCategories"),uni.showToast({title:"删除成功",icon:"success"})}catch(r){uni.showToast({title:r.message||"删除失败",icon:"none"})}}})}}}},[["render",function(t,a,r,o,s,n){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("text",{class:"title"},"分类管理")])]),o.isLoading?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-container"},[e.createElementVNode("view",{class:"loading-spinner"}),e.createElementVNode("text",{class:"loading-text"},"加载中...")])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"category-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.categories,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:t.category_id,class:"category-item level-1"},[e.createElementVNode("view",{class:"category-info"},[e.createElementVNode("text",{class:"category-name"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"category-actions"},[e.createElementVNode("button",{class:"action-btn edit-btn",onClick:e=>o.showEditDialog(t)},"编辑",8,["onClick"]),e.createElementVNode("button",{class:"action-btn add-btn",onClick:e=>o.showAddDialog(t.category_id,2)},"添加子类",8,["onClick"]),e.createElementVNode("button",{class:"action-btn delete-btn",onClick:e=>o.confirmDelete(t.category_id)},"删除",8,["onClick"])])]),t.children&&t.children.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"subcategory-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.children,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.category_id,class:"category-item level-2"},[e.createElementVNode("view",{class:"category-info"},[e.createElementVNode("text",{class:"category-name"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"category-actions"},[e.createElementVNode("button",{class:"action-btn edit-btn",onClick:e=>o.showEditDialog(t)},"编辑",8,["onClick"]),e.createElementVNode("button",{class:"action-btn add-btn",onClick:e=>o.showAddDialog(t.category_id,3)},"添加子类",8,["onClick"]),e.createElementVNode("button",{class:"action-btn delete-btn",onClick:e=>o.confirmDelete(t.category_id)},"删除",8,["onClick"])])]),t.children&&t.children.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"subcategory-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.children,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.category_id,class:"category-item level-3"},[e.createElementVNode("view",{class:"category-info"},[e.createElementVNode("text",{class:"category-name"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"category-actions"},[e.createElementVNode("button",{class:"action-btn edit-btn",onClick:e=>o.showEditDialog(t)},"编辑",8,["onClick"]),e.createElementVNode("button",{class:"action-btn delete-btn",onClick:e=>o.confirmDelete(t.category_id)},"删除",8,["onClick"])])])])))),128))])):e.createCommentVNode("",!0)])))),128))])):e.createCommentVNode("",!0)])))),128))]),e.createElementVNode("view",{class:"add-category-btn",onClick:a[0]||(a[0]=e=>o.showAddDialog(null,1))},[e.createElementVNode("text",null,"添加一级分类")]),o.showEditModal?(e.openBlock(),e.createElementBlock("view",{key:1,class:"modal"},[e.createElementVNode("view",{class:"modal-mask",onClick:a[1]||(a[1]=(...e)=>o.closeEditDialog&&o.closeEditDialog(...e))}),e.createElementVNode("view",{class:"modal-content"},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"编辑分类"),e.createElementVNode("text",{class:"modal-close",onClick:a[2]||(a[2]=(...e)=>o.closeEditDialog&&o.closeEditDialog(...e))},"×")]),e.createElementVNode("view",{class:"modal-body"},[e.withDirectives(e.createElementVNode("input",{class:"modal-input","onUpdate:modelValue":a[3]||(a[3]=e=>o.editForm.name=e),placeholder:"请输入分类名称"},null,512),[[e.vModelText,o.editForm.name]])]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-button cancel",onClick:a[4]||(a[4]=(...e)=>o.closeEditDialog&&o.closeEditDialog(...e))},"取消"),e.createElementVNode("button",{class:"modal-button confirm",onClick:a[5]||(a[5]=(...e)=>o.handleEditConfirm&&o.handleEditConfirm(...e))},"确定")])])])):e.createCommentVNode("",!0),o.showAddModal?(e.openBlock(),e.createElementBlock("view",{key:2,class:"modal"},[e.createElementVNode("view",{class:"modal-mask",onClick:a[6]||(a[6]=(...e)=>o.closeAddDialog&&o.closeAddDialog(...e))}),e.createElementVNode("view",{class:"modal-content"},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"添加分类"),e.createElementVNode("text",{class:"modal-close",onClick:a[7]||(a[7]=(...e)=>o.closeAddDialog&&o.closeAddDialog(...e))},"×")]),e.createElementVNode("view",{class:"modal-body"},[e.withDirectives(e.createElementVNode("input",{class:"modal-input","onUpdate:modelValue":a[8]||(a[8]=e=>o.addForm.name=e),placeholder:"请输入分类名称"},null,512),[[e.vModelText,o.addForm.name]])]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-button cancel",onClick:a[9]||(a[9]=(...e)=>o.closeAddDialog&&o.closeAddDialog(...e))},"取消"),e.createElementVNode("button",{class:"modal-button confirm",onClick:a[10]||(a[10]=(...e)=>o.handleAddConfirm&&o.handleAddConfirm(...e))},"确定")])])])):e.createCommentVNode("",!0)])}]]);const te=G({setup(){const a=l(),r=e.ref(""),o=e.ref("deepseek-chat"),s=e.ref(.2),n=e.ref(!1),i=e.ref({apiKey:""}),c=()=>{r.value?r.value.startsWith("sk-")?i.value.apiKey="":i.value.apiKey="API 密钥格式不正确，应以 sk- 开头":i.value.apiKey="API 密钥不能为空"},u=e.computed((()=>Object.values(i.value).some((e=>""!==e))));return e.onMounted((()=>{const e=a.state.apiSettings;e&&(r.value=e.apiKey||"",o.value=e.model||"deepseek-chat",s.value=void 0!==e.temperature?e.temperature:.2)})),{apiKey:r,selectedModel:o,temperature:s,isLoading:n,errors:i,hasErrors:u,validateApiKey:c,handleTemperatureChange:e=>{s.value=e.detail.value/10},saveSettings:async()=>{if(c(),!u.value){n.value=!0;try{await a.dispatch("saveApiSettings",{apiKey:r.value,model:o.value,temperature:s.value}),uni.showToast({title:"设置已保存",icon:"success"}),setTimeout((()=>{uni.navigateBack()}),1500)}catch(e){t("error","at pages/api-settings/api-settings.vue:136","保存设置失败:",e),uni.showToast({title:"保存失败，请重试",icon:"none"})}finally{n.value=!1}}}}}},[["render",function(t,a,r,o,s,n){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("text",{class:"title"},"API 设置")])]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"form-content"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"DeepSeek API 密钥"),e.withDirectives(e.createElementVNode("input",{type:"text","onUpdate:modelValue":a[0]||(a[0]=e=>o.apiKey=e),placeholder:"请输入 DeepSeek API 密钥",class:"input-field",onBlur:a[1]||(a[1]=(...e)=>o.validateApiKey&&o.validateApiKey(...e))},null,544),[[e.vModelText,o.apiKey]]),o.errors.apiKey?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(o.errors.apiKey),1)):e.createCommentVNode("",!0),e.createElementVNode("text",{class:"hint-text"},"格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"选择模型"),e.createElementVNode("view",{class:"model-selector"},[e.createElementVNode("view",{class:e.normalizeClass(["model-option",{active:"deepseek-chat"===o.selectedModel}]),onClick:a[2]||(a[2]=e=>o.selectedModel="deepseek-chat")},[e.createElementVNode("text",{class:"model-name"},"DeepSeek-V3"),e.createElementVNode("text",{class:"model-id"},"deepseek-chat"),e.createElementVNode("text",{class:"model-desc"},"通用对话模型，适合日常对话和分析")],2),e.createElementVNode("view",{class:e.normalizeClass(["model-option",{active:"deepseek-reasoner"===o.selectedModel}]),onClick:a[3]||(a[3]=e=>o.selectedModel="deepseek-reasoner")},[e.createElementVNode("text",{class:"model-name"},"DeepSeek-R1"),e.createElementVNode("text",{class:"model-id"},"deepseek-reasoner"),e.createElementVNode("text",{class:"model-desc"},"推理模型，适合复杂逻辑和数学计算")],2)])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"温度 (Temperature)"),e.createElementVNode("view",{class:"slider-container"},[e.createElementVNode("slider",{value:10*o.temperature,min:"0",max:"10","show-value":"",onChange:a[4]||(a[4]=(...e)=>o.handleTemperatureChange&&o.handleTemperatureChange(...e))},null,40,["value"]),e.createElementVNode("text",{class:"slider-value"},e.toDisplayString(o.temperature.toFixed(1)),1)]),e.createElementVNode("text",{class:"hint-text"},"较低的值使输出更确定，较高的值使输出更随机")]),e.createElementVNode("button",{class:"submit-button",onClick:a[5]||(a[5]=(...e)=>o.saveSettings&&o.saveSettings(...e)),disabled:o.isLoading||o.hasErrors},e.toDisplayString(o.isLoading?"保存中...":"保存设置"),9,["disabled"])])])])}]]);__definePage("pages/index/index",H),__definePage("pages/login/login",W),__definePage("pages/register/register",K),__definePage("pages/forgot-password/forgot-password",J),__definePage("pages/details/details",X),__definePage("pages/settings/settings",Y),__definePage("pages/change-password/change-password",z),__definePage("pages/contact/contact",Z),__definePage("pages/category-management/category-management",ee),__definePage("pages/api-settings/api-settings",te);const ae={onLaunch:async function(){t("log","at App.vue:6","App Launch"),t("log","at App.vue:7","当前应用图标路径:",plus.runtime.appIcon);try{await F.initDatabase(),t("log","at App.vue:10","数据库初始化成功")}catch(e){t("error","at App.vue:12","数据库初始化失败:",e)}},onShow:function(){t("log","at App.vue:16","App Show")},onHide:function(){t("log","at App.vue:19","App Hide")},onUnload:async function(){try{await F.closeDatabase(),t("log","at App.vue:24","数据库关闭成功")}catch(e){t("error","at App.vue:26","数据库关闭失败:",e)}}},re=[];for(let Re=0;Re<256;++Re)re.push((Re+256).toString(16).slice(1));var oe=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,62,0,62,0,63,52,53,54,55,56,57,58,59,60,61,0,0,0,0,0,0,0,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,0,0,0,0,63,0,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51];const se={getRandomValues(e){if(!(e instanceof Int8Array||e instanceof Uint8Array||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Uint8ClampedArray))throw new Error("Expected an integer array");if(e.byteLength>65536)throw new Error("Can only request a maximum of 65536 bytes");var t;return function(e,t){for(var a,r=e.length,o="="===e[r-2]?2:"="===e[r-1]?1:0,s=0,n=r-o&4294967292,l=0;l<n;l+=4)a=oe[e.charCodeAt(l)]<<18|oe[e.charCodeAt(l+1)]<<12|oe[e.charCodeAt(l+2)]<<6|oe[e.charCodeAt(l+3)],t[s++]=a>>16&255,t[s++]=a>>8&255,t[s++]=255&a;1===o&&(a=oe[e.charCodeAt(l)]<<10|oe[e.charCodeAt(l+1)]<<4|oe[e.charCodeAt(l+2)]>>2,t[s++]=a>>8&255,t[s++]=255&a),2===o&&(a=oe[e.charCodeAt(l)]<<2|oe[e.charCodeAt(l+1)]>>4,t[s++]=255&a)}((t="DCloud-Crypto",weex.requireModule(t)).getRandomValues(e.byteLength),new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e}};let ne;const le=new Uint8Array(16);const ie={randomUUID:void 0!==se&&se.randomUUID&&se.randomUUID.bind(se)};function ce(e,t,a){var r;if(ie.randomUUID&&!t&&!e)return ie.randomUUID();const o=(e=e||{}).random??(null==(r=e.rng)?void 0:r.call(e))??function(){if(!ne){if(void 0===se||!se.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");ne=se.getRandomValues.bind(se)}return ne(le)}();if(o.length<16)throw new Error("Random bytes length must be >= 16");if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t){if((a=a||0)<0||a+16>t.length)throw new RangeError(`UUID byte range ${a}:${a+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[a+e]=o[e];return t}return function(e,t=0){return(re[e[t+0]]+re[e[t+1]]+re[e[t+2]]+re[e[t+3]]+"-"+re[e[t+4]]+re[e[t+5]]+"-"+re[e[t+6]]+re[e[t+7]]+"-"+re[e[t+8]]+re[e[t+9]]+"-"+re[e[t+10]]+re[e[t+11]]+re[e[t+12]]+re[e[t+13]]+re[e[t+14]]+re[e[t+15]]).toLowerCase()}(o)}const ue={};var de=null;function me(e,t){if("number"!=typeof(e=e||_e))throw Error("Illegal arguments: "+typeof e+", "+typeof t);e<4?e=4:e>31&&(e=31);var a=[];return a.push("$2b$"),e<10&&a.push("0"),a.push(e.toString()),a.push("$"),a.push(be(function(e){try{return se.getRandomValues(new Uint8Array(e))}catch{}try{return ue.randomBytes(e)}catch{}if(!de)throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");return de(e)}(Ve),Ve)),a.join("")}function ge(e,t,a){if("function"==typeof t&&(a=t,t=void 0),"function"==typeof e&&(a=e,e=void 0),void 0===e)e=_e;else if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);function r(t){fe((function(){try{t(null,me(e))}catch(a){t(a)}}))}if(!a)return new Promise((function(e,t){r((function(a,r){a?t(a):e(r)}))}));if("function"!=typeof a)throw Error("Illegal callback: "+typeof a);r(a)}function pe(e,t){if(void 0===t&&(t=_e),"number"==typeof t&&(t=me(t)),"string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return Ae(e,t)}function he(e,t,a,r){function o(a){"string"==typeof e&&"number"==typeof t?ge(t,(function(t,o){Ae(e,o,a,r)})):"string"==typeof e&&"string"==typeof t?Ae(e,t,a,r):fe(a.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)))}if(!a)return new Promise((function(e,t){o((function(a,r){a?t(a):e(r)}))}));if("function"!=typeof a)throw Error("Illegal callback: "+typeof a);o(a)}function ve(e,t){for(var a=e.length^t.length,r=0;r<e.length;++r)a|=e.charCodeAt(r)^t.charCodeAt(r);return 0===a}var fe="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout;function we(e){for(var t=0,a=0,r=0;r<e.length;++r)(a=e.charCodeAt(r))<128?t+=1:a<2048?t+=2:55296==(64512&a)&&56320==(64512&e.charCodeAt(r+1))?(++r,t+=4):t+=3;return t}var ye="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),Ee=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1];function be(e,t){var a,r,o=0,s=[];if(t<=0||t>e.length)throw Error("Illegal len: "+t);for(;o<t;){if(a=255&e[o++],s.push(ye[a>>2&63]),a=(3&a)<<4,o>=t){s.push(ye[63&a]);break}if(a|=(r=255&e[o++])>>4&15,s.push(ye[63&a]),a=(15&r)<<2,o>=t){s.push(ye[63&a]);break}a|=(r=255&e[o++])>>6&3,s.push(ye[63&a]),s.push(ye[63&r])}return s.join("")}function Ne(e,t){var a,r,o,s,n,l=0,i=e.length,c=0,u=[];if(t<=0)throw Error("Illegal len: "+t);for(;l<i-1&&c<t&&(a=(n=e.charCodeAt(l++))<Ee.length?Ee[n]:-1,r=(n=e.charCodeAt(l++))<Ee.length?Ee[n]:-1,-1!=a&&-1!=r)&&(s=a<<2>>>0,s|=(48&r)>>4,u.push(String.fromCharCode(s)),!(++c>=t||l>=i))&&-1!=(o=(n=e.charCodeAt(l++))<Ee.length?Ee[n]:-1)&&(s=(15&r)<<4>>>0,s|=(60&o)>>2,u.push(String.fromCharCode(s)),!(++c>=t||l>=i));)s=(3&o)<<6>>>0,s|=(n=e.charCodeAt(l++))<Ee.length?Ee[n]:-1,u.push(String.fromCharCode(s)),++c;var d=[];for(l=0;l<c;l++)d.push(u[l].charCodeAt(0));return d}var Ve=16,_e=10,xe=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],Se=[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946,1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055,3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504,976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462],Ce=[1332899944,1700884034,1701343084,1684370003,1668446532,1869963892];function Te(e,t,a,r){var o,s=e[t],n=e[t+1];return o=r[(s^=a[0])>>>24],o+=r[256|s>>16&255],o^=r[512|s>>8&255],o=r[(n^=(o+=r[768|255&s])^a[1])>>>24],o+=r[256|n>>16&255],o^=r[512|n>>8&255],o=r[(s^=(o+=r[768|255&n])^a[2])>>>24],o+=r[256|s>>16&255],o^=r[512|s>>8&255],o=r[(n^=(o+=r[768|255&s])^a[3])>>>24],o+=r[256|n>>16&255],o^=r[512|n>>8&255],o=r[(s^=(o+=r[768|255&n])^a[4])>>>24],o+=r[256|s>>16&255],o^=r[512|s>>8&255],o=r[(n^=(o+=r[768|255&s])^a[5])>>>24],o+=r[256|n>>16&255],o^=r[512|n>>8&255],o=r[(s^=(o+=r[768|255&n])^a[6])>>>24],o+=r[256|s>>16&255],o^=r[512|s>>8&255],o=r[(n^=(o+=r[768|255&s])^a[7])>>>24],o+=r[256|n>>16&255],o^=r[512|n>>8&255],o=r[(s^=(o+=r[768|255&n])^a[8])>>>24],o+=r[256|s>>16&255],o^=r[512|s>>8&255],o=r[(n^=(o+=r[768|255&s])^a[9])>>>24],o+=r[256|n>>16&255],o^=r[512|n>>8&255],o=r[(s^=(o+=r[768|255&n])^a[10])>>>24],o+=r[256|s>>16&255],o^=r[512|s>>8&255],o=r[(n^=(o+=r[768|255&s])^a[11])>>>24],o+=r[256|n>>16&255],o^=r[512|n>>8&255],o=r[(s^=(o+=r[768|255&n])^a[12])>>>24],o+=r[256|s>>16&255],o^=r[512|s>>8&255],o=r[(n^=(o+=r[768|255&s])^a[13])>>>24],o+=r[256|n>>16&255],o^=r[512|n>>8&255],o=r[(s^=(o+=r[768|255&n])^a[14])>>>24],o+=r[256|s>>16&255],o^=r[512|s>>8&255],o=r[(n^=(o+=r[768|255&s])^a[15])>>>24],o+=r[256|n>>16&255],o^=r[512|n>>8&255],s^=(o+=r[768|255&n])^a[16],e[t]=n^a[17],e[t+1]=s,e}function ke(e,t){for(var a=0,r=0;a<4;++a)r=r<<8|255&e[t],t=(t+1)%e.length;return{key:r,offp:t}}function De(e,t,a){for(var r,o=0,s=[0,0],n=t.length,l=a.length,i=0;i<n;i++)o=(r=ke(e,o)).offp,t[i]=t[i]^r.key;for(i=0;i<n;i+=2)s=Te(s,0,t,a),t[i]=s[0],t[i+1]=s[1];for(i=0;i<l;i+=2)s=Te(s,0,t,a),a[i]=s[0],a[i+1]=s[1]}function je(e,t,a,r,o){var s,n=Ce.slice(),l=n.length;if(a<4||a>31){if(s=Error("Illegal number of rounds (4-31): "+a),r)return void fe(r.bind(this,s));throw s}if(t.length!==Ve){if(s=Error("Illegal salt length: "+t.length+" != "+Ve),r)return void fe(r.bind(this,s));throw s}a=1<<a>>>0;var i,c,u,d=0;function m(){if(o&&o(d/a),!(d<a)){for(d=0;d<64;d++)for(u=0;u<l>>1;u++)Te(n,u<<1,i,c);var s=[];for(d=0;d<l;d++)s.push((n[d]>>24&255)>>>0),s.push((n[d]>>16&255)>>>0),s.push((n[d]>>8&255)>>>0),s.push((255&n[d])>>>0);return r?void r(null,s):s}for(var g=Date.now();d<a&&(d+=1,De(e,i,c),De(t,i,c),!(Date.now()-g>100)););r&&fe(m)}if("function"==typeof Int32Array?(i=new Int32Array(xe),c=new Int32Array(Se)):(i=xe.slice(),c=Se.slice()),function(e,t,a,r){for(var o,s=0,n=[0,0],l=a.length,i=r.length,c=0;c<l;c++)s=(o=ke(t,s)).offp,a[c]=a[c]^o.key;for(s=0,c=0;c<l;c+=2)s=(o=ke(e,s)).offp,n[0]^=o.key,s=(o=ke(e,s)).offp,n[1]^=o.key,n=Te(n,0,a,r),a[c]=n[0],a[c+1]=n[1];for(c=0;c<i;c+=2)s=(o=ke(e,s)).offp,n[0]^=o.key,s=(o=ke(e,s)).offp,n[1]^=o.key,n=Te(n,0,a,r),r[c]=n[0],r[c+1]=n[1]}(t,e,i,c),void 0!==r)m();else for(var g;;)if(void 0!==(g=m()))return g||[]}function Ae(e,t,a,r){var o,s,n;if("string"!=typeof e||"string"!=typeof t){if(o=Error("Invalid string / salt: Not a string"),a)return void fe(a.bind(this,o));throw o}if("$"!==t.charAt(0)||"2"!==t.charAt(1)){if(o=Error("Invalid salt version: "+t.substring(0,2)),a)return void fe(a.bind(this,o));throw o}if("$"===t.charAt(2))s=String.fromCharCode(0),n=3;else{if("a"!==(s=t.charAt(2))&&"b"!==s&&"y"!==s||"$"!==t.charAt(3)){if(o=Error("Invalid salt revision: "+t.substring(2,4)),a)return void fe(a.bind(this,o));throw o}n=4}if(t.charAt(n+2)>"$"){if(o=Error("Missing salt rounds"),a)return void fe(a.bind(this,o));throw o}var l=10*parseInt(t.substring(n,n+1),10)+parseInt(t.substring(n+1,n+2),10),i=t.substring(n+3,n+25),c=function(e){for(var t,a,r=0,o=new Array(we(e)),s=0,n=e.length;s<n;++s)(t=e.charCodeAt(s))<128?o[r++]=t:t<2048?(o[r++]=t>>6|192,o[r++]=63&t|128):55296==(64512&t)&&56320==(64512&(a=e.charCodeAt(s+1)))?(t=65536+((1023&t)<<10)+(1023&a),++s,o[r++]=t>>18|240,o[r++]=t>>12&63|128,o[r++]=t>>6&63|128,o[r++]=63&t|128):(o[r++]=t>>12|224,o[r++]=t>>6&63|128,o[r++]=63&t|128);return o}(e+=s>="a"?"\0":""),u=Ne(i,Ve);function d(e){var t=[];return t.push("$2"),s>="a"&&t.push(s),t.push("$"),l<10&&t.push("0"),t.push(l.toString()),t.push("$"),t.push(be(u,u.length)),t.push(be(e,4*Ce.length-1)),t.join("")}if(void 0===a)return d(je(c,u,l));je(c,u,l,(function(e,t){e?a(e,null):a(null,d(t))}),r)}const Le={setRandomFallback:function(e){de=e},genSaltSync:me,genSalt:ge,hashSync:pe,hash:he,compareSync:function(e,t){if("string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return 60===t.length&&ve(pe(e,t.substring(0,t.length-31)),t)},compare:function(e,t,a,r){function o(a){"string"==typeof e&&"string"==typeof t?60===t.length?he(e,t.substring(0,29),(function(e,r){e?a(e):a(null,ve(r,t))}),r):fe(a.bind(this,null,!1)):fe(a.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)))}if(!a)return new Promise((function(e,t){o((function(a,r){a?t(a):e(r)}))}));if("function"!=typeof a)throw Error("Illegal callback: "+typeof a);o(a)},getRounds:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return parseInt(e.split("$")[2],10)},getSalt:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);if(60!==e.length)throw Error("Illegal hash length: "+e.length+" != 60");return e.substring(0,29)},truncates:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return we(e)>72},encodeBase64:function(e,t){return be(e,t)},decodeBase64:function(e,t){return Ne(e,t)}};const Ie=new class{async register(e,a,r){try{if(!e||!a||!r)throw new Error("用户名、密码和邮箱都不能为空");const s=await F.selectSql("SELECT * FROM users WHERE username = ? OR email = ?",[e,r]);s&&s.length>0&&(t("log","at services/localDataService.js:21","用户已存在，准备删除:",s),await F.executeSql("DELETE FROM users WHERE username = ? OR email = ?",[e,r]),t("log","at services/localDataService.js:26","已删除现有用户"));const n=ce().replace(/-/g,"").substr(0,13);let l;t("log","at services/localDataService.js:30","生成的用户ID:",n);try{const e=Le.genSaltSync(10);l=Le.hashSync(a,e),t("log","at services/localDataService.js:37","密码加密成功:",!!l)}catch(o){throw t("error","at services/localDataService.js:39","密码加密失败:",o),new Error("密码处理失败，请重试")}if(!l)throw new Error("密码处理失败，请重试");t("log","at services/localDataService.js:47","准备插入数据:",{userId:n,username:e,passwordLength:l?l.length:0,email:r});try{await F.executeSql("INSERT INTO users (user_id, username, password, email) VALUES (?, ?, ?, ?)",[n,e,l,r]),t("log","at services/localDataService.js:59","数据插入成功")}catch(o){throw t("error","at services/localDataService.js:61","数据插入失败，详细信息:",{error:JSON.stringify(o,null,2),userId:n,username:e,hashedPassword:l?"已加密":null,email:r}),o}return{user_id:n,username:e,email:r}}catch(o){throw t("error","at services/localDataService.js:77","注册失败:",o),o}}async login(e,a){try{t("log","at services/localDataService.js:85","开始登录流程:",{username:e});try{await F.openDatabase(),t("log","at services/localDataService.js:90","数据库连接已确认")}catch(r){throw t("error","at services/localDataService.js:92","数据库连接失败:",r),new Error("数据库连接失败，请重试")}let o;try{o=await F.selectSql("SELECT * FROM users WHERE username = ?",[e]),t("log","at services/localDataService.js:103","用户查询结果:",o)}catch(r){throw t("error","at services/localDataService.js:105","用户查询失败:",r),new Error("用户查询失败，请重试")}if(!o||0===o.length)throw t("log","at services/localDataService.js:110","未找到用户:",e),new Error("用户不存在");const s=o[0];t("log","at services/localDataService.js:115","找到用户:",{userId:s.user_id,username:s.username,hasPassword:!!s.password,passwordLength:s.password?s.password.length:0});let n,l=!1;try{l=Le.compareSync(a,s.password),t("log","at services/localDataService.js:126","密码验证结果:",l)}catch(r){throw t("error","at services/localDataService.js:128","密码验证出错:",r),new Error("密码验证失败，请重试")}if(!l)throw new Error("密码错误");try{n=await F.selectSql("SELECT * FROM categories WHERE user_id = ?",[s.user_id]),t("log","at services/localDataService.js:143","用户分类查询结果:",n)}catch(r){t("error","at services/localDataService.js:145","分类查询失败:",r)}if(n&&0!==n.length)t("log","at services/localDataService.js:159","用户已有分类，跳过创建");else try{await this.createDefaultCategories(s.user_id),t("log","at services/localDataService.js:153","默认分类创建成功")}catch(r){t("error","at services/localDataService.js:155","创建默认分类失败:",r)}const i={user_id:s.user_id,username:s.username,email:s.email};return t("log","at services/localDataService.js:168","登录成功，返回用户数据:",i),i}catch(r){throw t("error","at services/localDataService.js:172","登录失败，完整错误:",r),r}}async getCategories(e){try{t("log","at services/localDataService.js:180","开始获取分类，用户ID:",e);const a=await F.selectSql("SELECT * FROM categories WHERE user_id = ? ORDER BY level, sort_order",[e]);t("log","at services/localDataService.js:185","从数据库获取的原始分类数据:",a);const r=this.buildCategoryTree(a);return t("log","at services/localDataService.js:188","构建的分类树:",r),r}catch(a){throw t("error","at services/localDataService.js:192","获取分类失败:",a),a}}async saveExpense(e,a,r){try{for(const t of r){const r=ce().replace(/-/g,"").substr(0,13);await F.executeSql("INSERT INTO expenses (record_id, user_id, category_id, target, amount, record_date) VALUES (?, ?, ?, ?, ?, ?)",[r,e,t.category_id,t.target,t.amount,a]),await this.updateCategoryAmount(t.category_id,t.amount)}return{success:!0}}catch(o){throw t("error","at services/localDataService.js:214","保存支出失败:",o),o}}async changePassword(e,a,r){try{if(!a||!r)throw new Error("旧密码和新密码都不能为空");if(r.length<6)throw new Error("新密码长度不能少于6位");const t=await F.selectSql("SELECT * FROM users WHERE user_id = ?",[e]);if(!t||0===t.length)throw new Error("用户不存在");const o=t[0];if(!Le.compareSync(a,o.password))throw new Error("旧密码错误");const s=Le.genSaltSync(10),n=Le.hashSync(r,s);return await F.executeSql("UPDATE users SET password = ? WHERE user_id = ?",[n,e]),{success:!0}}catch(o){throw t("error","at services/localDataService.js:260","修改密码失败:",o),o}}async verifyUserEmail(e,a){try{t("log","at services/localDataService.js:268","开始验证用户邮箱:",{username:e,email:a});const r=await F.selectSql("SELECT * FROM users WHERE username = ?",[e]);if(!r||0===r.length)throw new Error("用户不存在");const o=r[0];if(o.email!==a)throw new Error("邮箱不匹配");return{success:!0,user_id:o.user_id}}catch(r){throw t("error","at services/localDataService.js:292","验证用户邮箱失败:",r),r}}async resetPassword(e,a,r){try{if(!e||!a||!r)throw new Error("用户名、邮箱和新密码都不能为空");if(r.length<6)throw new Error("新密码长度不能少于6位");const t=await this.verifyUserEmail(e,a);if(!t.success)throw new Error("身份验证失败");const o=Le.genSaltSync(10),s=Le.hashSync(r,o);return await F.executeSql("UPDATE users SET password = ? WHERE user_id = ?",[s,t.user_id]),{success:!0}}catch(o){throw t("error","at services/localDataService.js:327","重置密码失败:",o),o}}buildCategoryTree(e){try{const a=[],r={};e.forEach((e=>{r[e.category_id]={...e,children:[],total_amount:parseFloat(e.total_amount||0),level:parseInt(e.level)}})),e.forEach((e=>{const o=r[e.category_id];if(e.parent_id){const a=r[e.parent_id];a?a.children.push(o):t("error","at services/localDataService.js:359","未找到父分类:",e.parent_id,"对应的分类:",e.name)}else a.push(o)}));const o=e=>Array.isArray(e)?(e.sort(((e,t)=>e.level!==t.level?e.level-t.level:(e.sort_order||0)-(t.sort_order||0))),e.forEach((e=>{e.children&&e.children.length>0&&(e.children=o(e.children))})),e):e;return o(a)}catch(a){throw t("error","at services/localDataService.js:385","构建分类树失败:",a),a}}async updateCategoryAmount(e,a){try{const t=async e=>{const r=await F.selectSql("SELECT * FROM categories WHERE category_id = ?",[e]);r&&r.length>0&&(await F.executeSql("UPDATE categories SET total_amount = total_amount + ? WHERE category_id = ?",[a,e]),r[0].parent_id&&await t(r[0].parent_id))};await t(e)}catch(r){throw t("error","at services/localDataService.js:414","更新分类金额失败:",r),r}}async createDefaultCategories(e){try{const r=await F.selectSql("SELECT category_id FROM categories WHERE user_id = ?",[e]);if(r&&r.length>0)return void t("log","at services/localDataService.js:429","用户已有分类，跳过创建");const o=[["1",null,"生存必需类",1,1],["1.1","1","餐饮日用",2,1],["1.1.1","1.1","食材采购",3,1],["1.1.2","1.1","外卖外食",3,2],["1.1.3","1.1","商超购物",3,3],["1.2","1","交通出行",2,2],["1.2.1","1.2","公共交通",3,1],["1.2.2","1.2","油/电/费",3,2],["1.2.3","1.2","车辆维保",3,3],["1.3","1","住房相关",2,3],["1.3.1","1.3","房租/房贷",3,1],["1.3.2","1.3","水电燃气",3,2],["1.3.3","1.3","物业费",3,3],["1.3.4","1.3","维修养护",3,4],["1.4","1","医疗",2,4],["1.4.1","1.4","医院医疗",3,1],["1.4.2","1.4","常规药品",3,2],["1.4.3","1.4","体检疫苗",3,3],["1.5","1","父母子女",2,5],["1.5.1","1.5","父母",3,1],["1.5.2","1.5","子女教育",3,2],["1.5.3","1.5","子女消费",3,3],["2",null,"发展提升类",1,2],["2.1","2","教育培训",2,1],["2.1.1","2.1","课程费用",3,1],["2.1.2","2.1","书籍资料",3,2],["2.1.3","2.1","技能提升",3,3],["2.2","2","职业发展",2,2],["2.2.1","2.2","专业工具",3,1],["2.2.2","2.2","公司交费",3,2],["3",null,"品质生活类",1,3],["3.1","3","服饰美容",2,1],["3.1.1","3.1","服装鞋帽",3,1],["3.1.2","3.1","护肤美妆",3,2],["3.1.3","3.1","发型护理",3,3],["3.2","3","数码电子",2,2],["3.2.1","3.2","设备购置",3,1],["3.2.2","3.2","配件耗材",3,2],["3.3","3","娱乐休闲",2,3],["3.3.1","3.3","歌舞休闲",3,1],["3.3.2","3.3","运动健身",3,2],["3.3.3","3.3","影游演展",3,3],["3.3.4","3.3","其他娱乐",3,4],["3.4","3","旅行度假",2,4],["3.4.1","3.4","交通住宿",3,1],["3.4.2","3.4","景区消费",3,2],["4",null,"人际往来类",1,4],["4.1","4","社交人情",2,1],["4.1.1","4.1","节日礼品",3,1],["4.1.2","4.1","聚餐请客",3,2],["4.1.3","4.1","红包礼金",3,3],["4.2","4","宠物养护",2,2],["4.2.1","4.2","宠物费用",3,1],["5",null,"资产流动类",1,5],["5.1","5","投资理财",2,1],["5.1.1","5.1","股基理财",3,1],["5.1.2","5.1","资产投资",3,2],["5.1.3","5.1","保险费用",3,3],["5.2","5","债务管理",2,2],["5.2.1","5.2","贷款还款",3,1],["5.2.2","5.2","其他借款",3,2],["6",null,"其他支出类",1,6],["6.1","6","其他支出",2,1],["6.1.1","6.1","罚款税金",3,1],["6.1.2","6.1","捐赠",3,2],["6.1.3","6.1","其他支出",3,3]];t("log","at services/localDataService.js:516","开始创建默认分类，用户ID:",e);const s=new Map;for(const[n,l,i,c,u]of o){const r=`${ce().replace(/-/g,"").substr(0,8)}_${n}`;let o=null;if(!l||(o=s.get(l),o))try{await F.executeSql("INSERT INTO categories (category_id, user_id, parent_id, name, level, sort_order) VALUES (?, ?, ?, ?, ?, ?)",[r,e,o,i,c,u]),t("log","at services/localDataService.js:540","分类创建成功:",{categoryId:r,displayId:n,name:i}),s.set(n,r)}catch(a){throw t("error","at services/localDataService.js:545","创建单个分类失败:",{categoryId:r,displayId:n,parentId:o,name:i,level:c,sortOrder:u,error:JSON.stringify(a,null,2)}),a}else t("error","at services/localDataService.js:530","未找到父分类:",l)}t("log","at services/localDataService.js:558","所有默认分类创建完成")}catch(a){throw t("error","at services/localDataService.js:560","创建默认分类失败:",a),a}}async addCategory(e,a,r,o,s){try{const t=ce().replace(/-/g,"").substr(0,13);return await F.executeSql("INSERT INTO categories (category_id, user_id, parent_id, name, level, sort_order) VALUES (?, ?, ?, ?, ?, ?)",[t,e,r,a,o,s]),{category_id:t,user_id:e,parent_id:r,name:a,level:o,sort_order:s,total_amount:0,children:[]}}catch(n){throw t("error","at services/localDataService.js:589","添加分类失败:",n),n}}async updateCategory(e,a){try{return await F.executeSql("UPDATE categories SET name = ? WHERE category_id = ?",[a,e]),{success:!0}}catch(r){throw t("error","at services/localDataService.js:603","修改分类失败:",r),r}}async deleteCategory(e){try{const t=await F.selectSql("SELECT category_id FROM categories WHERE parent_id = ?",[e]);if(t&&t.length>0)throw new Error("该分类下有子分类，无法删除");const a=await F.selectSql("SELECT record_id FROM expenses WHERE category_id = ?",[e]);if(a&&a.length>0)throw new Error("该分类下有支出记录，无法删除");return await F.executeSql("DELETE FROM categories WHERE category_id = ?",[e]),{success:!0}}catch(a){throw t("error","at services/localDataService.js:639","删除分类失败:",a),a}}async updateCategoryOrder(e,a){try{return await F.executeSql("UPDATE categories SET sort_order = ? WHERE category_id = ?",[a,e]),{success:!0}}catch(r){throw t("error","at services/localDataService.js:653","更新分类排序失败:",r),r}}async clearUserExpenses(e){try{t("log","at services/localDataService.js:661","开始清空用户支出记录:",e);const r=`expenses_backup_${(new Date).toISOString().slice(0,10)}.json`;let o="";const s=await F.selectSql("SELECT * FROM expenses WHERE user_id = ?",[e]);if(t("log","at services/localDataService.js:674",`找到 ${s?s.length:0} 条支出记录待清空`),s&&s.length>0)try{t("log","at services/localDataService.js:679","开始请求存储权限"),await new Promise(((e,a)=>{plus.android.requestPermissions(["android.permission.WRITE_EXTERNAL_STORAGE"],(function(r){t("log","at services/localDataService.js:686","权限请求结果:",r),1===r.granted.length?e():a(new Error("未授予存储权限"))}),(function(e){t("error","at services/localDataService.js:694","权限请求失败:",e),a(e)}))})),t("log","at services/localDataService.js:700","已获得存储权限，准备导出数据");const e=plus.android.importClass("android.os.Environment"),a=plus.android.importClass("java.io.File"),n=(plus.android.runtimeMainActivity(),e.getExternalStorageDirectory().getAbsolutePath()+"/Download/"+r);t("log","at services/localDataService.js:712","使用外部存储路径:",n);const l=new a(n).getParentFile();l.exists()||l.mkdirs();const i=JSON.stringify(s,null,2),c=plus.android.importClass("java.io.FileOutputStream"),u=plus.android.importClass("java.io.OutputStreamWriter"),d=plus.android.importClass("java.io.BufferedWriter"),m=new c(n),g=new u(m,"UTF-8"),p=new d(g);p.write(i),p.flush(),p.close(),g.close(),m.close(),t("log","at services/localDataService.js:738","数据备份成功:",n),o=n}catch(a){t("error","at services/localDataService.js:741","备份数据失败:",a)}return await F.executeSql("DELETE FROM expenses WHERE user_id = ?",[e]),await F.executeSql("UPDATE categories SET total_amount = 0 WHERE user_id = ?",[e]),t("log","at services/localDataService.js:758","清空支出记录成功"),{success:!0,message:"清空成功",backupFile:o||r}}catch(r){throw t("error","at services/localDataService.js:765","清空支出记录失败:",r),r}}},Pe=(Me={state:{apiBaseUrl:"https://api.mybooking.com/api/v1",user:uni.getStorageSync("user")?JSON.parse(uni.getStorageSync("user")):null,categories:[],isLoading:!1,error:null,categoryMap:new Map,apiSettings:uni.getStorageSync("apiSettings")?JSON.parse(uni.getStorageSync("apiSettings")):{apiKey:"sk-38ff2a6b9f754b059fa42839d5a4b426",model:"deepseek-chat",temperature:.2}},mutations:{setUser(e,t){e.user=t,t?uni.setStorageSync("user",JSON.stringify(t)):uni.removeStorageSync("user")},setApiSettings(e,t){e.apiSettings=t,uni.setStorageSync("apiSettings",JSON.stringify(t))},clearUserInfo(e){e.user=null,e.categories=[],e.error=null,uni.removeStorageSync("user")},setLoading(e,t){e.isLoading=t},setError(e,t){e.error=t},updateCategories(e,t){const a=new Map,r=(e,t=null)=>{e.forEach((e=>{e.total_amount=Number(e.total_amount||0),e.parent_id=t,a.set(e.category_id,e),e.children&&e.children.length>0&&r(e.children,e.category_id)}))};r(t),a.forEach((e=>{if(e.parent_id){const t=a.get(e.parent_id);t&&(t.children||(t.children=[]),t.children.find((t=>t.category_id===e.category_id))||t.children.push(e))}})),e.categories=t.filter((e=>!e.parent_id)),e.categoryMap=a},updateCategoryAmount(e,{categoryId:t,amount:a}){const r=e.categoryMap.get(t);if(!r)return;r.total_amount=(r.total_amount||0)+a;const o=(t,a)=>{if(!t)return;const r=e.categoryMap.get(t);r&&(r.total_amount=(r.total_amount||0)+a,o(r.parent_id,a))};o(r.parent_id,a)}},actions:{async init({commit:e}){try{await F.open(),await F.initTables()}catch(a){t("error","at store/index.js:118","初始化失败:",a),e("setError",a.message)}},async register({commit:e},{username:a,password:r,email:o}){try{e("setLoading",!0),e("setError",null);const t=await Ie.register(a,r,o);return e("setUser",t),t}catch(s){throw t("error","at store/index.js:132","注册失败:",s),e("setError",s.message),s}finally{e("setLoading",!1)}},async login({commit:e},{username:a,password:r}){try{e("setLoading",!0),e("setError",null);const o=await Ie.login(a,r);e("setUser",o);const s=await Ie.getCategories(o.user_id);return t("log","at store/index.js:150","登录后获取的分类数据:",s),e("updateCategories",s),o}catch(o){throw t("error","at store/index.js:154","登录失败:",o),e("setError",o.message),o}finally{e("setLoading",!1)}},logout({commit:e}){e("clearUserInfo")},async fetchCategories({commit:e,state:a}){try{if(e("setLoading",!0),e("setError",null),!a.user)throw new Error("用户未登录");const t=await Ie.getCategories(a.user.user_id);return e("updateCategories",t),t}catch(r){return t("error","at store/index.js:180","获取分类失败:",r),e("setError",r.message),[]}finally{e("setLoading",!1)}},async saveExpense({commit:e,state:a,dispatch:r},{date:o,records:s}){try{if(e("setLoading",!0),e("setError",null),!a.user)throw new Error("用户未登录");return await Ie.saveExpense(a.user.user_id,o,s),await r("fetchCategories"),{success:!0}}catch(n){throw t("error","at store/index.js:204","保存支出失败:",n),e("setError",n.message),n}finally{e("setLoading",!1)}},async changePassword({commit:e,state:a},{oldPassword:r,newPassword:o}){try{if(e("setLoading",!0),e("setError",null),!a.user)throw new Error("用户未登录");const t=await Ie.changePassword(a.user.user_id,r,o);return t.success&&(e("clearUserInfo"),uni.reLaunch({url:"/pages/login/login"})),t}catch(s){throw t("error","at store/index.js:238","修改密码失败:",s),e("setError",s.message),s}finally{e("setLoading",!1)}},updateCategories({commit:e},t){e("updateCategories",t)},async addCategory({commit:e,state:a,dispatch:r},{name:o,parentId:s,level:n,sortOrder:l}){try{if(e("setLoading",!0),e("setError",null),!a.user)throw new Error("用户未登录");const t=await Ie.addCategory(a.user.user_id,o,s,n,l);return await r("fetchCategories"),t}catch(i){throw t("error","at store/index.js:272","添加分类失败:",i),e("setError",i.message),i}finally{e("setLoading",!1)}},async updateCategory({commit:e,dispatch:a},{categoryId:r,name:o}){try{return e("setLoading",!0),e("setError",null),await Ie.updateCategory(r,o),await a("fetchCategories"),{success:!0}}catch(s){throw t("error","at store/index.js:292","修改分类失败:",s),e("setError",s.message),s}finally{e("setLoading",!1)}},async deleteCategory({commit:e,dispatch:a},r){try{return e("setLoading",!0),e("setError",null),await Ie.deleteCategory(r),await a("fetchCategories"),{success:!0}}catch(o){throw t("error","at store/index.js:312","删除分类失败:",o),e("setError",o.message),o}finally{e("setLoading",!1)}},async updateCategoryOrder({commit:e,dispatch:a},{categoryId:r,newSortOrder:o}){try{return e("setLoading",!0),e("setError",null),await Ie.updateCategoryOrder(r,o),await a("fetchCategories"),{success:!0}}catch(s){throw t("error","at store/index.js:332","更新分类排序失败:",s),e("setError",s.message),s}finally{e("setLoading",!1)}},async clearUserData({commit:e,state:a,dispatch:r}){try{if(e("setLoading",!0),e("setError",null),!a.user)throw new Error("用户未登录");const t=await Ie.clearUserExpenses(a.user.user_id);return await r("fetchCategories"),t}catch(o){throw t("error","at store/index.js:357","清空支出记录失败:",o),e("setError",o.message),o}finally{e("setLoading",!1)}},async verifyUserEmail({commit:e},{username:a,email:r}){try{return e("setLoading",!0),e("setError",null),await Ie.verifyUserEmail(a,r)}catch(o){throw t("error","at store/index.js:374","验证用户邮箱失败:",o),e("setError",o.message),o}finally{e("setLoading",!1)}},async resetPassword({commit:e},{username:a,email:r,newPassword:o}){try{return e("setLoading",!0),e("setError",null),await Ie.resetPassword(a,r,o)}catch(s){throw t("error","at store/index.js:391","重置密码失败:",s),e("setError",s.message),s}finally{e("setLoading",!1)}},async saveApiSettings({commit:e},a){try{if(e("setLoading",!0),e("setError",null),!a.apiKey)throw new Error("API 密钥不能为空");if(!a.model)throw new Error("模型不能为空");return e("setApiSettings",a),{success:!0}}catch(r){throw t("error","at store/index.js:419","保存 API 设置失败:",r),e("setError",r.message),r}finally{e("setLoading",!1)}}},getters:{isLoggedIn:e=>!!e.user,categories:e=>e.categories,isLoading:e=>e.isLoading,error:e=>e.error,getCategoryById:e=>t=>e.categoryMap.get(t)||null,getCategoryTotal:e=>t=>{const a=e=>e.children&&0!==e.children.length?e.children.reduce(((e,t)=>e+a(t)),e.total_amount||0):e.total_amount||0,r=e.categoryMap.get(t);return r?a(r):0}}},new j(Me));var Me;const{app:Oe,Vuex:Be,Pinia:Ue}=function(){const t=e.createVueApp(ae);return t.use(Pe),{app:t}}();uni.Vuex=Be,uni.Pinia=Ue,Oe.provide("__globalStyles",__uniConfig.styles),Oe._component.mpType="app",Oe._component.render=()=>{},Oe.mount("#app")}(Vue);
