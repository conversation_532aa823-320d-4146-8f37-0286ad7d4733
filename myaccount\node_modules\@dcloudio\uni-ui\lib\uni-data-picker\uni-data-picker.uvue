<template>
  <view class="uni-data-tree">
    <view class="uni-data-tree-input" @click="handleInput">
      <slot :data="selectedPaths" :error="error">
        <view class="input-value" :class="{'input-value-border': border}">
          <text v-if="error!=null" class="error-text">{{error!.errMsg}}</text>
          <scroll-view v-if="selectedPaths.length" class="selected-path" scroll-x="true">
            <view class="selected-list">
              <template v-for="(item, index) in selectedPaths">
                <text class="text-color">{{item[mappingTextName]}}</text>
                <text v-if="index<selectedPaths.length-1" class="input-split-line">{{split}}</text>
              </template>
            </view>
          </scroll-view>
          <text v-else-if="error==null&&!loading" class="placeholder">{{placeholder}}</text>
          <view v-if="!readonly" class="arrow-area">
            <view class="input-arrow"></view>
          </view>
        </view>
      </slot>
      <view v-if="loading && !isOpened" class="selected-loading">
        <slot name="picker-loading" :loading="loading"></slot>
      </view>
    </view>
    <view class="uni-data-tree-cover" v-if="isOpened" @click="handleClose"></view>
    <view class="uni-data-tree-dialog" v-if="isOpened">
      <view class="uni-popper__arrow"></view>
      <view class="dialog-caption">
        <view class="dialog-title-view">
          <text class="dialog-title">{{popupTitle}}</text>
        </view>
        <view class="dialog-close" @click="handleClose">
          <view class="dialog-close-plus" data-id="close"></view>
          <view class="dialog-close-plus dialog-close-rotate" data-id="close"></view>
        </view>
      </view>
      <view ref="pickerView" class="uni-data-pickerview">
        <view v-if="error!=null" class="error">
          <text class="error-text">{{error!.errMsg}}</text>
        </view>
        <scroll-view v-if="!isCloudDataList" :scroll-x="true">
          <view class="selected-node-list">
            <template v-for="(item, index) in selectedNodes">
              <text class="selected-node-item" :class="{'selected-node-item-active':index==selectedIndex}"
                @click="onTabSelect(index)">
                {{item[mappingTextName]}}
              </text>
            </template>
          </view>
        </scroll-view>
        <list-view class="list-view" :scroll-y="true">
          <list-item class="list-item" v-for="(item, _) in currentDataList" @click="onNodeClick(item)">
            <text class="item-text" :class="{'item-text-disabled': item['disable']}">{{item[mappingTextName]}}</text>
            <text class="check" v-if="item[mappingValueName] == selectedNodes[selectedIndex][mappingValueName]"></text>
          </list-item>
        </list-view>
        <view class="loading-cover" v-if="loading">
          <slot name="pickerview-loading" :loading="loading"></slot>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { dataPicker } from "../uni-data-pickerview/uni-data-picker.uts"

  /**
   * DataPicker 级联选择
   * @description 支持单列、和多列级联选择。列数没有限制，如果屏幕显示不全，顶部tab区域会左右滚动。
   * @tutorial https://ext.dcloud.net.cn/plugin?id=3796
   * @property {String} popup-title 弹出窗口标题
   * @property {Array} localdata 本地数据，参考
   * @property {Boolean} border = [true|false] 是否有边框
   * @property {Boolean} readonly = [true|false] 是否仅读
   * @property {Boolean} preload = [true|false] 是否预加载数据
   * @value true 开启预加载数据，点击弹出窗口后显示已加载数据
   * @value false 关闭预加载数据，点击弹出窗口后开始加载数据
   * @property {Boolean} step-searh = [true|false] 是否分布查询
   * @value true 启用分布查询，仅查询当前选中节点
   * @value false 关闭分布查询，一次查询出所有数据
   * @property {String|DBFieldString} self-field 分布查询当前字段名称
   * @property {String|DBFieldString} parent-field 分布查询父字段名称
   * @property {String|DBCollectionString} collection 表名
   * @property {String|DBFieldString} field 查询字段，多个字段用 `,` 分割
   * @property {String} orderby 排序字段及正序倒叙设置
   * @property {String|JQLString} where 查询条件
   * @event {Function} popupshow 弹出的选择窗口打开时触发此事件
   * @event {Function} popuphide 弹出的选择窗口关闭时触发此事件
   */
  export default {
    name: 'UniDataPicker',
    emits: ['popupopened', 'popupclosed', 'nodeclick', 'change', 'input', 'update:modelValue', 'inputclick'],
    mixins: [dataPicker],
    props: {
      popupTitle: {
        type: String,
        default: '请选择'
      },
      placeholder: {
        type: String,
        default: '请选择'
      },
      heightMobile: {
        type: String,
        default: ''
      },
      readonly: {
        type: Boolean,
        default: false
      },
      clearIcon: {
        type: Boolean,
        default: true
      },
      border: {
        type: Boolean,
        default: true
      },
      split: {
        type: String,
        default: '/'
      },
      ellipsis: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        isOpened: false
      }
    },
    computed: {
      isShowClearIcon() : boolean {
        if (this.readonly) {
          return false
        }

        if (this.clearIcon && this.selectedPaths.length > 0) {
          return true
        }

        return false
      }
    },
    created() {
      this.load()
    },
    methods: {
      clear() {
      },
      load() {
        if (this.isLocalData) {
          this.loadLocalData()
        } else if (this.isCloudDataList || this.isCloudDataTree) {
          this.loadCloudDataPath()
        }
      },
      show() {
        this.isOpened = true
        this.$emit('popupopened')
        if (!this.hasCloudTreeData) {
          this.loadData()
        }
      },
      hide() {
        this.isOpened = false
        this.$emit('popupclosed')
      },
      handleInput() {
        if (this.readonly) {
          this.$emit('inputclick')
        } else {
          this.show()
        }
      },
      handleClose() {
        this.hide()
      },
      onFinish() {
        this.selectedPaths = this.getChangeNodes()
        this.$emit('change', this.selectedPaths)
        this.hide()
      }
    }
  }
</script>

<style>
  @import url("../uni-data-pickerview/uni-data-pickerview.css");

  .uni-data-tree {
    position: relative;
  }

  .uni-data-tree-input {
    position: relative;
  }

  .selected-loading {
    display: flex;
    justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }

  .error-text {
    flex: 1;
    font-size: 12px;
    color: #DD524D;
  }

  .input-value {
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    padding: 5px 5px;
    padding-right: 5px;
    overflow: hidden;
    min-height: 28px;
  }

  .input-value-border {
    border: 1px solid #e5e5e5;
    border-radius: 5px;
  }

  .selected-path {
    flex: 1;
    flex-direction: row;
    overflow: hidden;
  }

  .load-more {
    width: 40px;
  }

  .selected-list {
    flex-direction: row;
    flex-wrap: nowrap;
  }

  .selected-item {
    flex-direction: row;
    flex-wrap: nowrap;
  }

  .text-color {
    font-size: 14px;
    color: #333;
  }

  .placeholder {
    color: grey;
    font-size: 14px;
  }

  .input-split-line {
    opacity: .5;
    margin-left: 1px;
    margin-right: 1px;
  }

  .arrow-area {
    position: relative;
    padding: 0 12px;
    margin-left: auto;
    justify-content: center;
    transform: rotate(-45deg);
    transform-origin: center;
  }

  .input-arrow {
    width: 8px;
    height: 8px;
    border-left: 2px solid #999;
    border-bottom: 2px solid #999;
  }

  .uni-data-tree-cover {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, .4);
    flex-direction: column;
    z-index: 100;
  }

  .uni-data-tree-dialog {
    position: fixed;
    left: 0;
    top: 20%;
    right: 0;
    bottom: 0;
    background-color: #FFFFFF;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    flex-direction: column;
    z-index: 102;
    overflow: hidden;
  }

  .dialog-caption {
    position: relative;
    flex-direction: row;
  }

  .dialog-title-view {
    flex: 1;
  }

  .dialog-title {
    align-self: center;
    padding: 0 10px;
    line-height: 44px;
  }

  .dialog-close {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    flex-direction: row;
    align-items: center;
    padding: 0 15px;
  }

  .dialog-close-plus {
    width: 16px;
    height: 2px;
    background-color: #666;
    border-radius: 2px;
    transform: rotate(45deg);
  }

  .dialog-close-rotate {
    position: absolute;
    transform: rotate(-45deg);
  }

  .uni-data-pickerview {
    flex: 1;
  }

  .icon-clear {
    display: flex;
    align-items: center;
  }

  /* #ifdef H5 */
  @media all and (min-width: 768px) {
    .uni-data-tree-cover {
      background-color: transparent;
    }

    .uni-data-tree-dialog {
      position: absolute;
      top: 55px;
      height: auto;
      min-height: 400px;
      max-height: 50vh;
      background-color: #fff;
      border: 1px solid #EBEEF5;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      overflow: unset;
    }

    .dialog-caption {
      display: none;
    }
  }
  /* #endif */
</style>
