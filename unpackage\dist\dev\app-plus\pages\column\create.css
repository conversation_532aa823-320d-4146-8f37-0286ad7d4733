
.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 0.625rem 0.9375rem;
		padding-top: var(--status-bar-height);
}
.header-left, .header-right {
		width: 3.75rem;
}
.header-back {
		color: #FFFFFF;
		font-size: 0.875rem;
}
.header-title {
		color: #FFFFFF;
		font-size: 1.125rem;
		font-weight: bold;
}
.form-container {
		flex: 1;
		padding: 0.9375rem;
}
.form-item {
		margin-bottom: 0.9375rem;
}
.input-container {
		width: 100%;
		border: 1px solid #DDDDDD;
		border-radius: 0.25rem;
		background-color: #FFFFFF;
		padding: 0;
		margin: 0;
}
.form-label {
		font-size: 1rem;
		font-weight: bold;
		margin-bottom: 0.3125rem;
		display: block;
}
.form-input {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem;
		font-size: 0.875rem;
		width: 100%;
		min-width: 15.625rem;
		min-height: 2.5rem;
		box-sizing: border-box;
		margin: 0;
		border: none;
}
.form-error {
		color: #FF0000;
		font-size: 0.75rem;
		margin-top: 0.3125rem;
}
.radio-group {
		display: flex;
		flex-direction: column;
		gap: 0.625rem;
}
.radio-item {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem;
		display: flex;
		align-items: center;
}
.radio-item-selected {
		background-color: #E1F0FF;
		border: 0.0625rem solid #007AFF;
}
.radio-text {
		font-size: 0.875rem;
}
.checkbox-group {
		display: flex;
		flex-direction: column;
		gap: 0.625rem;
}
.checkbox-item {
		display: flex;
		align-items: center;
		gap: 0.625rem;
}
.checkbox {
		width: 1.25rem;
		height: 1.25rem;
		border-radius: 0.25rem;
		border: 0.0625rem solid #CCCCCC;
		background-color: #FFFFFF;
}
.checkbox-selected {
		background-color: #007AFF;
		border-color: #007AFF;
		position: relative;
}
.checkbox-selected::after {
		content: '';
		position: absolute;
		width: 0.625rem;
		height: 0.3125rem;
		border-left: 0.125rem solid #FFFFFF;
		border-bottom: 0.125rem solid #FFFFFF;
		transform: rotate(-45deg);
		top: 0.3125rem;
		left: 0.25rem;
}
.checkbox-text {
		font-size: 0.875rem;
}
.text-disabled {
		color: #999999;
}
.checkbox-disabled {
		opacity: 0.6;
}
.checkbox-tip {
		font-size: 0.6875rem;
		color: #999999;
		margin-left: 0.3125rem;
}
.foreign-key-options {
		background-color: #F8F8F8;
		border-radius: 0.25rem;
		padding: 0.625rem;
		margin-left: 1.25rem;
}
.picker-view {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem;
		font-size: 0.875rem;
}
.picker-placeholder {
		color: #999999;
}
.save-button {
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 1rem;
		padding: 0.625rem;
		border-radius: 0.25rem;
		margin-top: 0.9375rem;
}
