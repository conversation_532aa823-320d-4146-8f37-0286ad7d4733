
.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 0.625rem 0.9375rem;
		padding-top: var(--status-bar-height);
}
.header-left, .header-right {
		width: 3.75rem;
}
.header-back {
		color: #FFFFFF;
		font-size: 0.875rem;
}
.header-title {
		color: #FFFFFF;
		font-size: 1.125rem;
		font-weight: bold;
		max-width: 12.5rem;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
}
.form-container {
		flex: 1;
		padding: 0.9375rem;
}
.empty-tip, .loading-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 9.375rem;
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		color: #999999;
		font-size: 0.875rem;
}
.loading-tip {
		background-color: #F8F8F8;
}
.form-item {
		margin-bottom: 0.9375rem;
}
.form-label {
		font-size: 0.875rem;
		font-weight: bold;
		margin-bottom: 0.3125rem;
		display: block;
}
.input-container {
		width: 100%;
		border: 1px solid #DDDDDD;
		border-radius: 0.25rem;
		background-color: #FFFFFF;
		padding: 0;
		margin: 0;
}
.form-input, .picker-view {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem;
		font-size: 0.875rem;
		width: 100%;
		min-width: 15.625rem;
		min-height: 2.5rem;
		box-sizing: border-box;
		margin: 0;
		border: none;
}
.form-input:disabled {
		background-color: #F0F0F0;
		color: #999999;
}
.picker-placeholder {
		color: #999999;
}
.form-error {
		color: #FF0000;
		font-size: 0.75rem;
		margin-top: 0.3125rem;
}
.save-button {
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 1rem;
		padding: 0.625rem;
		border-radius: 0.25rem;
		margin-top: 0.9375rem;
}
.save-button[disabled] {
		background-color: #CCCCCC;
		color: #FFFFFF;
		opacity: 0.7;
}
