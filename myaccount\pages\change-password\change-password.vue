<template>
  <view class="container">
    <view class="header">
      <view class="header-content">
        <text class="title">修改密码</text>
      </view>
    </view>

    <view class="form-container">
      <view class="input-group">
        <text class="input-label">旧密码</text>
        <input 
          type="password" 
          v-model="oldPassword" 
          class="input-field" 
          placeholder="请输入旧密码"
        />
      </view>

      <view class="input-group">
        <text class="input-label">新密码</text>
        <input 
          type="password" 
          v-model="newPassword" 
          class="input-field" 
          placeholder="请输入新密码"
        />
      </view>

      <view class="input-group">
        <text class="input-label">确认密码</text>
        <input 
          type="password" 
          v-model="confirmPassword" 
          class="input-field" 
          placeholder="请再次输入新密码"
        />
      </view>

      <button 
        class="submit-button" 
        @tap="handleSubmit" 
        :disabled="isLoading"
      >
        {{ isLoading ? '提交中...' : '确认修改' }}
      </button>
    </view>
  </view>
</template>

<script>
import { ref } from 'vue'
import { useStore } from 'vuex'

export default {
  setup() {
    const store = useStore()
    const oldPassword = ref('')
    const newPassword = ref('')
    const confirmPassword = ref('')
    const isLoading = ref(false)

    // 处理密码修改提交
    const handleSubmit = async () => {
      if (!oldPassword.value || !newPassword.value || !confirmPassword.value) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return
      }

      if (newPassword.value !== confirmPassword.value) {
        uni.showToast({
          title: '两次输入的新密码不一致',
          icon: 'none'
        })
        return
      }

      if (newPassword.value.length < 6) {
        uni.showToast({
          title: '新密码长度不能少于6位',
          icon: 'none'
        })
        return
      }

      isLoading.value = true
      try {
        const result = await store.dispatch('changePassword', {
          oldPassword: oldPassword.value,
          newPassword: newPassword.value
        })

        if (result) {
          uni.showToast({
            title: '密码修改成功',
            icon: 'success'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      } catch (error) {
        uni.showToast({
          title: error.message || '密码修改失败',
          icon: 'none'
        })
      } finally {
        isLoading.value = false
      }
    }

    return {
      oldPassword,
      newPassword,
      confirmPassword,
      isLoading,
      handleSubmit
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: #4a90e2;
  padding: 8px;
  color: white;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 960px;
    margin: 0 auto;
    padding: 0 8px;
    position: relative;
  }

  .title {
    color: white;
    font-size: 16px;
    font-weight: 500;
  }
}

.form-container {
  margin: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.input-group {
  margin-bottom: 16px;

  .input-label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
  }

  .input-field {
    width: 100%;
    height: 40px;
    padding: 0 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
    transition: border-color 0.3s ease;

    &:focus {
      border-color: #4a90e2;
      outline: none;
    }

    &::placeholder {
      color: #999;
    }
  }
}

.submit-button {
  width: 100%;
  height: 40px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  margin-top: 8px;
  transition: background-color 0.3s ease;

  &:active {
    background: #357abd;
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
}
</style> 