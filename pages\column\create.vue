<template>
	<view class="content">
		<AppHeader title="创建列" subtitle="列定义" />

		<view class="form-container">
			<!-- 列名 -->
			<view class="form-item">
				<text class="form-label">列名</text>
				<view class="input-container">
					<input
						class="form-input"
						type="text"
						:value="columnName"
						@input="columnName = $event.detail.value"
						placeholder="请输入列名（英文字母和下划线）"
					/>
				</view>
				<text v-if="columnNameError" class="form-error">{{ columnNameError }}</text>
			</view>

			<!-- 数据类型 -->
			<view class="form-item">
				<text class="form-label">数据类型</text>
				<view class="radio-group">
					<view
						class="radio-item"
						:class="{ 'radio-item-selected': columnType === 'TEXT' }"
						@click="columnType = 'TEXT'"
					>
						<text class="radio-text">文本 (TEXT)</text>
					</view>
					<view
						class="radio-item"
						:class="{ 'radio-item-selected': columnType === 'INTEGER' }"
						@click="columnType = 'INTEGER'"
					>
						<text class="radio-text">整数 (INTEGER)</text>
					</view>
					<view
						class="radio-item"
						:class="{ 'radio-item-selected': columnType === 'REAL' }"
						@click="columnType = 'REAL'"
					>
						<text class="radio-text">小数 (REAL)</text>
					</view>
					<view
						class="radio-item"
						:class="{ 'radio-item-selected': columnType === 'BLOB' }"
						@click="columnType = 'BLOB'"
					>
						<text class="radio-text">二进制 (BLOB)</text>
					</view>
				</view>
			</view>

			<!-- 列属性 -->
			<view class="form-item">
				<text class="form-label">列属性</text>
				<view class="checkbox-group">
					<view class="checkbox-item" @click="!isEditMode && (isPrimaryKey = !isPrimaryKey)" :class="{ 'checkbox-disabled': isEditMode }">
						<view class="checkbox" :class="{ 'checkbox-selected': isPrimaryKey, 'checkbox-disabled': isEditMode }"></view>
						<text class="checkbox-text" :class="{ 'text-disabled': isEditMode }">主键</text>
						<text v-if="isEditMode" class="checkbox-tip">(编辑模式下不可用)</text>
					</view>
					<view class="checkbox-item" @click="isNotNull = !isNotNull">
						<view class="checkbox" :class="{ 'checkbox-selected': isNotNull }"></view>
						<text class="checkbox-text">不允许为空</text>
					</view>
					<view class="checkbox-item" @click="isUnique = !isUnique">
						<view class="checkbox" :class="{ 'checkbox-selected': isUnique }"></view>
						<text class="checkbox-text">唯一值</text>
					</view>
					<view class="checkbox-item" @click="toggleForeignKey">
						<view class="checkbox" :class="{ 'checkbox-selected': isForeignKey }"></view>
						<text class="checkbox-text">外键关联</text>
					</view>
				</view>
			</view>

			<!-- 外键关联选项 -->
			<view v-if="isForeignKey" class="form-item foreign-key-options">
				<view class="form-item">
					<text class="form-label">关联表</text>
					<picker
						:value="referenceTableIndex"
						:range="tables.map(t => t.name)"
						@change="onReferenceTableChange"
					>
						<view class="picker-view">
							<text v-if="referenceTableIndex >= 0">{{ tables[referenceTableIndex].name }}</text>
							<text v-else class="picker-placeholder">请选择关联表</text>
						</view>
					</picker>
				</view>

				<view class="form-item">
					<text class="form-label">关联列</text>
					<picker
						:value="referenceColumnIndex"
						:range="referenceColumns.map(c => c.name)"
						:disabled="referenceColumns.length === 0"
						@change="onReferenceColumnChange"
					>
						<view class="picker-view">
							<text v-if="referenceColumnIndex >= 0">{{ referenceColumns[referenceColumnIndex].name }}</text>
							<text v-else class="picker-placeholder">
								{{ referenceColumns.length === 0 ? '没有可用的列' : '请选择关联列' }}
							</text>
						</view>
					</picker>
				</view>
			</view>

			<!-- 保存按钮 -->
			<button class="save-button" @click="saveColumn">保存列</button>
		</view>
	</view>
</template>

<script>
	import { getAllTables, getTableColumns } from '@/utils/sqlite.js';
	import AppHeader from '@/components/AppHeader.vue';

	export default {
		components: {
			AppHeader
		},
		data() {
			return {
				columnName: '',
				columnType: 'TEXT',
				isPrimaryKey: false,
				isNotNull: false,
				isUnique: false,
				isForeignKey: false,
				tables: [],
				referenceTableIndex: -1,
				referenceColumns: [],
				referenceColumnIndex: -1,
				columnNameError: '',
				isEditMode: false // 是否为编辑表结构模式
			}
		},
		onLoad(options) {
			// 检查是否为编辑表结构模式
			this.isEditMode = options.editMode === 'true';

			// 加载所有表
			this.loadTables();
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 加载所有表
			async loadTables() {
				try {
					const tables = await getAllTables();
					this.tables = tables;

					// 如果没有表，禁用外键选项
					if (tables.length === 0) {
						this.isForeignKey = false;
					}
				} catch (e) {
					console.error('加载表失败', e);
					uni.showToast({
						title: '加载表失败',
						icon: 'none'
					});
				}
			},

			// 切换外键选项
			toggleForeignKey() {
				if (this.tables.length === 0) {
					uni.showToast({
						title: '没有可用的表进行关联',
						icon: 'none'
					});
					return;
				}

				this.isForeignKey = !this.isForeignKey;

				// 如果启用外键，加载第一个表的列
				if (this.isForeignKey && this.tables.length > 0 && this.referenceTableIndex === -1) {
					this.referenceTableIndex = 0;
					this.loadReferenceColumns(this.tables[0].id);
				}
			},

			// 关联表变更
			onReferenceTableChange(e) {
				const index = e.detail.value;
				this.referenceTableIndex = index;
				this.referenceColumnIndex = -1;
				this.loadReferenceColumns(this.tables[index].id);
			},

			// 关联列变更
			onReferenceColumnChange(e) {
				this.referenceColumnIndex = e.detail.value;
			},

			// 加载关联表的列
			async loadReferenceColumns(tableId) {
				try {
					const columns = await getTableColumns(tableId);
					this.referenceColumns = columns;
				} catch (e) {
					console.error('加载列失败', e);
					uni.showToast({
						title: '加载列失败',
						icon: 'none'
					});
				}
			},

			// 验证列名
			validateColumnName() {
				if (!this.columnName) {
					this.columnNameError = '列名不能为空';
					return false;
				}

				if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(this.columnName)) {
					this.columnNameError = '列名只能包含字母、数字和下划线，且必须以字母开头';
					return false;
				}

				this.columnNameError = '';
				return true;
			},

			// 保存列
			saveColumn() {
				// 验证列名
				if (!this.validateColumnName()) {
					return;
				}

				// 验证外键关联
				if (this.isForeignKey) {
					if (this.referenceTableIndex === -1) {
						uni.showToast({
							title: '请选择关联表',
							icon: 'none'
						});
						return;
					}

					if (this.referenceColumnIndex === -1) {
						uni.showToast({
							title: '请选择关联列',
							icon: 'none'
						});
						return;
					}
				}

				// 创建列对象
				const column = {
					name: this.columnName,
					type: this.columnType,
					isPrimaryKey: this.isPrimaryKey,
					isNotNull: this.isNotNull,
					isUnique: this.isUnique,
					isForeignKey: this.isForeignKey,
					referenceTableId: this.isForeignKey ? this.tables[this.referenceTableIndex].id : null,
					referenceColumnId: this.isForeignKey ? this.referenceColumns[this.referenceColumnIndex].id : null
				};

				// 返回列数据
				const eventChannel = this.getOpenerEventChannel();
				eventChannel.emit('columnCreated', column);

				// 返回上一页
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	@import '@/static/styles/form.css';

	/* 页面特定样式 */
	.input-container {
		border: 1px solid #DDDDDD;
	}

	.form-input {
		min-width: 500rpx;
		min-height: 80rpx;
		border: none;
	}

	.form-label {
		font-size: 32rpx;
	}

	.radio-group {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.radio-item {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx;
		display: flex;
		align-items: center;
	}

	.radio-item-selected {
		background-color: #E1F0FF;
		border: 2rpx solid #007AFF;
	}

	.radio-text {
		font-size: 28rpx;
	}

	.checkbox-group {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.checkbox-item {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.checkbox {
		width: 40rpx;
		height: 40rpx;
		border-radius: 8rpx;
		border: 2rpx solid #CCCCCC;
		background-color: #FFFFFF;
	}

	.checkbox-selected {
		background-color: #007AFF;
		border-color: #007AFF;
		position: relative;
	}

	.checkbox-selected::after {
		content: '';
		position: absolute;
		width: 20rpx;
		height: 10rpx;
		border-left: 4rpx solid #FFFFFF;
		border-bottom: 4rpx solid #FFFFFF;
		transform: rotate(-45deg);
		top: 10rpx;
		left: 8rpx;
	}

	.checkbox-text {
		font-size: 28rpx;
	}

	.text-disabled {
		color: #999999;
	}

	.checkbox-disabled {
		opacity: 0.6;
	}

	.checkbox-tip {
		font-size: 22rpx;
		color: #999999;
		margin-left: 10rpx;
	}

	.foreign-key-options {
		background-color: #F8F8F8;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-left: 40rpx;
	}

	.picker-view {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
	}

	.picker-placeholder {
		color: #999999;
	}

	.save-button {
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 32rpx;
		padding: 20rpx;
		border-radius: 8rpx;
		margin-top: 30rpx;
	}
</style>
