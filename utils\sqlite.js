/**
 * SQLite数据库工具类
 */
import { DB_CONFIG } from '@/config/index.js';

// 数据库名称和路径
const DB_NAME = DB_CONFIG.name;
const DB_PATH = DB_CONFIG.path;

// 管理表名称
const TABLE_TABLES = DB_CONFIG.systemTables.tables;
const TABLE_COLUMNS = DB_CONFIG.systemTables.columns;

// 数据库状态
let isDbOpen = false;

// 错误消息常量
const ERROR_MESSAGES = {
  DB_OPEN_FAILED: '数据库打开失败',
  DB_CLOSE_FAILED: '数据库关闭失败',
  TABLE_CREATE_FAILED: '创建表失败',
  TABLE_DROP_FAILED: '删除表失败',
  COLUMN_ADD_FAILED: '添加列失败',
  COLUMN_DELETE_FAILED: '删除列失败',
  DATA_INSERT_FAILED: '插入数据失败',
  DATA_UPDATE_FAILED: '更新数据失败',
  DATA_DELETE_FAILED: '删除数据失败',
  QUERY_FAILED: '查询失败'
};

/**
 * 打开数据库
 * @returns {Promise<boolean>} 是否成功
 */
export function openDatabase() {
  return new Promise((resolve, reject) => {
    // 如果数据库已经打开，直接返回成功
    if (isDbOpen) {
      console.log('数据库已经打开，无需重复打开');
      // 确保外键约束已启用
      executeSql('PRAGMA foreign_keys = ON')
        .then(() => {
          console.log('外键约束已启用');
          resolve(true);
        })
        .catch(e => {
          console.error('启用外键约束失败', e);
          resolve(true); // 仍然继续，不中断流程
        });
      return;
    }

    // 尝试关闭数据库，忽略错误
    try {
      plus.sqlite.closeDatabase({
        name: DB_NAME,
        success() {
          console.log('预防性关闭数据库成功');
        },
        fail() {
          // 忽略错误
        }
      });
    } catch (e) {
      // 忽略错误
    }

    // 打开数据库
    plus.sqlite.openDatabase({
      name: DB_NAME,
      path: DB_PATH,
      success(e) {
        console.log('数据库打开成功');
        isDbOpen = true;

        // 启用外键约束
        executeSql('PRAGMA foreign_keys = ON')
          .then(() => {
            console.log('外键约束已启用');
            resolve(true);
          })
          .catch(e => {
            console.error('启用外键约束失败', e);
            resolve(true); // 仍然继续，不中断流程
          });
      },
      fail(e) {
        console.error(ERROR_MESSAGES.DB_OPEN_FAILED, e);
        reject(e);
      }
    });
  });
}

/**
 * 关闭数据库
 * @returns {Promise<boolean>} 是否成功
 */
export function closeDatabase() {
  return new Promise((resolve, reject) => {
    // 如果数据库未打开，直接返回成功
    if (!isDbOpen) {
      console.log('数据库未打开，无需关闭');
      resolve(true);
      return;
    }

    plus.sqlite.closeDatabase({
      name: DB_NAME,
      success(e) {
        console.log('数据库关闭成功');
        isDbOpen = false;
        resolve(true);
      },
      fail(e) {
        console.error(ERROR_MESSAGES.DB_CLOSE_FAILED, e);
        reject(e);
      }
    });
  });
}

/**
 * 执行SQL语句
 * @param {string} sql SQL语句
 * @returns {Promise<any>} 执行结果
 */
export function executeSql(sql) {
  return new Promise((resolve, reject) => {
    plus.sqlite.executeSql({
      name: DB_NAME,
      sql: sql,
      success(e) {
        resolve(e);
      },
      fail(e) {
        console.error('SQL执行失败', sql, e);
        reject(e);
      }
    });
  });
}

/**
 * 查询SQL语句
 * @param {string} sql SQL语句
 * @returns {Promise<Array>} 查询结果
 */
export function selectSql(sql) {
  return new Promise((resolve, reject) => {
    plus.sqlite.selectSql({
      name: DB_NAME,
      sql: sql,
      success(e) {
        resolve(e);
      },
      fail(e) {
        console.error('SQL查询失败', sql, e);
        reject(e);
      }
    });
  });
}

/**
 * 初始化系统表
 * @returns {Promise<boolean>} 是否成功
 */
export async function initSystemTables() {
  try {
    // 创建表信息表
    await executeSql(`
      CREATE TABLE IF NOT EXISTS ${TABLE_TABLES} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        created_at INTEGER NOT NULL
      )
    `);

    // 创建列信息表
    await executeSql(`
      CREATE TABLE IF NOT EXISTS ${TABLE_COLUMNS} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        is_primary_key INTEGER DEFAULT 0,
        is_not_null INTEGER DEFAULT 0,
        is_unique INTEGER DEFAULT 0,
        is_foreign_key INTEGER DEFAULT 0,
        reference_table_id INTEGER,
        reference_column_id INTEGER,
        order_index INTEGER DEFAULT 0,
        FOREIGN KEY (table_id) REFERENCES ${TABLE_TABLES}(id) ON DELETE CASCADE
      )
    `);

    // 检查并更新系统表结构
    await migrateSystemTables();

    return true;
  } catch (e) {
    console.error('初始化系统表失败', e);
    return false;
  }
}

/**
 * 迁移系统表结构
 * 检查并添加新的列
 */
export async function migrateSystemTables() {
  try {
    console.log('开始检查系统表结构...');

    // 检查sys_columns表是否有is_hidden列
    const hasIsHidden = await checkColumnExists(TABLE_COLUMNS, 'is_hidden');
    if (!hasIsHidden) {
      console.log('添加is_hidden列到sys_columns表');
      await executeSql(`ALTER TABLE ${TABLE_COLUMNS} ADD COLUMN is_hidden INTEGER DEFAULT 0`);
    }

    // 检查sys_columns表是否有options列
    const hasOptions = await checkColumnExists(TABLE_COLUMNS, 'options');
    if (!hasOptions) {
      console.log('添加options列到sys_columns表');
      await executeSql(`ALTER TABLE ${TABLE_COLUMNS} ADD COLUMN options TEXT`);
    }

    // 检查sys_columns表是否有default_value列
    const hasDefaultValue = await checkColumnExists(TABLE_COLUMNS, 'default_value');
    if (!hasDefaultValue) {
      console.log('添加default_value列到sys_columns表');
      await executeSql(`ALTER TABLE ${TABLE_COLUMNS} ADD COLUMN default_value TEXT`);
    }

    console.log('系统表结构检查完成');
    return true;
  } catch (e) {
    console.error('迁移系统表结构失败', e);
    return false;
  }
}

/**
 * 检查表中是否存在指定的列
 * @param {string} tableName 表名
 * @param {string} columnName 列名
 * @returns {Promise<boolean>} 是否存在
 */
async function checkColumnExists(tableName, columnName) {
  try {
    // 使用PRAGMA table_info查询表结构
    const columns = await selectSql(`PRAGMA table_info(${tableName})`);
    return columns.some(col => col.name === columnName);
  } catch (e) {
    console.error(`检查列 ${columnName} 是否存在失败`, e);
    return false;
  }
}

/**
 * 检查表是否存在
 * @param {string} tableName 表名
 * @returns {Promise<boolean>} 是否存在
 */
export async function checkTableExists(tableName) {
  try {
    const result = await selectSql(`SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'`);
    return result.length > 0;
  } catch (e) {
    console.error(`检查表 ${tableName} 是否存在失败`, e);
    return false;
  }
}

/**
 * 检查表是否存在于系统表中
 * @param {string} tableName 表名
 * @returns {Promise<boolean>} 是否存在
 */
export async function checkTableExistsInSystem(tableName) {
  try {
    const result = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name='${tableName}'`);
    return result.length > 0;
  } catch (e) {
    console.error(`检查表 ${tableName} 是否存在于系统表中失败`, e);
    return false;
  }
}

/**
 * 获取所有表
 * @returns {Promise<Array>} 表列表
 */
export async function getAllTables() {
  try {
    const tables = await selectSql(`SELECT * FROM ${TABLE_TABLES} ORDER BY created_at DESC`);
    return tables;
  } catch (e) {
    console.error('获取表列表失败', e);
    return [];
  }
}

/**
 * 获取表的所有列
 * @param {number} tableId 表ID
 * @returns {Promise<Array>} 列列表
 */
export async function getTableColumns(tableId) {
  try {
    const columns = await selectSql(`SELECT * FROM ${TABLE_COLUMNS} WHERE table_id = ${tableId} ORDER BY order_index ASC`);
    return columns;
  } catch (e) {
    console.error('获取列列表失败', e);
    return [];
  }
}

/**
 * 创建新表
 * @param {string} tableName 表名
 * @param {string} description 表描述
 * @param {Array} columns 列定义
 * @returns {Promise<number>} 表ID
 */
export async function createTable(tableName, description, columns) {
  try {
    // 1. 插入表信息
    await executeSql(`
      INSERT INTO ${TABLE_TABLES} (name, description, created_at)
      VALUES ('${tableName}', '${description}', ${Date.now()})
    `);

    // 2. 获取表ID
    const tables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = '${tableName}'`);
    if (!tables || tables.length === 0) {
      throw new Error('创建表失败');
    }

    const tableId = tables[0].id;

    // 3. 插入列信息
    for (let i = 0; i < columns.length; i++) {
      const column = columns[i];
      await executeSql(`
        INSERT INTO ${TABLE_COLUMNS} (
          table_id, name, type, is_primary_key, is_not_null, is_unique,
          is_foreign_key, reference_table_id, reference_column_id, order_index
        ) VALUES (
          ${tableId}, '${column.name}', '${column.type}', ${column.isPrimaryKey ? 1 : 0},
          ${column.isNotNull ? 1 : 0}, ${column.isUnique ? 1 : 0}, ${column.isForeignKey ? 1 : 0},
          ${column.referenceTableId || 'NULL'}, ${column.referenceColumnId || 'NULL'}, ${i}
        )
      `);
    }

    // 4. 创建用户表
    const createTableSql = buildCreateTableSql(tableName, columns);
    await executeSql(createTableSql);

    return tableId;
  } catch (e) {
    console.error('创建表失败', e);
    throw e;
  }
}

/**
 * 删除表
 * @param {number} tableId 表ID
 * @param {string} tableName 表名
 * @returns {Promise<boolean>} 是否成功
 */
export async function dropTable(tableId, tableName) {
  try {
    // 1. 删除用户表
    await executeSql(`DROP TABLE IF EXISTS ${tableName}`);

    // 2. 删除表信息
    await executeSql(`DELETE FROM ${TABLE_TABLES} WHERE id = ${tableId}`);

    // 3. 删除表的列信息
    await executeSql(`DELETE FROM ${TABLE_COLUMNS} WHERE table_id = ${tableId}`);

    return true;
  } catch (e) {
    console.error('删除表失败', e);
    return false;
  }
}

/**
 * 删除所有同名表的系统记录（不删除实际表和数据）
 * @param {string} tableName 表名
 * @returns {Promise<boolean>} 是否成功
 */
export async function dropAllTablesWithName(tableName) {
  try {
    console.log(`开始删除所有名为 ${tableName} 的表的系统记录`);

    // 1. 获取所有同名表
    const tables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = '${tableName}'`);
    console.log(`找到 ${tables.length} 个名为 ${tableName} 的表`);

    // 2. 删除所有同名表的系统记录
    for (const table of tables) {
      // 删除表的列记录
      await executeSql(`DELETE FROM ${TABLE_COLUMNS} WHERE table_id = ${table.id}`);
      console.log(`已删除表 ${tableName} 的列记录，ID: ${table.id}`);

      // 删除表记录
      await executeSql(`DELETE FROM ${TABLE_TABLES} WHERE id = ${table.id}`);
      console.log(`已删除表 ${tableName} 的系统记录，ID: ${table.id}`);
    }

    // 注意：我们不再删除实际的表，这样可以保留表中的数据
    console.log(`保留实际表 ${tableName} 及其数据`);

    return true;
  } catch (e) {
    console.error(`删除所有名为 ${tableName} 的表的系统记录失败`, e);
    return false;
  }
}

/**
 * 构建创建表的SQL语句
 * @param {string} tableName 表名
 * @param {Array} columns 列定义
 * @returns {string} SQL语句
 */
function buildCreateTableSql(tableName, columns) {
  let sql = `CREATE TABLE IF NOT EXISTS ${tableName} (`;

  const columnDefs = columns.map(column => {
    let def = `${column.name} ${column.type}`;

    if (column.isPrimaryKey) {
      def += ' PRIMARY KEY';
      if (column.type === 'INTEGER') {
        def += ' AUTOINCREMENT';
      }
    }

    if (column.isNotNull) {
      def += ' NOT NULL';
    }

    if (column.isUnique) {
      def += ' UNIQUE';
    }

    // 添加默认值
    if (column.defaultValue) {
      // 对于日期时间函数，不需要额外的引号
      if (column.defaultValue.includes("datetime(") || column.defaultValue.includes("strftime(")) {
        def += ` DEFAULT ${column.defaultValue}`;
      } else {
        // 其他类型的默认值需要加引号
        def += ` DEFAULT '${column.defaultValue}'`;
      }
    }

    return def;
  });

  sql += columnDefs.join(', ');

  // 添加外键约束
  const foreignKeys = columns.filter(column => column.isForeignKey && column.referenceTableId && column.referenceColumnId);
  if (foreignKeys.length > 0) {
    for (const fk of foreignKeys) {
      // 这里需要查询引用表和列的名称
      // 简化处理，实际应用中需要查询
      sql += `, FOREIGN KEY (${fk.name}) REFERENCES table_${fk.referenceTableId}(column_${fk.referenceColumnId})`;
    }
  }

  sql += ')';
  return sql;
}

/**
 * 检查文件名是否存在于文档表中
 * @param {string} fileName 文件名
 * @returns {Promise<boolean>} 是否存在
 */
export async function checkFileNameExists(fileName) {
  try {
    const result = await selectSql(`SELECT COUNT(*) as count FROM documents WHERE fileName = '${fileName.replace(/'/g, "''")}'`);
    return result && result.length > 0 && result[0].count > 0;
  } catch (e) {
    console.error('检查文件名是否存在失败', e);
    return false;
  }
}

/**
 * 创建文档记录
 * @param {string} fileName 文件名
 * @returns {Promise<number|boolean>} 插入的行ID或成功状态
 */
export async function createDocumentRecord(fileName) {
  try {
    // 创建一个基本的文档记录，使用"待完善"作为默认值
    const documentData = {
      fileName: fileName,
      category: '待完善',
      documentNumber: '待完善',
      publishDate: '',
      implementDate: '',
      publishingUnit: '待完善'
    };

    console.log(`为文件名 ${fileName} 创建文档记录，使用默认值:`, documentData);

    // 插入文档记录
    return await insertData('documents', documentData);
  } catch (e) {
    console.error('创建文档记录失败', e);
    return false;
  }
}

/**
 * 插入数据
 * @param {string} tableName 表名
 * @param {Object} data 数据
 * @returns {Promise<number|boolean>} 成功时返回插入的行ID，失败时返回false
 */
export async function insertData(tableName, data) {
  try {
    // 创建数据的副本，避免修改原始数据
    const insertData = { ...data };

    // 如果是条文表，检查文件名是否存在于文档表中
    if (tableName === 'articles' && insertData.fileName) {
      const fileNameExists = await checkFileNameExists(insertData.fileName);
      if (!fileNameExists) {
        console.log(`文件名 ${insertData.fileName} 不存在于文档表中，自动创建文档记录`);
        const result = await createDocumentRecord(insertData.fileName);
        if (!result) {
          console.error(`为文件名 ${insertData.fileName} 创建文档记录失败`);
          throw new Error(`为文件名 ${insertData.fileName} 创建文档记录失败`);
        }
        console.log(`为文件名 ${insertData.fileName} 创建文档记录成功，ID: ${result}`);
      }
    }

    // 如果是文档表或条文表，自动添加createTime和updateTime字段
    if (tableName === 'documents' || tableName === 'articles') {
      // 检查表结构中是否有createTime和updateTime字段
      const tableInfo = await selectSql(`PRAGMA table_info(${tableName})`);
      const hasCreateTime = tableInfo.some(col => col.name === 'createTime');
      const hasUpdateTime = tableInfo.some(col => col.name === 'updateTime');

      // 使用当前时间设置createTime和updateTime字段
      if (hasCreateTime && !insertData.createTime) {
        insertData.createTime = `datetime('now', 'localtime')`;
      }

      if (hasUpdateTime && !insertData.updateTime) {
        insertData.updateTime = `datetime('now', 'localtime')`;
      }
    }

    const columns = Object.keys(insertData);
    const values = Object.entries(insertData).map(([key, value]) => {
      // 如果值是日期时间函数，直接使用函数
      if (typeof value === 'string' && (value.includes("datetime(") || value.includes("strftime("))) {
        return value;
      } else if (typeof value === 'string') {
        // 对字符串进行转义，防止SQL注入
        return `'${value.replace(/'/g, "''")}'`;
      } else if (value === null || value === undefined) {
        return 'NULL';
      } else {
        return value;
      }
    });

    // 构建SQL语句
    const sql = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')})`;
    console.log(`插入数据SQL: ${sql}`);
    await executeSql(sql);

    // 获取最后插入的行ID
    const result = await selectSql('SELECT last_insert_rowid() as rowid');
    if (result && result.length > 0 && result[0].rowid) {
      console.log(`数据插入成功，行ID: ${result[0].rowid}`);
      return result[0].rowid;
    }

    return true;
  } catch (e) {
    console.error('插入数据失败', e);
    return false;
  }
}

/**
 * 查询表数据
 * @param {string} tableName 表名
 * @returns {Promise<Array>} 数据列表
 */
export async function queryTableData(tableName) {
  try {
    // 使用rowid作为别名，确保它在结果中可用
    const data = await selectSql(`SELECT rowid as rowid, * FROM ${tableName}`);

    // 检查数据是否包含rowid
    if (data && data.length > 0) {
      const hasRowId = data[0].hasOwnProperty('rowid');
      console.log(`查询表 ${tableName} 数据，是否包含rowid: ${hasRowId}`);

      // 如果没有rowid，尝试添加
      if (!hasRowId) {
        console.warn(`表 ${tableName} 的查询结果缺少rowid，尝试手动添加`);

        // 尝试使用ROWID查询
        try {
          const rowIds = await selectSql(`SELECT ROWID FROM ${tableName}`);

          // 如果能获取到ROWID，将其添加到数据中
          if (rowIds && rowIds.length === data.length) {
            for (let i = 0; i < data.length; i++) {
              data[i].rowid = rowIds[i].ROWID;
            }
            console.log(`已手动添加rowid到表 ${tableName} 的数据中`);
          }
        } catch (rowIdError) {
          console.error(`尝试获取表 ${tableName} 的ROWID失败`, rowIdError);
        }
      }
    }

    return data;
  } catch (e) {
    console.error('查询数据失败', e);
    return [];
  }
}

/**
 * 更新表中的一行数据
 * @param {string} tableName 表名
 * @param {number} rowId 行ID
 * @param {Object} data 更新的数据
 * @returns {Promise<boolean>} 是否成功
 */
export async function updateTableRow(tableName, rowId, data) {
  try {
    // 创建数据的副本，避免修改原始数据
    const updatedData = { ...data };

    // 如果是文档表或条文表，自动更新updateTime字段
    if (tableName === 'documents' || tableName === 'articles') {
      // 使用当前时间更新updateTime字段
      updatedData.updateTime = `datetime('now', 'localtime')`;
    }

    // 构建SET子句
    const setClause = Object.entries(updatedData)
      .map(([key, value]) => {
        // 如果值是日期时间函数，不需要加引号
        if (typeof value === 'string' && (value.includes("datetime(") || value.includes("strftime("))) {
          return `${key} = ${value}`;
        } else if (typeof value === 'string') {
          return `${key} = '${value}'`;
        }
        return `${key} = ${value}`;
      })
      .join(', ');

    // 执行更新
    await executeSql(`UPDATE ${tableName} SET ${setClause} WHERE rowid = ${rowId}`);
    console.log(`更新表 ${tableName} 中的数据，行ID: ${rowId}`);
    return true;
  } catch (e) {
    console.error('更新数据失败', e);
    return false;
  }
}

/**
 * 删除表中的一行数据
 * @param {string} tableName 表名
 * @param {number} rowId 行ID
 * @returns {Promise<boolean>} 是否成功
 */
export async function deleteTableRow(tableName, rowId) {
  try {
    await executeSql(`DELETE FROM ${tableName} WHERE rowid = ${rowId}`);
    return true;
  } catch (e) {
    console.error('删除数据失败', e);
    return false;
  }
}

/**
 * 添加列到已有表
 * @param {number} tableId 表ID
 * @param {string} tableName 表名
 * @param {Object} column 列定义
 * @returns {Promise<boolean>} 是否成功
 */
export async function addColumnToTable(tableId, tableName, column) {
  try {
    // 1. 添加列到用户表
    const alterTableSql = `ALTER TABLE ${tableName} ADD COLUMN ${column.name} ${column.type}`;
    await executeSql(alterTableSql);

    // 2. 获取当前列的最大顺序
    const columns = await getTableColumns(tableId);
    const maxOrder = columns.length > 0 ? Math.max(...columns.map(c => c.order_index)) : -1;

    // 3. 添加列信息到系统表
    // 构建SQL语句，根据列是否存在来添加字段
    let sql = `
      INSERT INTO ${TABLE_COLUMNS} (
        table_id, name, type, is_primary_key, is_not_null, is_unique,
        is_foreign_key, reference_table_id, reference_column_id, order_index
      ) VALUES (
        ${tableId}, '${column.name}', '${column.type}', ${column.isPrimaryKey ? 1 : 0},
        ${column.isNotNull ? 1 : 0}, ${column.isUnique ? 1 : 0}, ${column.isForeignKey ? 1 : 0},
        ${column.referenceTableId || 'NULL'}, ${column.referenceColumnId || 'NULL'}, ${maxOrder + 1}
      )
    `;

    await executeSql(sql);

    // 获取刚插入的列ID
    const result = await selectSql(`SELECT id FROM ${TABLE_COLUMNS} WHERE table_id = ${tableId} AND name = '${column.name}'`);
    if (result && result.length > 0) {
      const columnId = result[0].id;

      // 更新额外的字段
      if (column.isHidden !== undefined) {
        await executeSql(`UPDATE ${TABLE_COLUMNS} SET is_hidden = ${column.isHidden ? 1 : 0} WHERE id = ${columnId}`);
      }

      if (column.options) {
        await executeSql(`UPDATE ${TABLE_COLUMNS} SET options = '${JSON.stringify(column.options)}' WHERE id = ${columnId}`);
      }

      if (column.defaultValue) {
        // 使用双引号包裹整个SQL语句，使用单引号包裹default_value值
        await executeSql(`UPDATE ${TABLE_COLUMNS} SET default_value = ${JSON.stringify(column.defaultValue)} WHERE id = ${columnId}`);
      }
    }

    return true;
  } catch (e) {
    console.error('添加列失败', e);
    return false;
  }
}

/**
 * 删除表中的列
 * @param {number} tableId 表ID
 * @param {string} tableName 表名
 * @param {number} columnId 列ID
 * @param {string} columnName 列名
 * @returns {Promise<boolean>} 是否成功
 */
export async function deleteColumnFromTable(tableId, tableName, columnId, columnName) {
  try {
    // 1. 创建新表（不包含要删除的列）
    const columns = await getTableColumns(tableId);
    if (columns.length <= 1) {
      throw new Error('表至少需要保留一列');
    }

    // 检查是否为主键列
    const isPrimaryKey = columns.find(c => c.id === columnId)?.is_primary_key === 1;
    if (isPrimaryKey) {
      throw new Error('不能删除主键列');
    }

    // 2. 创建临时表
    const tempTableName = `${tableName}_temp`;
    const remainingColumns = columns.filter(c => c.id !== columnId);

    // 构建创建临时表的SQL
    let createTempTableSql = `CREATE TABLE ${tempTableName} (`;
    const columnDefs = remainingColumns.map(column => {
      let def = `${column.name} ${column.type}`;

      if (column.is_primary_key === 1) {
        def += ' PRIMARY KEY';
        if (column.type === 'INTEGER') {
          def += ' AUTOINCREMENT';
        }
      }

      if (column.is_not_null === 1) {
        def += ' NOT NULL';
      }

      if (column.is_unique === 1) {
        def += ' UNIQUE';
      }

      // 添加默认值
      if (column.default_value) {
        // 对于日期时间函数，不需要额外的引号
        if (column.default_value.includes("datetime(") || column.default_value.includes("strftime(")) {
          def += ` DEFAULT ${column.default_value}`;
        } else {
          // 其他类型的默认值需要加引号
          def += ` DEFAULT '${column.default_value}'`;
        }
      }

      return def;
    });

    createTempTableSql += columnDefs.join(', ') + ')';
    await executeSql(createTempTableSql);

    // 3. 复制数据到临时表
    const columnNames = remainingColumns.map(c => c.name).join(', ');
    await executeSql(`INSERT INTO ${tempTableName} SELECT ${columnNames} FROM ${tableName}`);

    // 4. 删除原表
    await executeSql(`DROP TABLE ${tableName}`);

    // 5. 重命名临时表
    await executeSql(`ALTER TABLE ${tempTableName} RENAME TO ${tableName}`);

    // 6. 从系统表中删除列信息
    await executeSql(`DELETE FROM ${TABLE_COLUMNS} WHERE id = ${columnId}`);

    return true;
  } catch (e) {
    console.error('删除列失败', e);
    throw e;
  }
}

/**
 * 更新列顺序
 * @param {number} tableId 表ID
 * @param {Array} columns 列数组，包含id和新的order_index
 * @returns {Promise<boolean>} 是否成功
 */
export async function updateColumnsOrder(tableId, columns) {
  try {
    // 开始事务
    await executeSql('BEGIN TRANSACTION');

    // 更新每一列的顺序
    for (const column of columns) {
      await executeSql(`
        UPDATE ${TABLE_COLUMNS}
        SET order_index = ${column.order_index}
        WHERE id = ${column.id}
      `);
    }

    // 提交事务
    await executeSql('COMMIT');

    return true;
  } catch (e) {
    // 回滚事务
    try {
      await executeSql('ROLLBACK');
    } catch (rollbackError) {
      console.error('回滚事务失败', rollbackError);
    }

    console.error('更新列顺序失败', e);
    return false;
  }
}

/**
 * 清空数据库
 * 删除所有用户表和系统表中的记录，包括文档表和条文表中的数据
 * @returns {Promise<boolean>} 是否成功
 */
export async function clearDatabase() {
  try {
    console.log('开始清空数据库');

    // 1. 获取所有表
    const tables = await selectSql(`SELECT name FROM sqlite_master WHERE type='table' AND name NOT IN ('sqlite_sequence')`);
    console.log('数据库中的表:', tables.map(t => t.name));

    // 2. 开始事务
    await executeSql('BEGIN TRANSACTION');

    try {
      // 3. 删除所有表中的数据（包括文档表和条文表）
      for (const table of tables) {
        // 跳过SQLite内部表
        if (table.name.startsWith('sqlite_')) {
          continue;
        }

        // 删除表中的数据
        await executeSql(`DELETE FROM ${table.name}`);
        console.log(`已清空表 ${table.name} 中的数据`);
      }

      // 4. 重置所有表的自增ID
      await executeSql(`DELETE FROM sqlite_sequence`);
      console.log('已重置所有表的自增ID');

      // 5. 提交事务
      await executeSql('COMMIT');
      console.log('数据库清空成功（包括文档表和条文表中的数据）');

      // 6. 重新初始化系统表
      await initSystemTables();

      // 7. 确保系统表结构是最新的
      await migrateSystemTables();
      console.log('系统表已重新初始化并迁移');

      return true;
    } catch (e) {
      // 如果出错，回滚事务
      await executeSql('ROLLBACK');
      console.error('清空数据库失败，已回滚事务', e);
      throw e;
    }
  } catch (e) {
    console.error('清空数据库失败', e);
    return false;
  }
}

/**
 * 获取表的外键引用数据
 * @param {number} tableId 引用表ID
 * @param {number} columnId 引用列ID
 * @returns {Promise<Array>} 引用数据
 */
export async function getForeignKeyData(tableId, columnId) {
  try {
    // 1. 获取引用表信息
    const tables = await selectSql(`SELECT * FROM ${TABLE_TABLES} WHERE id = ${tableId}`);
    if (!tables || tables.length === 0) {
      return [];
    }

    // 2. 获取引用列信息
    const columns = await selectSql(`SELECT * FROM ${TABLE_COLUMNS} WHERE id = ${columnId}`);
    if (!columns || columns.length === 0) {
      return [];
    }

    const refTableName = tables[0].name;
    const refColumnName = columns[0].name;

    // 3. 查询引用数据
    const data = await selectSql(`SELECT ${refColumnName} FROM ${refTableName}`);
    return data.map(item => item[refColumnName]);
  } catch (e) {
    console.error('获取外键引用数据失败', e);
    return [];
  }
}
