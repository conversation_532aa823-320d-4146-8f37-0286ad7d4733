/**
 * 表格页面通用混入
 * 提供表格页面的通用方法和数据
 */

import { TableUtils } from '@/config/tables.js';

export default {
  data() {
    return {
      // 表格数据
      tableData: [],
      columns: [],
      isLoading: false,
      
      // 搜索和筛选
      searchText: '',
      activeFilters: {},
      
      // 批量操作
      selectedRows: [],
      isBatchMode: false,
      
      // 分页
      currentPage: 1,
      pageSize: 50,
      totalCount: 0,
      
      // 排序
      sortColumn: '',
      sortOrder: 'asc'
    };
  },
  
  computed: {
    // 过滤后的数据
    filteredData() {
      let data = this.tableData;
      
      // 搜索过滤
      if (this.searchText) {
        const searchLower = this.searchText.toLowerCase();
        data = data.filter(row => {
          return this.columns.some(column => {
            if (TableUtils.isHiddenColumn(column)) return false;
            const value = row[column.name];
            return value && value.toString().toLowerCase().includes(searchLower);
          });
        });
      }
      
      // 筛选条件过滤
      Object.keys(this.activeFilters).forEach(columnName => {
        const filterValue = this.activeFilters[columnName];
        if (filterValue) {
          data = data.filter(row => {
            const value = row[columnName];
            return value && value.toString().includes(filterValue);
          });
        }
      });
      
      return data;
    },
    
    // 可见列
    visibleColumns() {
      return this.columns.filter(column => !TableUtils.isHiddenColumn(column));
    },
    
    // 选中行数量
    selectedCount() {
      return this.selectedRows.length;
    }
  },
  
  methods: {
    // 获取表的中文显示名称
    getTableDisplayName(tableName) {
      return TableUtils.getTableDisplayName(tableName);
    },
    
    // 获取列的中文显示名称
    getColumnDisplayName(columnName) {
      return TableUtils.getColumnDisplayName(columnName);
    },
    
    // 获取列的CSS类名
    getColumnClass(columnName) {
      return TableUtils.getColumnClass(this.tableName, columnName);
    },
    
    // 获取列的样式
    getColumnStyle(columnName) {
      return TableUtils.getColumnStyle(this.tableName, columnName);
    },
    
    // 判断列是否隐藏
    isHiddenColumn(column) {
      return TableUtils.isHiddenColumn(column);
    },
    
    // 搜索
    onSearch() {
      // 搜索逻辑在computed中处理
      this.currentPage = 1;
    },
    
    // 清除搜索
    clearSearch() {
      this.searchText = '';
      this.onSearch();
    },
    
    // 添加筛选条件
    addFilter(columnName, value) {
      this.$set(this.activeFilters, columnName, value);
    },
    
    // 移除筛选条件
    removeFilter(columnName) {
      this.$delete(this.activeFilters, columnName);
    },
    
    // 清除所有筛选条件
    clearAllFilters() {
      this.activeFilters = {};
    },
    
    // 切换批量模式
    toggleBatchMode() {
      this.isBatchMode = !this.isBatchMode;
      if (!this.isBatchMode) {
        this.selectedRows = [];
      }
    },
    
    // 全选/取消全选
    toggleSelectAll() {
      if (this.selectedRows.length === this.filteredData.length) {
        this.selectedRows = [];
      } else {
        this.selectedRows = [...this.filteredData];
      }
    },
    
    // 切换行选中状态
    toggleRowSelection(row) {
      const index = this.selectedRows.findIndex(r => r.id === row.id);
      if (index > -1) {
        this.selectedRows.splice(index, 1);
      } else {
        this.selectedRows.push(row);
      }
    },
    
    // 判断行是否选中
    isRowSelected(row) {
      return this.selectedRows.some(r => r.id === row.id);
    },
    
    // 批量删除
    async batchDelete() {
      if (this.selectedRows.length === 0) {
        uni.showToast({
          title: '请选择要删除的数据',
          icon: 'none'
        });
        return;
      }
      
      const result = await new Promise((resolve) => {
        uni.showModal({
          title: '确认删除',
          content: `确定要删除选中的 ${this.selectedRows.length} 条数据吗？`,
          success: resolve
        });
      });
      
      if (!result.confirm) return;
      
      try {
        const ids = this.selectedRows.map(row => row.id);
        await this.deleteRows(ids);
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        this.selectedRows = [];
        this.isBatchMode = false;
        await this.loadData();
      } catch (error) {
        console.error('批量删除失败', error);
        uni.showToast({
          title: '删除失败',
          icon: 'none'
        });
      }
    },
    
    // 排序
    sort(columnName) {
      if (this.sortColumn === columnName) {
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortColumn = columnName;
        this.sortOrder = 'asc';
      }
      this.loadData();
    },
    
    // 导出数据
    async exportData() {
      try {
        const data = this.selectedRows.length > 0 ? this.selectedRows : this.filteredData;
        
        if (data.length === 0) {
          uni.showToast({
            title: '没有数据可导出',
            icon: 'none'
          });
          return;
        }
        
        // 调用导出API
        await this.performExport(data);
        
        uni.showToast({
          title: '导出成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('导出失败', error);
        uni.showToast({
          title: '导出失败',
          icon: 'none'
        });
      }
    },
    
    // 需要在具体页面中实现的方法
    async loadData() {
      throw new Error('loadData method must be implemented');
    },
    
    async deleteRows(ids) {
      throw new Error('deleteRows method must be implemented');
    },
    
    async performExport(data) {
      throw new Error('performExport method must be implemented');
    }
  }
};
