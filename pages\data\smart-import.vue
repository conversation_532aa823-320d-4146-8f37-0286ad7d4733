<template>
  <view class="container">
    <view class="header">
      <view class="header-left">
        <text class="header-back" @tap="goBack">返回</text>
      </view>
      <view class="header-title">智能输入</view>
      <view class="header-right"></view>
    </view>

    <view class="content">
      <view class="step-indicator">
        <view class="step" :class="{ active: currentStep >= 1 }">1. 选择文件</view>
        <view class="step" :class="{ active: currentStep >= 2 }">2. 处理内容</view>
        <view class="step" :class="{ active: currentStep >= 3 }">3. 导入数据</view>
      </view>

      <!-- 步骤1：选择文件 -->
      <view v-if="currentStep === 1" class="step-content">
        <view class="instruction">请选择一个Markdown文件进行智能处理</view>
        <button class="primary-button" @tap="selectFile">选择MD文件</button>
        <view v-if="selectedFile" class="file-info">
          <text class="file-name">已选择: {{ selectedFile.name }}</text>
          <text class="file-size">大小: {{ formatFileSize(selectedFile.size) }}</text>
        </view>
        <button v-if="selectedFile" class="primary-button" @tap="readFileContent">开始处理</button>
      </view>

      <!-- 步骤2：处理内容 -->
      <view v-if="currentStep === 2" class="step-content">
        <view class="processing-status">
          <text class="status-text">{{ processingStatus }}</text>
          <progress :percent="processingProgress" stroke-width="4" />
          <text class="progress-text">{{ processingProgress.toFixed(0) }}%</text>
        </view>
        <view v-if="processingError" class="error-message">
          <text>{{ processingError }}</text>
        </view>
        <button v-if="processingComplete" class="primary-button" @tap="previewResults">预览结果</button>
      </view>

      <!-- 步骤3：预览和导入 -->
      <view v-if="currentStep === 3" class="step-content">
        <view class="preview-header">
          <text class="preview-title">处理结果预览</text>
          <text class="preview-count">共 {{ processedItems.length }} 条数据</text>
        </view>
        <scroll-view class="preview-list" scroll-y>
          <view v-for="(item, index) in previewItems" :key="index" class="preview-item">
            <view class="preview-item-header">
              <text class="item-number">{{ index + 1 }}</text>
              <text class="item-type">{{ item.articleType }}</text>
            </view>
            <view class="preview-item-content">
              <text class="item-content">{{ item.articleContent }}</text>
            </view>
            <view class="preview-item-footer">
              <text class="item-keywords">关键词: {{ item.keywords }}</text>
            </view>
          </view>
        </scroll-view>
        <view class="import-actions">
          <button class="secondary-button" @tap="cancelImport">取消</button>
          <button class="primary-button" @tap="importData" :disabled="isImporting">
            {{ isImporting ? '导入中...' : '导入数据' }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed } from 'vue';
import { formatFileSize } from '@/utils/common.js';
import { insertData } from '@/utils/sqlite.js';

export default {
  setup() {
    // 状态变量
    const currentStep = ref(1);
    const selectedFile = ref(null);
    const fileContent = ref('');
    const processingStatus = ref('准备处理文件...');
    const processingProgress = ref(0);
    const processingError = ref('');
    const processingComplete = ref(false);
    const processedItems = ref([]);
    const isImporting = ref(false);
    const importProgress = ref(0);
    const importComplete = ref(false);
    const fileName = ref('');

    // 计算属性：预览显示的条目（最多显示10条）
    const previewItems = computed(() => {
      return processedItems.value.slice(0, 10);
    });

    // 返回上一页
    const goBack = () => {
      uni.navigateBack();
    };

    // 选择文件
    const selectFile = () => {
      // 在Android平台上，使用原生方法从Download文件夹选择文件
      if (uni.getSystemInfoSync().platform === 'android') {
        selectFileFromDownload();
      } else {
        // 在其他平台上，显示提示
        uni.showModal({
          title: '提示',
          content: '当前平台暂不支持直接选择文件，请使用Android设备。',
          showCancel: false
        });
      }
    };

    // 从Download文件夹选择文件
    const selectFileFromDownload = async () => {
      try {
        // 请求存储权限
        await requestStoragePermission();

        // 使用原生Java方法获取Download目录
        const Environment = plus.android.importClass("android.os.Environment");
        const File = plus.android.importClass("java.io.File");

        // 获取Download目录路径 - 使用更可靠的方法
        let downloadPath = '';

        try {
          // 方法1：使用公共目录
          const downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
          if (downloadDir) {
            downloadPath = downloadDir.getAbsolutePath();
            console.log('使用公共目录获取Download路径:', downloadPath);
          }
        } catch (e) {
          console.error('获取公共Download目录失败:', e);
        }

        // 如果方法1失败，尝试方法2
        if (!downloadPath) {
          try {
            // 方法2：使用外部存储根目录 + Download
            const externalStorageDir = Environment.getExternalStorageDirectory();
            if (externalStorageDir) {
              downloadPath = externalStorageDir.getAbsolutePath() + "/Download";
              console.log('使用外部存储根目录获取Download路径:', downloadPath);
            }
          } catch (e) {
            console.error('获取外部存储根目录失败:', e);
          }
        }

        // 如果方法2也失败，尝试方法3
        if (!downloadPath) {
          try {
            // 方法3：使用应用私有目录
            const context = plus.android.runtimeMainActivity();
            const fileDir = context.getExternalFilesDir(null);
            if (fileDir) {
              downloadPath = fileDir.getAbsolutePath();
              console.log('使用应用私有目录:', downloadPath);
            }
          } catch (e) {
            console.error('获取应用私有目录失败:', e);
          }
        }

        // 如果所有方法都失败，显示错误
        if (!downloadPath) {
          throw new Error('无法获取Download目录路径');
        }

        console.log('最终使用的Download目录路径:', downloadPath);

        // 确保目录存在
        const downloadDir = new File(downloadPath);
        if (!downloadDir.exists()) {
          downloadDir.mkdirs();
          console.log('创建Download目录:', downloadPath);
        }

        // 列出Download目录中的所有.md文件
        const mdFiles = listMdFiles(downloadPath);

        if (mdFiles.length === 0) {
          uni.showModal({
            title: '未找到MD文件',
            content: 'Download文件夹中没有找到.md文件',
            showCancel: false
          });
          return;
        }

        // 显示文件选择列表
        uni.showActionSheet({
          itemList: mdFiles.map(file => file.name),
          success: (res) => {
            const selectedIndex = res.tapIndex;
            selectedFile.value = mdFiles[selectedIndex];
            fileName.value = selectedFile.value.name.replace('.md', '');
            console.log('已选择文件:', selectedFile.value);
          }
        });
      } catch (error) {
        console.error('选择文件失败:', error);
        uni.showToast({
          title: '选择文件失败: ' + error.message,
          icon: 'none'
        });
      }
    };

    // 列出指定目录中的所有.md文件
    const listMdFiles = (dirPath) => {
      try {
        console.log('开始列出目录中的MD文件:', dirPath);

        const File = plus.android.importClass("java.io.File");
        const dir = new File(dirPath);

        // 检查目录是否存在
        if (!dir.exists()) {
          console.error('目录不存在:', dirPath);
          return [];
        }

        // 检查是否是目录
        if (!dir.isDirectory()) {
          console.error('路径不是目录:', dirPath);
          return [];
        }

        console.log('目录存在且有效:', dirPath);

        // 列出文件
        const files = dir.listFiles();
        console.log('目录中的文件数量:', files ? files.length : 0);

        const mdFiles = [];
        if (files) {
          for (let i = 0; i < files.length; i++) {
            try {
              const file = files[i];
              const fileName = file.getName();
              const isFile = file.isFile();
              const isDirectory = file.isDirectory();

              console.log(`文件 ${i+1}/${files.length}: ${fileName}, 是文件: ${isFile}, 是目录: ${isDirectory}`);

              if (isFile && fileName.toLowerCase().endsWith('.md')) {
                const filePath = file.getAbsolutePath();
                const fileSize = file.length();

                console.log(`找到MD文件: ${fileName}, 路径: ${filePath}, 大小: ${fileSize} 字节`);

                mdFiles.push({
                  name: fileName,
                  path: filePath,
                  size: fileSize
                });
              }
            } catch (fileError) {
              console.error('处理文件时出错:', fileError);
            }
          }
        } else {
          console.warn('目录中没有文件或无法访问:', dirPath);
        }

        console.log(`共找到 ${mdFiles.length} 个MD文件`);
        return mdFiles;
      } catch (error) {
        console.error('列出MD文件失败:', error);
        return [];
      }
    };

    // 请求存储权限
    const requestStoragePermission = () => {
      return new Promise((resolve, reject) => {
        console.log('开始请求存储权限');

        // Android 10及以上需要请求MANAGE_EXTERNAL_STORAGE权限
        const permissions = [
          'android.permission.READ_EXTERNAL_STORAGE',
          'android.permission.WRITE_EXTERNAL_STORAGE'
        ];

        // 检查Android版本
        try {
          const Build = plus.android.importClass("android.os.Build");
          const sdkInt = Build.VERSION.SDK_INT;
          console.log('Android SDK版本:', sdkInt);

          // Android 11 (API 30)及以上版本
          if (sdkInt >= 30) {
            console.log('Android 11及以上版本，需要特殊处理');

            // 对于Android 11+，我们使用应用专属存储空间
            // 这不需要特殊权限，但我们仍然请求基本的存储权限
            console.warn('Android 11+设备将使用应用专属存储空间');
          }
        } catch (e) {
          console.error('检查Android版本失败:', e);
        }

        console.log('请求以下权限:', permissions.join(', '));

        plus.android.requestPermissions(
          permissions,
          function(resultObj) {
            console.log('权限请求结果:', JSON.stringify(resultObj));

            if (resultObj.granted && resultObj.granted.length > 0) {
              console.log('已授予权限:', resultObj.granted.join(', '));
              resolve();
            } else {
              console.error('未授予所有请求的权限');

              // 即使没有获得所有权限，我们也尝试继续
              if (resultObj.granted && resultObj.granted.length > 0) {
                console.warn('尝试使用部分权限继续操作');
                resolve();
              } else {
                reject(new Error('未授予任何存储权限，无法访问文件'));
              }
            }
          },
          function(error) {
            console.error('请求权限时出错:', error);
            reject(error);
          }
        );
      });
    };

    // 读取文件内容
    const readFileContent = async () => {
      if (!selectedFile.value) {
        uni.showToast({
          title: '请先选择文件',
          icon: 'none'
        });
        return;
      }

      try {
        processingStatus.value = '正在读取文件...';
        processingProgress.value = 5;
        console.log('开始读取文件:', selectedFile.value.path);

        // 使用原生Java方法读取文件内容
        const File = plus.android.importClass("java.io.File");
        const FileInputStream = plus.android.importClass("java.io.FileInputStream");
        const BufferedReader = plus.android.importClass("java.io.BufferedReader");
        const InputStreamReader = plus.android.importClass("java.io.InputStreamReader");
        const StringBuilder = plus.android.importClass("java.lang.StringBuilder");

        // 检查文件是否存在
        const file = new File(selectedFile.value.path);
        if (!file.exists()) {
          throw new Error('文件不存在: ' + selectedFile.value.path);
        }

        console.log('文件存在，大小:', file.length(), '字节');

        // 尝试读取文件内容
        let fis = null;
        let isr = null;
        let br = null;

        try {
          fis = new FileInputStream(file);
          console.log('文件输入流创建成功');

          isr = new InputStreamReader(fis, "UTF-8");
          console.log('输入流读取器创建成功');

          br = new BufferedReader(isr);
          console.log('缓冲读取器创建成功');

          const sb = new StringBuilder();
          let line = null;
          let lineCount = 0;

          console.log('开始逐行读取文件内容');
          while ((line = br.readLine()) !== null) {
            sb.append(line).append("\n");
            lineCount++;

            // 每读取100行更新一次进度
            if (lineCount % 100 === 0) {
              processingProgress.value = 5 + (lineCount / 1000); // 假设文件最多10000行
              processingStatus.value = `正在读取文件...已读取 ${lineCount} 行`;
            }
          }

          fileContent.value = sb.toString();
          console.log('文件内容读取成功，总行数:', lineCount, '总字符数:', fileContent.value.length);

          // 进入处理步骤
          currentStep.value = 2;
          processFileContent();
        } finally {
          // 确保关闭所有流
          console.log('关闭所有流');
          if (br) {
            try { br.close(); } catch (e) { console.error('关闭BufferedReader失败:', e); }
          }
          if (isr) {
            try { isr.close(); } catch (e) { console.error('关闭InputStreamReader失败:', e); }
          }
          if (fis) {
            try { fis.close(); } catch (e) { console.error('关闭FileInputStream失败:', e); }
          }
        }
      } catch (error) {
        console.error('读取文件失败:', error);
        processingError.value = '读取文件失败: ' + error.message;

        // 显示更详细的错误信息
        uni.showModal({
          title: '读取文件失败',
          content: `无法读取文件: ${selectedFile.value.path}\n\n错误信息: ${error.message}`,
          showCancel: false
        });
      }
    };

    // 处理文件内容
    const processFileContent = async () => {
      try {
        processingStatus.value = '正在分析文件内容...';
        processingProgress.value = 10;

        // 将文件内容分成多个部分进行处理
        const content = fileContent.value;
        const sections = splitContentIntoSections(content);

        processingStatus.value = `文件已分割为 ${sections.length} 个部分，开始处理...`;
        processingProgress.value = 20;

        // 处理每个部分
        const results = [];
        for (let i = 0; i < sections.length; i++) {
          processingStatus.value = `正在处理第 ${i + 1}/${sections.length} 部分...`;
          processingProgress.value = 20 + (i / sections.length) * 60;

          const section = sections[i];
          if (section.trim()) {
            const sectionResults = await processSection(section);
            if (sectionResults && sectionResults.length > 0) {
              results.push(...sectionResults);
            }
          }
        }

        processedItems.value = results;
        processingStatus.value = `处理完成，共生成 ${results.length} 条数据`;
        processingProgress.value = 100;
        processingComplete.value = true;
      } catch (error) {
        console.error('处理文件内容失败:', error);
        processingError.value = '处理文件内容失败: ' + error.message;
      }
    };

    // 将内容分割成多个部分
    const splitContentIntoSections = (content) => {
      // 根据文件大小和内容结构分割
      // 这里简单地按照段落或标题分割
      const maxSectionLength = 4000; // DeepSeek的输入限制约为8K tokens
      const lines = content.split('\n');
      const sections = [];
      let currentSection = '';

      for (const line of lines) {
        // 如果当前部分加上这一行超过了最大长度，或者遇到了标题行
        if (currentSection.length + line.length > maxSectionLength || line.startsWith('#')) {
          if (currentSection.trim()) {
            sections.push(currentSection.trim());
          }
          currentSection = line + '\n';
        } else {
          currentSection += line + '\n';
        }
      }

      // 添加最后一部分
      if (currentSection.trim()) {
        sections.push(currentSection.trim());
      }

      return sections;
    };

    // 处理单个部分
    const processSection = async (section) => {
      try {
        // 调用DeepSeek API处理内容
        const response = await uni.request({
          url: 'https://api.deepseek.com/v1/chat/completions',
          method: 'POST',
          data: {
            messages: [
              {
                role: "system",
                content: `你是一个专业的安全管理数据分析助手。请分析用户提供的安全管理文档内容，并按照以下要求提取信息：

1. 将文档完整内容逐条或逐项或逐句或逐段进行拆分处理。
2. 对每条内容提取相关信息，并转化成JSON格式。
3. 文件名为"${fileName.value}"。
4. articleType必须是以下选项之一："主体行为"、"设施设备"、"物料"、"方法措施"、"作业环境"、"应急管理"、"资料管理"。请仔细理解每条内容讲的是哪方面。如果一条内容涉及不止一个方面，那就拆分成多条内容。
5. articleContent为内容原文，不要修改，保留原始标点。
6. keywords为内容的关键词/标签，包括主语、标签、主要内容或摘要等。如果内容本身没有主语，需要从上下文或各级标题中获取。要确保我在搜索框中输入关键词或条文大意时，能够找到对应的原文内容。

输出格式示例：
[
  {
    "fileName": "工程质量安全手册",
    "articleType": "主体行为",
    "articleContent": "与参建各方签订的合同中应当明确安全责任，并加强履约管理。",
    "keywords": "建设单位 合同约定 安全责任"
  }
]

请站在安全管理专家、工程管理专家、语言学专家、资料管理专家等的角度分析内容。`
              },
              {
                role: "user",
                content: section
              }
            ],
            model: "deepseek-chat",
            temperature: 0.2
          },
          header: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer sk-38ff2a6b9f754b059fa42839d5a4b426'
          }
        });

        if (response.statusCode === 200 && response.data && response.data.choices && response.data.choices.length > 0) {
          const content = response.data.choices[0].message.content;

          // 提取JSON部分
          const jsonMatch = content.match(/\[\s*\{[\s\S]*\}\s*\]/);
          if (jsonMatch) {
            const jsonStr = jsonMatch[0];
            const parsedData = JSON.parse(jsonStr);
            return parsedData;
          } else {
            console.error('无法从API响应中提取JSON数据');
            return [];
          }
        } else {
          console.error('API请求失败:', response);
          throw new Error('API请求失败: ' + (response.statusCode || 'Unknown error'));
        }
      } catch (error) {
        console.error('处理部分内容失败:', error);
        return [];
      }
    };

    // 预览结果
    const previewResults = () => {
      if (processedItems.value.length === 0) {
        uni.showToast({
          title: '没有可预览的数据',
          icon: 'none'
        });
        return;
      }

      currentStep.value = 3;
    };

    // 取消导入
    const cancelImport = () => {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消导入吗？已处理的数据将丢失。',
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        }
      });
    };

    // 导入数据
    const importData = async () => {
      if (processedItems.value.length === 0) {
        uni.showToast({
          title: '没有数据可导入',
          icon: 'none'
        });
        return;
      }

      isImporting.value = true;

      try {
        uni.showLoading({
          title: '正在导入数据...'
        });

        // 分批导入数据
        const batchSize = 50; // 每批导入的数据量
        const totalItems = processedItems.value.length;
        let successCount = 0;

        for (let i = 0; i < totalItems; i += batchSize) {
          const batch = processedItems.value.slice(i, i + batchSize);

          // 导入当前批次
          for (const item of batch) {
            const result = await insertData('articles', item);
            if (result) {
              successCount++;
            }
          }

          // 更新进度
          importProgress.value = (i + batch.length) / totalItems * 100;
          uni.showLoading({
            title: `已导入 ${successCount}/${totalItems}...`
          });
        }

        uni.hideLoading();
        importComplete.value = true;

        // 显示导入结果
        uni.showModal({
          title: '导入完成',
          content: `成功导入 ${successCount} 条数据`,
          showCancel: false,
          success: () => {
            uni.navigateBack();
          }
        });
      } catch (error) {
        console.error('导入数据失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '导入数据失败: ' + error.message,
          icon: 'none'
        });
      } finally {
        isImporting.value = false;
      }
    };

    return {
      currentStep,
      selectedFile,
      processingStatus,
      processingProgress,
      processingError,
      processingComplete,
      processedItems,
      previewItems,
      isImporting,
      importProgress,
      importComplete,
      formatFileSize,
      goBack,
      selectFile,
      readFileContent,
      previewResults,
      cancelImport,
      importData
    };
  }
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #007AFF;
  padding: 20rpx 30rpx;
  padding-top: var(--status-bar-height);
}

.header-left, .header-right {
  width: 120rpx;
}

.header-back {
  color: #FFFFFF;
  font-size: 28rpx;
}

.header-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: bold;
}

.content {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

.step-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  background-color: #FFFFFF;
  padding: 20rpx;
  border-radius: 10rpx;
}

.step {
  font-size: 28rpx;
  color: #999999;
  position: relative;
  padding-bottom: 10rpx;
}

.step.active {
  color: #007AFF;
  font-weight: bold;
}

.step.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #007AFF;
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
  padding: 30rpx;
  border-radius: 10rpx;
}

.instruction {
  font-size: 32rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.primary-button {
  background-color: #007AFF;
  color: #FFFFFF;
  border-radius: 10rpx;
  padding: 20rpx;
  margin: 20rpx 0;
}

.secondary-button {
  background-color: #F0F0F0;
  color: #333333;
  border-radius: 10rpx;
  padding: 20rpx;
  margin: 20rpx 0;
}

.file-info {
  margin: 30rpx 0;
  padding: 20rpx;
  background-color: #F8F8F8;
  border-radius: 10rpx;
}

.file-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.file-size {
  font-size: 26rpx;
  color: #666666;
}

.processing-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40rpx 0;
}

.status-text {
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 26rpx;
  margin-top: 10rpx;
  color: #666666;
}

.error-message {
  margin: 20rpx 0;
  padding: 20rpx;
  background-color: #FFEBEB;
  border-radius: 10rpx;
  color: #DD524D;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.preview-title {
  font-size: 32rpx;
  font-weight: bold;
}

.preview-count {
  font-size: 26rpx;
  color: #666666;
}

.preview-list {
  flex: 1;
  margin-bottom: 20rpx;
}

.preview-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #F8F8F8;
  border-radius: 10rpx;
}

.preview-item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.item-number {
  font-size: 26rpx;
  color: #666666;
}

.item-type {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: bold;
}

.preview-item-content {
  margin-bottom: 10rpx;
}

.item-content {
  font-size: 28rpx;
}

.preview-item-footer {
  margin-top: 10rpx;
}

.item-keywords {
  font-size: 24rpx;
  color: #666666;
}

.import-actions {
  display: flex;
  justify-content: space-between;
}
</style>
