/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  min-height: 100vh;
  background: #f5f5f5;
}
.header {
  background: #4a90e2;
  padding: 30px 16px;
  color: white;
}
.header .header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding-top: 15px;
}
.header .title {
  color: white;
  font-size: 20px;
  font-weight: 500;
}
.category-list {
  padding: 16px;
}
.category-item {
  margin-bottom: 12px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}
.category-item.level-1 {
  border-left: 4px solid #4a90e2;
}
.category-item.level-2 {
  border-left: 4px solid #67c23a;
  margin-left: 16px;
}
.category-item.level-3 {
  border-left: 4px solid #e6a23c;
  margin-left: 16px;
}
.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}
.category-name {
  font-size: 16px;
  font-weight: 500;
}
.category-actions {
  display: flex;
  gap: 8px;
}
.action-btn {
  font-size: 12px;
  padding: 4px 8px;
  margin: 0;
  line-height: 1.2;
}
.action-btn.edit-btn {
  background: #4a90e2;
  color: white;
}
.action-btn.add-btn {
  background: #67c23a;
  color: white;
}
.action-btn.delete-btn {
  background: #f56c6c;
  color: white;
}
.subcategory-list {
  padding: 0 0 8px 0;
}
.add-category-btn {
  margin: 20px 16px;
  background: #4a90e2;
  color: white;
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  font-weight: 500;
}
.dialog-input {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 12px;
  width: 100%;
}
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.8);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}
.loading-text {
  font-size: 16px;
  color: #333;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.modal-content {
  position: relative;
  width: 90%;
  max-width: 600px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  z-index: 1;
  margin: 0 auto;
}
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}
.modal-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
.modal-close {
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 4px;
}
.modal-body {
  padding: 20px;
  box-sizing: border-box;
}
.modal-input {
  width: 100%;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  box-sizing: border-box;
}
.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  border-top: 1px solid #eee;
  gap: 12px;
  box-sizing: border-box;
}
.modal-button {
  font-size: 14px;
  padding: 8px 24px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  white-space: nowrap;
  min-width: 80px;
  text-align: center;
}
.modal-button.cancel {
  background-color: #f5f5f5;
  color: #606266;
  border: 1px solid #dcdfe6;
}
.modal-button.cancel:active {
  background-color: #e9e9e9;
}
.modal-button.confirm {
  background-color: #4a90e2;
  color: #fff;
  border: none;
}
.modal-button.confirm:active {
  background-color: #357abd;
}