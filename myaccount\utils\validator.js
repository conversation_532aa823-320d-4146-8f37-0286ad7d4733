/**
 * 表单验证工具
 */

// 用户名验证
export const validateUsername = (username) => {
  if (!username) {
    return '用户名不能为空'
  }
  if (username.length < 3) {
    return '用户名至少需要3个字符'
  }
  return ''
}

// 密码验证
export const validatePassword = (password) => {
  if (!password) {
    return '密码不能为空'
  }
  if (password.length < 6) {
    return '密码至少需要6个字符'
  }
  return ''
}

// 确认密码验证
export const validateConfirmPassword = (password, confirmPassword) => {
  if (!confirmPassword) {
    return '确认密码不能为空'
  }
  if (password !== confirmPassword) {
    return '两次输入的密码不一致'
  }
  return ''
}

// 邮箱验证
export const validateEmail = (email) => {
  if (!email) {
    return '邮箱不能为空'
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    return '请输入有效的邮箱地址'
  }
  
  return ''
}

// 金额验证
export const validateAmount = (amount) => {
  if (!amount && amount !== 0) {
    return '金额不能为空'
  }
  
  const amountValue = parseFloat(amount)
  if (isNaN(amountValue)) {
    return '请输入有效的金额'
  }
  
  if (amountValue < 0) {
    return '金额不能为负数'
  }
  
  return ''
}

// 分类名称验证
export const validateCategoryName = (name) => {
  if (!name) {
    return '分类名称不能为空'
  }
  
  if (name.length > 20) {
    return '分类名称不能超过20个字符'
  }
  
  return ''
}

// API密钥验证
export const validateApiKey = (apiKey) => {
  if (!apiKey) {
    return 'API密钥不能为空'
  }
  
  if (apiKey.length < 10) {
    return 'API密钥格式不正确'
  }
  
  return ''
}

// 表单整体验证
export const validateForm = (form, rules) => {
  const errors = {}
  let isValid = true
  
  Object.keys(rules).forEach(field => {
    const value = form[field]
    const validationFn = rules[field]
    const error = validationFn(value)
    
    if (error) {
      errors[field] = error
      isValid = false
    }
  })
  
  return { isValid, errors }
}
