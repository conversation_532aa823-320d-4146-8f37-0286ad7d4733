18:21:32.052 项目 'safety_management' 开始编译...
18:21:36.530 请注意运行模式下，因日志输出、sourcemap 以及未压缩源码等原因，性能和包体积，均不及发行模式。
18:21:36.530 编译器版本：4.57（vue3）
18:21:36.545 正在编译中...
18:21:45.387 项目 'safety_management' 编译成功。
18:21:45.432 ready in 11979ms.
18:21:45.462 正在建立手机连接...
18:21:46.021 手机端调试基座版本号为4.57, 版本号相同，跳过基座更新
18:21:47.548 正在同步手机端程序文件...
18:21:47.848 同步手机端程序文件完成
18:21:49.121 正在启动HBuilder调试基座...
18:21:50.307 应用【SQLite数据库管理器】已启动
18:21:50.442 App Launch at App.vue:6
18:21:50.442 App Show at App.vue:21
18:21:50.517 数据库打开成功 at utils/sqlite.js:73
18:21:50.532 外键约束已启用 at utils/sqlite.js:79
18:21:50.607 开始检查系统表结构... at utils/sqlite.js:215
18:21:50.727 开始初始化所有预置表 at pages/index/index.vue:81
18:21:50.742 开始初始化所有预置表,  (强制重新创建) at pages/index/init-tables.js:18
18:21:50.772 系统表结构检查完成 at utils/sqlite.js:238
18:21:50.772 开始检查系统表结构... at utils/sqlite.js:215
18:21:50.952 系统表结构检查完成 at utils/sqlite.js:238
18:21:50.952 数据库初始化成功，表的创建将在用户界面中处理 at App.vue:15
18:21:50.967 检查表是否存在:,  [Object] {"documentsExists":true,"articlesExists":true,"projectsExists":true,"subprojectsExists":true}  at pages/index/init-tables.js:157
18:21:50.967 开始删除所有名为 documents 的表的系统记录 at utils/sqlite.js:402
18:21:51.027 找到 1 个名为 documents 的表 at utils/sqlite.js:406
18:21:51.074 已删除表 documents 的列记录，ID: 229 at utils/sqlite.js:412
18:21:51.089 已删除表 documents 的系统记录，ID: 229 at utils/sqlite.js:416
18:21:51.089 保留实际表 documents 及其数据 at utils/sqlite.js:420
18:21:51.089 已删除所有名为 documents 的表 at pages/index/init-tables.js:174
18:21:51.104 文档表创建成功 at pages/index/init-tables.js:51
18:21:51.104 文档表已重新创建 at pages/index/init-tables.js:178
18:21:51.254 设置 category 的选项:,  ["法律","法规","规章","规范性文件","标准/规范","规程","其他"] at pages/index/init-tables.js:344
18:21:51.359 文档表注册成功 at pages/index/init-tables.js:355
18:21:51.374 文档表已注册到系统表 at pages/index/init-tables.js:182
18:21:51.389 开始删除所有名为 articles 的表的系统记录 at utils/sqlite.js:402
18:21:51.389 找到 1 个名为 articles 的表 at utils/sqlite.js:406
18:21:51.404 已删除表 articles 的列记录，ID: 230 at utils/sqlite.js:412
18:21:51.419 已删除表 articles 的系统记录，ID: 230 at utils/sqlite.js:416
18:21:51.419 保留实际表 articles 及其数据 at utils/sqlite.js:420
18:21:51.419 已删除所有名为 articles 的表 at pages/index/init-tables.js:189
18:21:51.419 条文表创建成功 at pages/index/init-tables.js:78
18:21:51.419 条文表已重新创建 at pages/index/init-tables.js:193
18:21:51.509 设置 articleType 的选项:,  ["主体行为","设施设备","物料","方法措施","作业环境","应急管理","资料管理"] at pages/index/init-tables.js:453
18:21:51.614 条文表注册成功 at pages/index/init-tables.js:464
18:21:51.614 条文表已注册到系统表 at pages/index/init-tables.js:197
18:21:51.614 开始删除所有名为 projects 的表的系统记录 at utils/sqlite.js:402
18:21:51.629 找到 1 个名为 projects 的表 at utils/sqlite.js:406
18:21:51.629 已删除表 projects 的列记录，ID: 231 at utils/sqlite.js:412
18:21:51.659 已删除表 projects 的系统记录，ID: 231 at utils/sqlite.js:416
18:21:51.659 保留实际表 projects 及其数据 at utils/sqlite.js:420
18:21:51.659 已删除所有名为 projects 的表 at pages/index/init-tables.js:204
18:21:51.659 项目表创建成功 at pages/index/init-tables.js:104
18:21:51.659 项目表已重新创建 at pages/index/init-tables.js:208
18:21:51.794 项目表注册成功 at pages/index/init-tables.js:536
18:21:51.794 项目表已注册到系统表 at pages/index/init-tables.js:212
18:21:51.809 开始删除所有名为 subprojects 的表的系统记录 at utils/sqlite.js:402
18:21:51.824 找到 1 个名为 subprojects 的表 at utils/sqlite.js:406
18:21:51.824 已删除表 subprojects 的列记录，ID: 232 at utils/sqlite.js:412
18:21:51.839 已删除表 subprojects 的系统记录，ID: 232 at utils/sqlite.js:416
18:21:51.839 保留实际表 subprojects 及其数据 at utils/sqlite.js:420
18:21:51.854 已删除所有名为 subprojects 的表 at pages/index/init-tables.js:219
18:21:51.854 子项目表创建成功 at pages/index/init-tables.js:137
18:21:51.854 子项目表已重新创建 at pages/index/init-tables.js:223
18:21:52.121 子项目表注册成功 at pages/index/init-tables.js:657
18:21:52.136 子项目表已注册到系统表 at pages/index/init-tables.js:227
18:21:52.151 所有预置表初始化完成 at pages/index/init-tables.js:23
18:21:52.151 初始化所有预置表结果:,  [Boolean] true  at pages/index/index.vue:83
18:21:57.103 初始化表格详情页，UI_CONFIG:,  [Object] {"longPressThreshold":800,"tablePageSize":50,"tableMaxHeight":800,"searchDebounceTime":300}  at pages/table/detail.vue:261
18:21:57.118 有效列数量:,  [Number] 9  at pages/table/detail.vue:545
18:21:57.118 有效列数量:,  [Number] 9  at pages/table/detail.vue:545
18:21:57.118 查询表 documents 数据，是否包含rowid: true at utils/sqlite.js:617
18:21:57.118 表 documents 数据加载完成，共 2 行，是否都有rowid: true at pages/table/detail.vue:468
18:21:57.133 查询表 documents 数据，是否包含rowid: true at utils/sqlite.js:617
18:21:57.133 表 documents 数据加载完成，共 2 行，是否都有rowid: true at pages/table/detail.vue:468
18:22:06.354 有效列数量:,  [Number] 9  at pages/table/detail.vue:545
18:22:09.106 有效列数量:,  [Number] 9  at pages/table/detail.vue:545
18:22:32.323 更新表 documents 中的数据，行ID: 2 at utils/sqlite.js:680
18:22:33.058 有效列数量:,  [Number] 9  at pages/table/detail.vue:545
18:22:33.088 查询表 documents 数据，是否包含rowid: true at utils/sqlite.js:617
18:22:33.103 表 documents 数据加载完成，共 2 行，是否都有rowid: true at pages/table/detail.vue:468
18:22:35.943 初始化表格详情页，UI_CONFIG:,  [Object] {"longPressThreshold":800,"tablePageSize":50,"tableMaxHeight":800,"searchDebounceTime":300}  at pages/table/detail.vue:261
18:22:35.943 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:22:35.958 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:22:35.988 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:22:35.988 表 articles 数据加载完成，共 161 行，是否都有rowid: true at pages/table/detail.vue:468
18:22:36.345 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:22:36.345 表 articles 数据加载完成，共 161 行，是否都有rowid: true at pages/table/detail.vue:468
18:22:45.210 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:22:45.240 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:22:45.240 表 articles 数据加载完成，共 160 行，是否都有rowid: true at pages/table/detail.vue:468
18:22:49.257 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:22:49.287 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:22:49.287 表 articles 数据加载完成，共 159 行，是否都有rowid: true at pages/table/detail.vue:468
18:23:18.824 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:23:18.854 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:23:18.854 表 articles 数据加载完成，共 151 行，是否都有rowid: true at pages/table/detail.vue:468
18:23:31.571 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:23:31.586 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:23:31.586 表 articles 数据加载完成，共 150 行，是否都有rowid: true at pages/table/detail.vue:468
18:23:42.568 初始化表格详情页，UI_CONFIG:,  [Object] {"longPressThreshold":800,"tablePageSize":50,"tableMaxHeight":800,"searchDebounceTime":300}  at pages/table/detail.vue:261
18:23:42.598 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:23:42.598 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:23:42.598 查询表 projects 数据，是否包含rowid: true at utils/sqlite.js:617
18:23:42.598 表 projects 数据加载完成，共 1 行，是否都有rowid: true at pages/table/detail.vue:468
18:23:42.613 查询表 projects 数据，是否包含rowid: true at utils/sqlite.js:617
18:23:42.613 表 projects 数据加载完成，共 1 行，是否都有rowid: true at pages/table/detail.vue:468
18:23:53.912 初始化表格详情页，UI_CONFIG:,  [Object] {"longPressThreshold":800,"tablePageSize":50,"tableMaxHeight":800,"searchDebounceTime":300}  at pages/table/detail.vue:261
18:23:53.912 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:23:53.912 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:23:53.912 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:23:53.912 表 articles 数据加载完成，共 150 行，是否都有rowid: true at pages/table/detail.vue:468
18:23:54.407 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:23:54.407 表 articles 数据加载完成，共 150 行，是否都有rowid: true at pages/table/detail.vue:468
18:23:56.888 初始化表格详情页，UI_CONFIG:,  [Object] {"longPressThreshold":800,"tablePageSize":50,"tableMaxHeight":800,"searchDebounceTime":300}  at pages/table/detail.vue:261
18:23:56.888 有效列数量:,  [Number] 9  at pages/table/detail.vue:545
18:23:56.903 有效列数量:,  [Number] 9  at pages/table/detail.vue:545
18:23:56.903 查询表 documents 数据，是否包含rowid: true at utils/sqlite.js:617
18:23:56.903 表 documents 数据加载完成，共 2 行，是否都有rowid: true at pages/table/detail.vue:468
18:23:56.903 查询表 documents 数据，是否包含rowid: true at utils/sqlite.js:617
18:23:56.918 表 documents 数据加载完成，共 2 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:00.641 有效列数量:,  [Number] 9  at pages/table/detail.vue:545
18:24:00.641 查询表 documents 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:00.641 表 documents 数据加载完成，共 1 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:02.227 初始化表格详情页，UI_CONFIG:,  [Object] {"longPressThreshold":800,"tablePageSize":50,"tableMaxHeight":800,"searchDebounceTime":300}  at pages/table/detail.vue:261
18:24:02.242 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:24:02.242 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:24:02.273 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:02.273 表 articles 数据加载完成，共 150 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:02.929 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:02.929 表 articles 数据加载完成，共 150 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:08.003 权限请求结果:,  [Object] {"granted":["android.permission.READ_EXTERNAL_STORAGE"],"deniedPresent":[],"deniedAlways":[]}  at pages/data/import.vue:283
18:24:08.047 已获得存储权限，准备选择文件 at pages/data/import.vue:299
18:24:08.362 方法2获取Download路径:,  /storage/emulated/0/Download at pages/data/import.vue:332
18:24:08.362 最终使用的Download文件夹路径:,  /storage/emulated/0/Download at pages/data/import.vue:355
18:24:08.600 Download文件夹中的文件数量:,  [Number] 89  at pages/data/import.vue:395
18:24:08.614 不是文件，跳过:,  [Object] {"__UUID__":"Invocation-777675231","__TYPE__":"JSBObject","className":"java.io.File"}  at pages/data/import.vue:424
18:24:08.614 不是文件，跳过:,  [Object] {"__UUID__":"Invocation2130135939","__TYPE__":"JSBObject","className":"java.io.File"}  at pages/data/import.vue:424
18:24:08.629 不是文件，跳过:,  [Object] {"__UUID__":"Invocation1885394974","__TYPE__":"JSBObject","className":"java.io.File"}  at pages/data/import.vue:424
18:24:08.629 检查文件: wireguard-export.zip at pages/data/import.vue:434
18:24:08.660 检查文件: F-Droid.apk at pages/data/import.vue:434
18:24:08.660 检查文件: ProtonVPN_v3.2.11.exe at pages/data/import.vue:434
18:24:08.660 检查文件: com.microsoft.copilot.apk at pages/data/import.vue:434
18:24:08.660 检查文件: wireguard-installer.exe at pages/data/import.vue:434
18:24:08.690 检查文件: ChatGPT.xapk at pages/data/import.vue:434
18:24:08.690 不是文件，跳过:,  [Object] {"__UUID__":"Invocation-1939220842","__TYPE__":"JSBObject","className":"java.io.File"}  at pages/data/import.vue:424
18:24:08.705 检查文件: AuroraStore-4.4.3.apk at pages/data/import.vue:434
18:24:08.705 检查文件: mobile.zip at pages/data/import.vue:434
18:24:08.720 检查文件: Qv2ray-v2.7.0-linux-x64 (1).AppImage at pages/data/import.vue:434
18:24:08.720 检查文件: articles_2025-05-22.csv at pages/data/import.vue:434
18:24:08.720 找到有效文件: articles_2025-05-22.csv, 路径: /storage/emulated/0/Download/articles_2025-05-22.csv at pages/data/import.vue:438
18:24:08.735 检查文件: v2rayNG_1.8.19.apk at pages/data/import.vue:434
18:24:08.750 检查文件: Hiddify-Android-arm64.apk at pages/data/import.vue:434
18:24:08.750 检查文件: oex-alpha-v0.1.9-20240415.apk at pages/data/import.vue:434
18:24:08.764 检查文件: Hiddify-rpm-x64.rpm at pages/data/import.vue:434
18:24:08.779 检查文件: QvPlugin-SSR.v3.0.0-pre3.linux-no-avx-x64.so at pages/data/import.vue:434
18:24:08.779 不是文件，跳过:,  [Object] {"__UUID__":"Invocation1129037638","__TYPE__":"JSBObject","className":"java.io.File"}  at pages/data/import.vue:424
18:24:08.794 检查文件: lark_feishu_website_organic_and_v6667_7150750_12d9_1713263558.apk at pages/data/import.vue:434
18:24:08.810 检查文件: v2ray-linux-64.zip at pages/data/import.vue:434
18:24:08.810 检查文件: QvPlugin-Trojan.v3.0.0-pre3.linux-x64.so at pages/data/import.vue:434
18:24:08.825 检查文件: Obsidian_1.5.12_Apkpure.apk at pages/data/import.vue:434
18:24:08.825 检查文件: 0601.7z at pages/data/import.vue:434
18:24:08.840 检查文件: Hiddify-Debian-x64.deb at pages/data/import.vue:434
18:24:08.855 检查文件: QvPlugin-NaiveProxy.v3.0.0-pre3.linux-x64.so at pages/data/import.vue:434
18:24:08.855 检查文件: QvPlugin-TrojanGo.v3.0.0-pre3.linux-x64.so at pages/data/import.vue:434
18:24:08.870 检查文件: QvPlugin-SS.v3.0.0-pre3.linux-x64.so at pages/data/import.vue:434
18:24:08.885 检查文件: QvPlugin-Command.v3.0.0-pre3.linux-x64.so at pages/data/import.vue:434
18:24:08.885 检查文件: articles_2025-05-22(1).csv at pages/data/import.vue:434
18:24:08.899 找到有效文件: articles_2025-05-22(1).csv, 路径: /storage/emulated/0/Download/articles_2025-05-22(1).csv at pages/data/import.vue:438
18:24:08.899 检查文件: .tistore at pages/data/import.vue:434
18:24:08.915 检查文件: jetbra-ded4f9dc4fcb60294b21669dafa90330f2713ce4.zip at pages/data/import.vue:434
18:24:08.929 检查文件: inpaint-web-main.zip at pages/data/import.vue:434
18:24:08.944 检查文件: .exmu-cfg1.data at pages/data/import.vue:434
18:24:08.944 检查文件: OneSync-0.0.5.jar at pages/data/import.vue:434
18:24:08.960 检查文件: obsidian_dataview_example_vault-main.zip at pages/data/import.vue:434
18:24:08.960 检查文件: zlibrary-app-latest.apk at pages/data/import.vue:434
18:24:08.975 检查文件: articles_2025-05-22T10-08-25-574Z.json at pages/data/import.vue:434
18:24:08.975 找到有效文件: articles_2025-05-22T10-08-25-574Z.json, 路径: /storage/emulated/0/Download/articles_2025-05-22T10-08-25-574Z.json at pages/data/import.vue:438
18:24:08.975 检查文件: Pkmer-Math-main.zip at pages/data/import.vue:434
18:24:08.990 检查文件: OBS-Studio-30.1.2-Full-Installer-x64.exe at pages/data/import.vue:434
18:24:09.005 检查文件: Obsidian.1.5.12.exe at pages/data/import.vue:434
18:24:09.005 检查文件: Obsidian.1.5.12-arm64.exe at pages/data/import.vue:434
18:24:09.020 检查文件: 中华人民共和国安全生产法.md at pages/data/import.vue:434
18:24:09.020 不是文件，跳过:,  [Object] {"__UUID__":"Invocation1575164073","__TYPE__":"JSBObject","className":"java.io.File"}  at pages/data/import.vue:424
18:24:09.064 检查文件: Hiddify-Windows-Setup-x64.exe at pages/data/import.vue:434
18:24:09.064 检查文件: 1356667802203.doc at pages/data/import.vue:434
18:24:09.079 检查文件: Satoshi-5110-GLO.apk at pages/data/import.vue:434
18:24:09.079 检查文件: P020230407569760184890.docx at pages/data/import.vue:434
18:24:09.079 不是文件，跳过:,  [Object] {"__UUID__":"Invocation-1580759117","__TYPE__":"JSBObject","className":"java.io.File"}  at pages/data/import.vue:424
18:24:09.079 不是文件，跳过:,  [Object] {"__UUID__":"Invocation-777675202","__TYPE__":"JSBObject","className":"java.io.File"}  at pages/data/import.vue:424
18:24:09.079 检查文件: juscrkat.dat at pages/data/import.vue:434
18:24:09.110 不是文件，跳过:,  [Object] {"__UUID__":"Invocation-1576375716","__TYPE__":"JSBObject","className":"java.io.File"}  at pages/data/import.vue:424
18:24:09.110 不是文件，跳过:,  [Object] {"__UUID__":"Invocation-1576397099","__TYPE__":"JSBObject","className":"java.io.File"}  at pages/data/import.vue:424
18:24:09.125 检查文件: HANYCJLZOEUS_TOKEN2.dat at pages/data/import.vue:434
18:24:09.125 检查文件: nbavmc_unxqbih.dat at pages/data/import.vue:434
18:24:09.140 检查文件: CJJ_1-2008_城镇道路工程施工与质量验收规范.pdf at pages/data/import.vue:434
18:24:09.154 检查文件: xf40.apk at pages/data/import.vue:434
18:24:09.170 检查文件: F-Droid (2).apk at pages/data/import.vue:434
18:24:09.170 检查文件: oex-alpha-v1.0.22-20240909.apk at pages/data/import.vue:434
18:24:09.185 检查文件: Cloudflare_WARP_2024.9.346.0.msi at pages/data/import.vue:434
18:24:09.200 检查文件: bb8a6fdb69104b0d9c93fd2b101e36aa.doc at pages/data/import.vue:434
18:24:09.215 检查文件: bb8a6fdb69104b0d9c93fd2b101e36aa (2).doc at pages/data/import.vue:434
18:24:09.215 检查文件: geph-android-4.4.20.apk at pages/data/import.vue:434
18:24:09.229 检查文件: mdm.xml at pages/data/import.vue:434
18:24:09.229 检查文件: rustdesk-1.3.8-universal-signed.apk at pages/data/import.vue:434
18:24:09.244 检查文件: geph-windows-setup.exe at pages/data/import.vue:434
18:24:09.244 检查文件: karing-1.0.26.325.zip at pages/data/import.vue:434
18:24:09.260 检查文件: geph-android (1).apk at pages/data/import.vue:434
18:24:09.260 检查文件: 2024011515380270066.doc at pages/data/import.vue:434
18:24:09.275 检查文件: obsidian-latex-suite.zip at pages/data/import.vue:434
18:24:09.290 检查文件: 论系统工程 (钱学森) (Z-Library).pdf at pages/data/import.vue:434
18:24:09.290 检查文件: rustdesk-1.3.3-universal-signed.apk at pages/data/import.vue:434
18:24:09.305 检查文件: 钱学森系统科学思想研究 (中国系统工程学会，上海交通大_ (Z-Library).pdf at pages/data/import.vue:434
18:24:09.305 检查文件: 运筹学 (陈秉正) (Z-Library).pdf at pages/data/import.vue:434
18:24:09.319 检查文件: 600000@zhengzhou-release-2.10.1.3_44035190.apk at pages/data/import.vue:434
18:24:09.319 检查文件: .thumbcache_idx_001 at pages/data/import.vue:434
18:24:09.335 检查文件: MinerU-master.zip at pages/data/import.vue:434
18:24:09.350 检查文件: app-universal-release.apk at pages/data/import.vue:434
18:24:09.350 检查文件: .pending-1736380122145-Inpaint-Anything-0.1.0.zip at pages/data/import.vue:434
18:24:09.364 检查文件: dzfp_24412000000210826886_郑州君鹏综合医院有限公司_20250102103102.pdf at pages/data/import.vue:434
18:24:09.379 检查文件: Python编程：从入门到实践（第3版） ([美] 埃_ (Z-Library).pdf at pages/data/import.vue:434
18:24:09.394 不是文件，跳过:,  [Object] {"__UUID__":"Invocation1610896664","__TYPE__":"JSBObject","className":"java.io.File"}  at pages/data/import.vue:424
18:24:09.410 检查文件: ima_1.0.1.1001_20250225_174257.apk at pages/data/import.vue:434
18:24:09.410 检查文件: JetBrains+全家桶激活（2024最新）.exe at pages/data/import.vue:434
18:24:09.424 检查文件: Inpaint-Anything-0.1.0 (1).zip at pages/data/import.vue:434
18:24:09.439 检查文件: rustdesk-1.3.6-universal-signed (1).apk at pages/data/import.vue:434
18:24:09.455 检查文件: .pending-1745682600360-iBiliPlayer-html5_search_bing.apk at pages/data/import.vue:434
18:24:09.455 检查文件: QuarkBrowser_V7.10.5.800_android_pf3300_(zh-cn)_release_(Build250417150726-arm64).apk at pages/data/import.vue:434
18:24:09.455 找到 3 个有效的CSV/JSON文件 at pages/data/import.vue:449
18:24:11.383 开始读取文件:,  /storage/emulated/0/Download/articles_2025-05-22(1).csv at pages/data/import.vue:490
18:24:11.428 文件大小:,  [Number] 391 ,  字节 at pages/data/import.vue:513
18:24:11.428 使用GBK编码 at pages/data/import.vue:529
18:24:11.458 开始读取文件内容，使用,  GBK,  编码 at pages/data/import.vue:532
18:24:11.515 使用,  GBK,  编码成功读取了,  [Number] 3 ,  行数据 at pages/data/import.vue:555
18:24:11.515 文件内容读取完成 at pages/data/import.vue:608
18:24:11.515 开始解析CSV数据 at pages/data/import.vue:777
18:24:11.515 CSV文件包含 3 行数据 at pages/data/import.vue:787
18:24:11.515 CSV内容包含中文字符 at pages/data/import.vue:796
18:24:11.515 解析CSV行:,  id,fileName,articleType,articl... at pages/data/import.vue:893
18:24:11.515 解析到的表头:,  [Object] ["id","fileName","articleType","articleContent","keywords","createTime","updateTime"]  at pages/data/import.vue:800
18:24:11.515 表列类型信息:,  [Object] {"id":"INTEGER","fileName":"TEXT","articleType":"TEXT","articleContent":"TEXT","keywords":"...} at pages/data/import.vue:807
18:24:11.515 自动生成字段:,  [Object] ["id","createTime","updateTime","rowid"]  at pages/data/import.vue:811
18:24:11.515 过滤后的表头:,  [Object] ["fileName","articleType","articleContent","keywords"]  at pages/data/import.vue:815
18:24:11.515 解析CSV行:,  2,工程质量安全手册,主体行为,与参建各方签订的合同中应当明... at pages/data/import.vue:893
18:24:11.515 第 1 行数据:,  {"fileName":"工程质量安全手册","articleType":"主体行为","articleContent":"与参建各方签订的合同中应当明确安全责任，并加强履约管理。","keywords":"建设单位 合同约定 安全责任"} at pages/data/import.vue:882
18:24:11.515 解析CSV行:,  3,工程质量安全手册,主体行为,按规定将委托的监理单位、监理... at pages/data/import.vue:893
18:24:11.515 第 2 行数据:,  {"fileName":"工程质量安全手册","articleType":"主体行为","articleContent":"按规定将委托的监理单位、监理的内容及监理权限书面通知被监理的建筑施工企业。","keywords":"建设单位 书面告知施工单位 监理单位内容"} at pages/data/import.vue:882
18:24:11.515 成功解析 2 行数据 at pages/data/import.vue:886
18:24:14.927 开始导入数据，共,  [Number] 2 ,  条记录 at pages/data/import.vue:1306
18:24:14.942 准备插入第 1 行数据:,  {"fileName":"工程质量安全手册","articleType":"主体行为","articleContent":"与参建各方签订的合同中应当明确安全责任，并加强履约管理。","keywords":"建设单位 合同约定 安全责任"} at pages/data/import.vue:1377
18:24:14.987 文件名 工程质量安全手册 不存在于文档表中，自动创建文档记录 at utils/sqlite.js:543
18:24:15.002 为文件名 工程质量安全手册 创建文档记录，使用默认值:,  [Object] {"fileName":"工程质量安全手册","category":"待完善","documentNumber":"待完善","publishDate":"","implementD...} at utils/sqlite.js:518
18:24:15.002 插入数据SQL: INSERT INTO documents (fileName, category, documentNumber, publishDate, implementDate, publishingUnit, createTime, updateTime) VALUES ('工程质量安全手册', '待完善', '待完善', '', '', '待完善', datetime('now', 'localtime'), datetime('now', 'localtime')) at utils/sqlite.js:587
18:24:15.002 数据插入成功，行ID: 3 at utils/sqlite.js:593
18:24:15.017 为文件名 工程质量安全手册 创建文档记录成功，ID: 3 at utils/sqlite.js:549
18:24:15.017 插入数据SQL: INSERT INTO articles (fileName, articleType, articleContent, keywords, createTime, updateTime) VALUES ('工程质量安全手册', '主体行为', '与参建各方签订的合同中应当明确安全责任，并加强履约管理。', '建设单位 合同约定 安全责任', datetime('now', 'localtime'), datetime('now', 'localtime')) at utils/sqlite.js:587
18:24:15.047 数据插入成功，行ID: 162 at utils/sqlite.js:593
18:24:15.047 第 1 行数据插入成功，结果:,  [Number] 162  at pages/data/import.vue:1408
18:24:15.047 准备插入第 2 行数据:,  {"fileName":"工程质量安全手册","articleType":"主体行为","articleContent":"按规定将委托的监理单位、监理的内容及监理权限书面通知被监理的建筑施工企业。","keywords":"建设单位 书面告知施工单位 监理单位内容"} at pages/data/import.vue:1377
18:24:15.122 插入数据SQL: INSERT INTO articles (fileName, articleType, articleContent, keywords, createTime, updateTime) VALUES ('工程质量安全手册', '主体行为', '按规定将委托的监理单位、监理的内容及监理权限书面通知被监理的建筑施工企业。', '建设单位 书面告知施工单位 监理单位内容', datetime('now', 'localtime'), datetime('now', 'localtime')) at utils/sqlite.js:587
18:24:15.122 数据插入成功，行ID: 163 at utils/sqlite.js:593
18:24:15.122 第 2 行数据插入成功，结果:,  [Number] 163  at pages/data/import.vue:1408
18:24:18.711 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:24:18.965 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:18.965 表 articles 数据加载完成，共 152 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:21.420 初始化表格详情页，UI_CONFIG:,  [Object] {"longPressThreshold":800,"tablePageSize":50,"tableMaxHeight":800,"searchDebounceTime":300}  at pages/table/detail.vue:261
18:24:21.420 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:24:21.435 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:24:21.465 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:21.465 表 articles 数据加载完成，共 152 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:21.717 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:21.732 表 articles 数据加载完成，共 152 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:37.827 初始化表格详情页，UI_CONFIG:,  [Object] {"longPressThreshold":800,"tablePageSize":50,"tableMaxHeight":800,"searchDebounceTime":300}  at pages/table/detail.vue:261
18:24:37.827 有效列数量:,  [Number] 9  at pages/table/detail.vue:545
18:24:37.827 有效列数量:,  [Number] 9  at pages/table/detail.vue:545
18:24:37.842 查询表 documents 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:37.842 表 documents 数据加载完成，共 2 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:37.842 查询表 documents 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:37.842 表 documents 数据加载完成，共 2 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:42.688 初始化表格详情页，UI_CONFIG:,  [Object] {"longPressThreshold":800,"tablePageSize":50,"tableMaxHeight":800,"searchDebounceTime":300}  at pages/table/detail.vue:261
18:24:42.688 有效列数量:,  [Number] 14  at pages/table/detail.vue:545
18:24:42.703 有效列数量:,  [Number] 14  at pages/table/detail.vue:545
18:24:42.703 查询表 subprojects 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:42.703 表 subprojects 数据加载完成，共 1 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:42.703 查询表 subprojects 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:42.718 表 subprojects 数据加载完成，共 1 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:47.326 初始化表格详情页，UI_CONFIG:,  [Object] {"longPressThreshold":800,"tablePageSize":50,"tableMaxHeight":800,"searchDebounceTime":300}  at pages/table/detail.vue:261
18:24:47.341 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:24:47.341 有效列数量:,  [Number] 7  at pages/table/detail.vue:545
18:24:47.371 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:47.371 表 articles 数据加载完成，共 152 行，是否都有rowid: true at pages/table/detail.vue:468
18:24:47.758 查询表 articles 数据，是否包含rowid: true at utils/sqlite.js:617
18:24:47.758 表 articles 数据加载完成，共 152 行，是否都有rowid: true at pages/table/detail.vue:468
