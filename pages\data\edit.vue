<template>
	<view class="content">
		<AppHeader
			:title="tableName"
			:subtitle="getTableDisplayName(tableName)"
		/>

		<view class="form-container">
			<view v-if="isLoading" class="loading-tip">
				<text>加载中...</text>
			</view>

			<view v-else-if="columns.length === 0" class="empty-tip">
				<text>没有列定义</text>
			</view>

			<view v-else>
				<!-- 数据编辑表单 -->
				<template v-for="(column, index) in columns" :key="column ? column.id : index">
					<view
						v-if="column && !isHidden(column)"
						class="form-item"
					>
						<text class="form-label">{{ column.name }} ({{ column.type }})</text>

						<!-- 外键选择器 -->
						<view
							v-if="column.is_foreign_key === 1 && foreignKeyData[column.id] && foreignKeyData[column.id].length > 0"
							class="input-container"
						>
							<picker
								:value="formData[column.name]?.index || 0"
								:range="foreignKeyData[column.id]"
								@change="(e) => onForeignKeyChange(column, e)"
							>
								<view class="picker-view">
									<text v-if="formData[column.name]?.value">{{ formData[column.name].value }}</text>
									<text v-else class="picker-placeholder">请选择{{ column.name }}</text>
								</view>
							</picker>
						</view>

						<!-- 普通输入框 -->
						<view v-else class="input-container">
							<input
								class="form-input"
								:value="formData[column.name]"
								@input="formData[column.name] = $event.detail.value"
								:placeholder="getPlaceholder(column)"
								:disabled="isDisabled(column)"
								:type="getInputType(column)"
							/>
						</view>

						<text v-if="errors[column.name]" class="form-error">{{ errors[column.name] }}</text>
					</view>
				</template>

				<!-- 保存按钮 -->
				<button class="save-button" @click="saveData" :disabled="isSaving">
					{{ isSaving ? '保存中...' : '保存数据' }}
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getTableColumns,
		getForeignKeyData,
		updateTableRow
	} from '@/utils/sqlite.js';
	import { TableUtils } from '@/config/tables.js';
	import { FormUtils } from '@/utils/form.js';
	import AppHeader from '@/components/AppHeader.vue';

	export default {
		components: {
			AppHeader
		},
		data() {
			return {
				tableId: 0,
				tableName: '',
				rowId: 0,
				rowData: null,
				columns: [],
				formData: {},
				foreignKeyData: {},
				errors: {},
				isLoading: false,
				isSaving: false
			}
		},
		onLoad(options) {
			this.tableId = parseInt(options.tableId) || 0;
			this.tableName = options.tableName || '';
			this.rowId = parseInt(options.rowId) || 0;

			// 解析行数据
			if (options.rowData) {
				try {
					this.rowData = JSON.parse(decodeURIComponent(options.rowData));
				} catch (e) {
					console.error('解析行数据失败', e);
				}
			}

			// 加载表格列信息
			this.loadColumns();
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 加载列信息
			async loadColumns() {
				this.isLoading = true;

				try {
					// 获取列信息
					const columns = await getTableColumns(this.tableId);
					this.columns = columns.sort((a, b) => a.order_index - b.order_index);

					// 初始化表单数据
					this.initFormData();

					// 加载外键数据
					await this.loadForeignKeyData();
				} catch (e) {
					console.error('加载列信息失败', e);
					uni.showToast({
						title: '加载列信息失败',
						icon: 'none'
					});
				} finally {
					this.isLoading = false;
				}
			},

			// 获取表的中文显示名称
			getTableDisplayName(tableName) {
				return TableUtils.getTableDisplayName(tableName);
			},

			// 初始化表单数据
			initFormData() {
				this.formData = FormUtils.initFormData(this.columns, this.rowData);
			},

			// 加载外键数据
			async loadForeignKeyData() {
				const foreignKeyData = {};

				for (const column of this.columns) {
					// 跳过隐藏字段
					if (this.isHidden(column)) {
						continue;
					}

					if (column.is_foreign_key === 1 && column.reference_table_id && column.reference_column_id) {
						try {
							const data = await getForeignKeyData(column.reference_table_id, column.reference_column_id);
							foreignKeyData[column.id] = data;

							// 如果有行数据，找到对应的外键索引
							if (this.rowData && this.rowData[column.name] !== undefined) {
								const value = this.rowData[column.name];
								const index = data.findIndex(item => item === value);
								if (index >= 0) {
									this.formData[column.name] = {
										index: index,
										value: data[index]
									};
								}
							}
						} catch (e) {
							console.error('加载外键数据失败', e);
						}
					}
				}

				this.foreignKeyData = foreignKeyData;
			},

			// 外键选择变更
			onForeignKeyChange(column, e) {
				const index = e.detail.value;
				const data = this.foreignKeyData[column.id];

				if (data && index >= 0 && index < data.length) {
					this.formData[column.name] = {
						index: index,
						value: data[index]
					};
				}
			},

			// 获取输入框占位符
			getPlaceholder(column) {
				return FormUtils.getPlaceholder(column);
			},

			// 判断输入框是否禁用
			isDisabled(column) {
				return FormUtils.isDisabled(column);
			},

			// 获取输入框类型
			getInputType(column) {
				return FormUtils.getInputType(column);
			},

			// 判断列是否隐藏
			isHidden(column) {
				return TableUtils.isHiddenColumn(column);
			},

			// 验证表单数据
			validateForm() {
				const { isValid, errors } = FormUtils.validateForm(this.columns, this.formData, this.isHidden);
				this.errors = errors;
				return isValid;
			},

			// 保存数据
			async saveData() {
				// 验证表单
				if (!this.validateForm()) {
					return;
				}

				// 设置保存状态
				this.isSaving = true;

				try {
					// 准备数据
					const data = FormUtils.prepareSaveData(this.columns, this.formData, this.isHidden);

					// 显示加载提示
					uni.showLoading({
						title: '保存中...'
					});

					// 更新数据
					const success = await updateTableRow(this.tableName, this.rowId, data);

					// 隐藏加载提示
					uni.hideLoading();

					if (success) {
						uni.showToast({
							title: '数据更新成功',
							icon: 'success'
						});

						// 返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else {
						uni.showToast({
							title: '数据更新失败',
							icon: 'none'
						});
					}
				} catch (e) {
					// 隐藏加载提示
					uni.hideLoading();

					console.error('保存数据失败', e);
					uni.showToast({
						title: '保存数据失败: ' + (e.message || e),
						icon: 'none'
					});
				} finally {
					// 重置保存状态
					this.isSaving = false;
				}
			}
		}
	}
</script>

<style>
	@import '@/static/styles/form.css';

	/* 页面特定样式 */
	.input-container {
		border: 1px solid #DDDDDD;
	}

	.form-input, .picker-view {
		min-width: 500rpx;
		min-height: 80rpx;
		border: none;
	}

	.save-button[disabled] {
		opacity: 0.7;
	}
</style>
