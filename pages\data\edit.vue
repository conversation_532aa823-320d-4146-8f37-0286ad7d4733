<template>
	<view class="content">
		<view class="header">
			<view class="header-left" @click="goBack">
				<text class="header-back">返回</text>
			</view>
			<text class="header-title">编辑数据: {{ tableName }}</text>
			<view class="header-right"></view>
		</view>

		<view class="form-container">
			<view v-if="isLoading" class="loading-tip">
				<text>加载中...</text>
			</view>

			<view v-else-if="columns.length === 0" class="empty-tip">
				<text>没有列定义</text>
			</view>

			<view v-else>
				<!-- 数据编辑表单 -->
				<template v-for="(column, index) in columns" :key="column ? column.id : index">
					<view
						v-if="column && !isHidden(column)"
						class="form-item"
					>
						<text class="form-label">{{ column.name }} ({{ column.type }})</text>

						<!-- 外键选择器 -->
						<view
							v-if="column.is_foreign_key === 1 && foreignKeyData[column.id] && foreignKeyData[column.id].length > 0"
							class="input-container"
						>
							<picker
								:value="formData[column.name]?.index || 0"
								:range="foreignKeyData[column.id]"
								@change="(e) => onForeignKeyChange(column, e)"
							>
								<view class="picker-view">
									<text v-if="formData[column.name]?.value">{{ formData[column.name].value }}</text>
									<text v-else class="picker-placeholder">请选择{{ column.name }}</text>
								</view>
							</picker>
						</view>

						<!-- 普通输入框 -->
						<view v-else class="input-container">
							<input
								class="form-input"
								:value="formData[column.name]"
								@input="formData[column.name] = $event.detail.value"
								:placeholder="getPlaceholder(column)"
								:disabled="isDisabled(column)"
								:type="getInputType(column)"
							/>
						</view>

						<text v-if="errors[column.name]" class="form-error">{{ errors[column.name] }}</text>
					</view>
				</template>

				<!-- 保存按钮 -->
				<button class="save-button" @click="saveData" :disabled="isSaving">
					{{ isSaving ? '保存中...' : '保存数据' }}
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getTableColumns,
		getForeignKeyData,
		updateTableRow
	} from '@/utils/sqlite.js';

	export default {
		data() {
			return {
				tableId: 0,
				tableName: '',
				rowId: 0,
				rowData: null,
				columns: [],
				formData: {},
				foreignKeyData: {},
				errors: {},
				isLoading: false,
				isSaving: false
			}
		},
		onLoad(options) {
			this.tableId = parseInt(options.tableId) || 0;
			this.tableName = options.tableName || '';
			this.rowId = parseInt(options.rowId) || 0;

			// 解析行数据
			if (options.rowData) {
				try {
					this.rowData = JSON.parse(decodeURIComponent(options.rowData));
				} catch (e) {
					console.error('解析行数据失败', e);
				}
			}

			// 加载表格列信息
			this.loadColumns();
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 加载列信息
			async loadColumns() {
				this.isLoading = true;

				try {
					// 获取列信息
					const columns = await getTableColumns(this.tableId);
					this.columns = columns.sort((a, b) => a.order_index - b.order_index);

					// 初始化表单数据
					this.initFormData();

					// 加载外键数据
					await this.loadForeignKeyData();
				} catch (e) {
					console.error('加载列信息失败', e);
					uni.showToast({
						title: '加载列信息失败',
						icon: 'none'
					});
				} finally {
					this.isLoading = false;
				}
			},

			// 初始化表单数据
			initFormData() {
				const formData = {};

				for (const column of this.columns) {
					// 如果是自增主键或隐藏字段，不需要输入
					if (column.is_primary_key === 1 && column.type === 'INTEGER' || this.isHidden(column)) {
						continue;
					}

					// 如果有行数据，使用行数据初始化
					if (this.rowData && this.rowData[column.name] !== undefined) {
						formData[column.name] = this.rowData[column.name];
					} else {
						formData[column.name] = '';
					}
				}

				this.formData = formData;
			},

			// 加载外键数据
			async loadForeignKeyData() {
				const foreignKeyData = {};

				for (const column of this.columns) {
					// 跳过隐藏字段
					if (this.isHidden(column)) {
						continue;
					}

					if (column.is_foreign_key === 1 && column.reference_table_id && column.reference_column_id) {
						try {
							const data = await getForeignKeyData(column.reference_table_id, column.reference_column_id);
							foreignKeyData[column.id] = data;

							// 如果有行数据，找到对应的外键索引
							if (this.rowData && this.rowData[column.name] !== undefined) {
								const value = this.rowData[column.name];
								const index = data.findIndex(item => item === value);
								if (index >= 0) {
									this.formData[column.name] = {
										index: index,
										value: data[index]
									};
								}
							}
						} catch (e) {
							console.error('加载外键数据失败', e);
						}
					}
				}

				this.foreignKeyData = foreignKeyData;
			},

			// 外键选择变更
			onForeignKeyChange(column, e) {
				const index = e.detail.value;
				const data = this.foreignKeyData[column.id];

				if (data && index >= 0 && index < data.length) {
					this.formData[column.name] = {
						index: index,
						value: data[index]
					};
				}
			},

			// 获取输入框占位符
			getPlaceholder(column) {
				if (column.is_primary_key === 1 && column.type === 'INTEGER') {
					return '自动生成';
				}
				return `请输入${column.name}`;
			},

			// 判断输入框是否禁用
			isDisabled(column) {
				return column.is_primary_key === 1 && column.type === 'INTEGER';
			},

			// 获取输入框类型
			getInputType(column) {
				switch (column.type) {
					case 'INTEGER': return 'number';
					case 'REAL': return 'digit';
					default: return 'text';
				}
			},

			// 判断列是否隐藏
			isHidden(column) {
				if (!column) return true; // 如果列不存在，则隐藏

				// 检查是否有is_hidden标记
				if (column.is_hidden === 1) return true;

				// 检查是否是ID、创建时间或更新时间字段
				const hiddenFields = ['id', 'createTime', 'updateTime'];

				// 打印调试信息
				console.log(`检查列 ${column.name} 是否隐藏:`,
					hiddenFields.includes(column.name) ? '是' : '否',
					'is_hidden =', column.is_hidden);

				return hiddenFields.includes(column.name);
			},

			// 验证表单数据
			validateForm() {
				const errors = {};
				let isValid = true;

				for (const column of this.columns) {
					// 跳过自增主键和隐藏字段
					if (column.is_primary_key === 1 && column.type === 'INTEGER' || this.isHidden(column)) {
						continue;
					}

					const value = this.formData[column.name];

					// 检查必填字段
					if (column.is_not_null === 1) {
						if (!value || (typeof value === 'object' && !value.value)) {
							errors[column.name] = `${column.name} 不能为空`;
							isValid = false;
							continue;
						}
					}

					// 检查数据类型
					if (value) {
						const actualValue = typeof value === 'object' ? value.value : value;

						if (column.type === 'INTEGER' && !/^-?\d+$/.test(actualValue)) {
							errors[column.name] = `${column.name} 必须是整数`;
							isValid = false;
						} else if (column.type === 'REAL' && !/^-?\d+(\.\d+)?$/.test(actualValue)) {
							errors[column.name] = `${column.name} 必须是数字`;
							isValid = false;
						}
					}
				}

				this.errors = errors;
				return isValid;
			},

			// 保存数据
			async saveData() {
				// 验证表单
				if (!this.validateForm()) {
					return;
				}

				// 设置保存状态
				this.isSaving = true;

				try {
					// 准备数据
					const data = {};

					for (const column of this.columns) {
						// 跳过自增主键和隐藏字段
						if (column.is_primary_key === 1 && column.type === 'INTEGER' || this.isHidden(column)) {
							continue;
						}

						const value = this.formData[column.name];

						if (value !== undefined) {
							// 处理外键值
							if (typeof value === 'object' && value.value) {
								data[column.name] = value.value;
							} else {
								data[column.name] = value;
							}
						}
					}

					// 显示加载提示
					uni.showLoading({
						title: '保存中...'
					});

					// 更新数据
					const success = await updateTableRow(this.tableName, this.rowId, data);

					// 隐藏加载提示
					uni.hideLoading();

					if (success) {
						uni.showToast({
							title: '数据更新成功',
							icon: 'success'
						});

						// 返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else {
						uni.showToast({
							title: '数据更新失败',
							icon: 'none'
						});
					}
				} catch (e) {
					// 隐藏加载提示
					uni.hideLoading();

					console.error('保存数据失败', e);
					uni.showToast({
						title: '保存数据失败: ' + (e.message || e),
						icon: 'none'
					});
				} finally {
					// 重置保存状态
					this.isSaving = false;
				}
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 20rpx 30rpx;
		padding-top: var(--status-bar-height);
	}

	.header-left, .header-right {
		width: 120rpx;
	}

	.header-back {
		color: #FFFFFF;
		font-size: 28rpx;
	}

	.header-title {
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: bold;
		max-width: 400rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.form-container {
		flex: 1;
		padding: 30rpx;
	}

	.empty-tip, .loading-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 300rpx;
		background-color: #FFFFFF;
		border-radius: 8rpx;
		color: #999999;
		font-size: 28rpx;
	}

	.loading-tip {
		background-color: #F8F8F8;
	}

	.form-item {
		margin-bottom: 30rpx;
	}

	.form-label {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
	}

	.input-container {
		width: 100%;
		border: 1px solid #DDDDDD;
		border-radius: 8rpx;
		background-color: #FFFFFF;
		padding: 0;
		margin: 0;
	}

	.form-input, .picker-view {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		width: 100%;
		min-width: 500rpx;
		min-height: 80rpx;
		box-sizing: border-box;
		margin: 0;
		border: none;
	}

	.form-input:disabled {
		background-color: #F0F0F0;
		color: #999999;
	}

	.picker-placeholder {
		color: #999999;
	}

	.form-error {
		color: #FF0000;
		font-size: 24rpx;
		margin-top: 10rpx;
	}

	.save-button {
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 32rpx;
		padding: 20rpx;
		border-radius: 8rpx;
		margin-top: 30rpx;
	}

	.save-button[disabled] {
		background-color: #CCCCCC;
		color: #FFFFFF;
		opacity: 0.7;
	}
</style>
