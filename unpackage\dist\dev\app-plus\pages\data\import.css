
.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 0.625rem 0.9375rem;
		padding-top: var(--status-bar-height);
}
.header-left, .header-right {
		width: 3.75rem;
}
.header-back {
		color: #FFFFFF;
		font-size: 0.875rem;
}
.header-title {
		color: #FFFFFF;
		font-size: 1.125rem;
		font-weight: bold;
		max-width: 12.5rem;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
}
.import-container {
		flex: 1;
		padding: 0.9375rem;
		display: flex;
		flex-direction: column;
}
.import-options {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem;
		margin-bottom: 0.625rem;
}
.option-group {
		margin-bottom: 0.625rem;
}
.option-label {
		font-size: 0.875rem;
		font-weight: bold;
		margin-bottom: 0.3125rem;
		display: block;
}
.format-options, .import-methods {
		display: flex;
		flex-wrap: wrap;
}
.format-option, .method-option {
		padding: 0.3125rem 0.625rem;
		border: 0.03125rem solid #DDDDDD;
		border-radius: 0.25rem;
		margin-right: 0.625rem;
		margin-bottom: 0.3125rem;
}
.format-option-selected, .method-option-selected {
		background-color: #007AFF;
		border-color: #007AFF;
}
.format-text, .method-text {
		font-size: 0.875rem;
}
.format-option-selected .format-text,
	.method-option-selected .method-text {
		color: #FFFFFF;
}
.file-select {
		margin-top: 0.625rem;
}
.file-button {
		background-color: #F0F0F0;
		color: #333333;
		font-size: 0.875rem;
		padding: 0.3125rem 0.625rem;
		border-radius: 0.25rem;
		margin-bottom: 0.3125rem;
}
.file-name {
		font-size: 0.75rem;
		color: #666666;
}
.text-input-container {
		margin-top: 0.625rem;
}
.text-input {
		width: 100%;
		min-height: 6.25rem;
		border: 0.03125rem solid #DDDDDD;
		border-radius: 0.25rem;
		padding: 0.625rem;
		font-size: 0.875rem;
		background-color: #FFFFFF;
}
.format-help {
		margin-top: 0.625rem;
		background-color: #F8F8F8;
		border-radius: 0.25rem;
		padding: 0.625rem;
}
.format-help-title {
		font-size: 0.8125rem;
		font-weight: bold;
		margin-bottom: 0.3125rem;
		display: block;
}
.format-help-text {
		font-size: 0.75rem;
		color: #666666;
		display: block;
		white-space: pre-wrap;
		word-break: break-all;
		margin-bottom: 0.3125rem;
}
.format-help-action {
		font-size: 0.75rem;
		color: #007AFF;
		display: block;
		text-align: right;
}
.format-help-note {
		font-size: 0.75rem;
		color: #FF6600;
		display: block;
		margin-top: 0.3125rem;
		padding: 0.3125rem;
		background-color: #FFF8F0;
		border-left: 0.125rem solid #FF6600;
		border-radius: 0.125rem;
}
.preview-section {
		flex: 1;
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem;
		margin-bottom: 0.625rem;
		display: flex;
		flex-direction: column;
}
.preview-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 0.625rem;
}
.preview-title {
		font-size: 0.875rem;
		font-weight: bold;
}
.preview-count {
		font-size: 0.75rem;
		color: #666666;
}
.loading-tip, .empty-tip {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #999999;
		font-size: 0.875rem;
}
.preview-table {
		flex: 1;
		display: flex;
		flex-direction: column;
}
.preview-header-scroll {
		background-color: #F8F8F8;
}
.preview-row {
		display: flex;
		border-bottom: 0.03125rem solid #EEEEEE;
}
.preview-row-even {
		background-color: #F8F8F8;
}
.preview-header-row {
		background-color: #F0F0F0;
		border-bottom: 0.03125rem solid #DDDDDD;
}
.preview-cell {
		min-width: 6.25rem;
		padding: 0.3125rem;
		word-break: break-all;
}
.preview-header-cell {
		font-weight: bold;
}
.preview-header-text {
		font-size: 0.75rem;
}
.preview-cell-text {
		font-size: 0.75rem;
}
.preview-body {
		flex: 1;
}
.preview-more {
		padding: 0.3125rem;
		text-align: center;
}
.preview-more-text {
		font-size: 0.75rem;
		color: #999999;
}
.import-actions {
		margin-top: 0.625rem;
}
.import-button {
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 1rem;
		padding: 0.625rem;
		border-radius: 0.25rem;
}
.import-button[disabled] {
		background-color: #CCCCCC;
		color: #FFFFFF;
		opacity: 0.7;
}
