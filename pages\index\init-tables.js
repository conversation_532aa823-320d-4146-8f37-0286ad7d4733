/**
 * 初始化表格
 * 直接创建文档表和条文表
 */
import { executeSql, selectSql, dropAllTablesWithName } from '@/utils/sqlite.js';
import { DB_CONFIG } from '@/config/index.js';

// 系统表名
const TABLE_TABLES = DB_CONFIG.systemTables.tables;
const TABLE_COLUMNS = DB_CONFIG.systemTables.columns;

/**
 * 初始化文档表和条文表
 * @param {boolean} forceRecreate 是否强制重新创建表（默认为false）
 */
export async function initDocumentAndArticleTables(forceRecreate = false) {
  try {
    console.log('开始初始化文档表和条文表', forceRecreate ? '(强制重新创建)' : '');

    // 注册表到系统表（这个函数会处理表的创建和注册）
    await registerTablesToSystem(forceRecreate);

    console.log('文档表和条文表初始化完成');
    return true;
  } catch (e) {
    console.error('初始化文档表和条文表失败', e);
    return false;
  }
}

/**
 * 创建文档表
 */
async function createDocumentTable() {
  try {
    // 创建文档表
    await executeSql(`
      CREATE TABLE IF NOT EXISTS documents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        fileName TEXT NOT NULL UNIQUE,
        category TEXT NOT NULL,
        documentNumber TEXT NOT NULL,
        publishDate TEXT,
        implementDate TEXT,
        publishingUnit TEXT,
        createTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
        updateTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
      )
    `);

    console.log('文档表创建成功');
    return true;
  } catch (e) {
    console.error('创建文档表失败', e);
    throw e;
  }
}

/**
 * 创建条文表
 */
async function createArticleTable() {
  try {
    // 创建条文表
    await executeSql(`
      CREATE TABLE IF NOT EXISTS articles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        fileName TEXT NOT NULL,
        articleType TEXT NOT NULL,
        articleContent TEXT NOT NULL,
        keywords TEXT,
        createTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
        updateTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
        FOREIGN KEY (fileName) REFERENCES documents(fileName) ON DELETE CASCADE
      )
    `);

    console.log('条文表创建成功');
    return true;
  } catch (e) {
    console.error('创建条文表失败', e);
    throw e;
  }
}

/**
 * 注册表到系统表
 * @param {boolean} forceRecreate 是否强制重新创建表（默认为false）
 */
async function registerTablesToSystem(forceRecreate = false) {
  try {
    // 检查文档表是否存在
    const documentsExists = await checkTableExistsInSystem('documents');
    // 检查条文表是否存在
    const articlesExists = await checkTableExistsInSystem('articles');

    console.log('检查表是否存在:', { documentsExists, articlesExists });

    // 如果两个表都已存在且不强制重新创建，则直接返回
    if (documentsExists && articlesExists && !forceRecreate) {
      console.log('文档表和条文表已存在，无需重新创建');
      return true;
    }

    // 如果需要重新创建文档表
    if (!documentsExists || forceRecreate) {
      // 1. 删除所有名为 documents 的表
      await dropAllTablesWithName('documents');
      console.log('已删除所有名为 documents 的表');

      // 2. 创建文档表
      await createDocumentTable();
      console.log('文档表已重新创建');

      // 3. 注册文档表
      await registerDocumentTable();
      console.log('文档表已注册到系统表');
    }

    // 如果需要重新创建条文表
    if (!articlesExists || forceRecreate) {
      // 4. 删除所有名为 articles 的表
      await dropAllTablesWithName('articles');
      console.log('已删除所有名为 articles 的表');

      // 5. 创建条文表
      await createArticleTable();
      console.log('条文表已重新创建');

      // 6. 注册条文表
      await registerArticleTable();
      console.log('条文表已注册到系统表');
    }

    return true;
  } catch (e) {
    console.error('注册表到系统表失败', e);
    throw e;
  }
}

/**
 * 检查表是否已在系统表中注册
 */
async function checkTableExistsInSystem(tableName) {
  try {
    const result = await selectSql(`SELECT COUNT(*) as count FROM ${TABLE_TABLES} WHERE name = '${tableName}'`);
    return result[0].count > 0;
  } catch (e) {
    console.error('检查表是否存在失败', e);
    return false;
  }
}

/**
 * 从系统表中删除表记录
 * @param {string} tableName 表名
 * @returns {Promise<boolean>} 是否成功
 */
async function removeTableFromSystem(tableName) {
  try {
    // 1. 获取表ID
    const tables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = '${tableName}'`);
    if (!tables || tables.length === 0) {
      console.log(`表 ${tableName} 不存在于系统表中`);
      return true;
    }

    const tableId = tables[0].id;

    // 2. 删除表的列记录
    await executeSql(`DELETE FROM ${TABLE_COLUMNS} WHERE table_id = ${tableId}`);
    console.log(`已删除表 ${tableName} 的列记录`);

    // 3. 删除表记录
    await executeSql(`DELETE FROM ${TABLE_TABLES} WHERE id = ${tableId}`);
    console.log(`已删除表 ${tableName} 的记录`);

    return true;
  } catch (e) {
    console.error(`从系统表中删除表 ${tableName} 失败`, e);
    throw e;
  }
}

/**
 * 注册文档表
 */
async function registerDocumentTable() {
  try {
    // 1. 插入表信息
    await executeSql(`
      INSERT INTO ${TABLE_TABLES} (name, description, created_at)
      VALUES ('documents', '文档主表，存储文件的基本信息', ${Date.now()})
    `);

    // 2. 获取表ID
    const tables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = 'documents'`);
    if (!tables || tables.length === 0) {
      throw new Error('获取文档表ID失败');
    }

    const tableId = tables[0].id;

    // 3. 插入列信息
    const columns = [
      { name: 'id', type: 'INTEGER', isPrimaryKey: 1, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: true },
      { name: 'fileName', type: 'TEXT', isPrimaryKey: 0, isNotNull: 1, isUnique: 1, isForeignKey: 0, isHidden: false },
      { name: 'category', type: 'TEXT', isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: false,
        options: ['法律', '法规', '规章', '规范性文件', '标准/规范', '规程', '其他'] },
      { name: 'documentNumber', type: 'TEXT', isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: false },
      { name: 'publishDate', type: 'TEXT', isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
      { name: 'implementDate', type: 'TEXT', isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
      { name: 'publishingUnit', type: 'TEXT', isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
      { name: 'createTime', type: 'TEXT', isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: true,
        defaultValue: "datetime('now', 'localtime')" },
      { name: 'updateTime', type: 'TEXT', isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: true,
        defaultValue: "datetime('now', 'localtime')" }
    ];

    for (let i = 0; i < columns.length; i++) {
      const column = columns[i];
      // 构建SQL语句，根据列是否存在来添加字段
      let sql = `
        INSERT INTO ${TABLE_COLUMNS} (
          table_id, name, type, is_primary_key, is_not_null, is_unique,
          is_foreign_key, reference_table_id, reference_column_id, order_index
        ) VALUES (
          ${tableId}, '${column.name}', '${column.type}', ${column.isPrimaryKey},
          ${column.isNotNull}, ${column.isUnique}, ${column.isForeignKey},
          NULL, NULL, ${i}
        )
      `;

      await executeSql(sql);

      // 获取刚插入的列ID
      const result = await selectSql(`SELECT id FROM ${TABLE_COLUMNS} WHERE table_id = ${tableId} AND name = '${column.name}'`);
      if (result && result.length > 0) {
        const columnId = result[0].id;

        // 更新额外的字段
        if (column.isHidden !== undefined) {
          await executeSql(`UPDATE ${TABLE_COLUMNS} SET is_hidden = ${column.isHidden ? 1 : 0} WHERE id = ${columnId}`);
        }

        if (column.options) {
          const optionsJson = JSON.stringify(column.options);
          console.log(`设置 ${column.name} 的选项:`, optionsJson);
          await executeSql(`UPDATE ${TABLE_COLUMNS} SET options = '${optionsJson}' WHERE id = ${columnId}`);
        }

        if (column.defaultValue) {
          // 使用双引号包裹整个SQL语句，使用单引号包裹default_value值
          await executeSql(`UPDATE ${TABLE_COLUMNS} SET default_value = ${JSON.stringify(column.defaultValue)} WHERE id = ${columnId}`);
        }
      }
    }

    console.log('文档表注册成功');
    return true;
  } catch (e) {
    console.error('注册文档表失败', e);
    throw e;
  }
}

/**
 * 注册条文表
 */
async function registerArticleTable() {
  try {
    // 1. 插入表信息
    await executeSql(`
      INSERT INTO ${TABLE_TABLES} (name, description, created_at)
      VALUES ('articles', '条文表，存储文件中的条文内容', ${Date.now()})
    `);

    // 2. 获取表ID
    const tables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = 'articles'`);
    if (!tables || tables.length === 0) {
      throw new Error('获取条文表ID失败');
    }

    const tableId = tables[0].id;

    // 3. 获取文档表ID和fileName列ID
    const documentTables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = 'documents'`);
    let documentTableId = null;
    let fileNameColumnId = null;

    if (documentTables && documentTables.length > 0) {
      documentTableId = documentTables[0].id;

      // 获取fileName列ID
      const fileNameColumns = await selectSql(`
        SELECT id FROM ${TABLE_COLUMNS}
        WHERE table_id = ${documentTableId} AND name = 'fileName'
      `);

      if (fileNameColumns && fileNameColumns.length > 0) {
        fileNameColumnId = fileNameColumns[0].id;
      }
    }

    // 4. 插入列信息
    const columns = [
      { name: 'id', type: 'INTEGER', isPrimaryKey: 1, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: true },
      {
        name: 'fileName',
        type: 'TEXT',
        isPrimaryKey: 0,
        isNotNull: 1,
        isUnique: 0,
        isForeignKey: documentTableId && fileNameColumnId ? 1 : 0,
        referenceTableId: documentTableId,
        referenceColumnId: fileNameColumnId,
        isHidden: false
      },
      { name: 'articleType', type: 'TEXT', isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: false,
        options: ['主体行为', '设施设备', '物料', '方法措施', '作业环境', '应急管理', '资料管理'] },
      { name: 'articleContent', type: 'TEXT', isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: false },
      { name: 'keywords', type: 'TEXT', isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
      { name: 'createTime', type: 'TEXT', isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: true,
        defaultValue: "datetime('now', 'localtime')" },
      { name: 'updateTime', type: 'TEXT', isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: true,
        defaultValue: "datetime('now', 'localtime')" }
    ];

    for (let i = 0; i < columns.length; i++) {
      const column = columns[i];
      // 构建SQL语句，根据列是否存在来添加字段
      let sql = `
        INSERT INTO ${TABLE_COLUMNS} (
          table_id, name, type, is_primary_key, is_not_null, is_unique,
          is_foreign_key, reference_table_id, reference_column_id, order_index
        ) VALUES (
          ${tableId}, '${column.name}', '${column.type}', ${column.isPrimaryKey},
          ${column.isNotNull}, ${column.isUnique}, ${column.isForeignKey},
          ${column.referenceTableId || 'NULL'}, ${column.referenceColumnId || 'NULL'}, ${i}
        )
      `;

      await executeSql(sql);

      // 获取刚插入的列ID
      const result = await selectSql(`SELECT id FROM ${TABLE_COLUMNS} WHERE table_id = ${tableId} AND name = '${column.name}'`);
      if (result && result.length > 0) {
        const columnId = result[0].id;

        // 更新额外的字段
        if (column.isHidden !== undefined) {
          await executeSql(`UPDATE ${TABLE_COLUMNS} SET is_hidden = ${column.isHidden ? 1 : 0} WHERE id = ${columnId}`);
        }

        if (column.options) {
          const optionsJson = JSON.stringify(column.options);
          console.log(`设置 ${column.name} 的选项:`, optionsJson);
          await executeSql(`UPDATE ${TABLE_COLUMNS} SET options = '${optionsJson}' WHERE id = ${columnId}`);
        }

        if (column.defaultValue) {
          // 使用双引号包裹整个SQL语句，使用单引号包裹default_value值
          await executeSql(`UPDATE ${TABLE_COLUMNS} SET default_value = ${JSON.stringify(column.defaultValue)} WHERE id = ${columnId}`);
        }
      }
    }

    console.log('条文表注册成功');
    return true;
  } catch (e) {
    console.error('注册条文表失败', e);
    throw e;
  }
}
