<template>
  <view class="container">
    <view class="header">
      <view class="header-content">
        <text class="title">设置</text>
      </view>
    </view>

    <view class="settings-list">
      <view class="settings-item" @tap="goToCategoryManagement">
        <text class="item-label">分类管理</text>
        <text class="item-arrow">></text>
      </view>
      <view class="settings-item" @tap="goToChangePassword">
        <text class="item-label">修改密码</text>
        <text class="item-arrow">></text>
      </view>
      <view class="settings-item" @tap="handleBackup">
        <text class="item-label">备份数据库</text>
        <text class="item-arrow">></text>
      </view>
      <view class="settings-item danger" @tap="handleClearData">
        <text class="item-label danger-text">清空数据</text>
        <text class="item-arrow">></text>
      </view>
      <view class="settings-item" @tap="goToApiSettings">
        <text class="item-label">DeepSeek API 设置</text>
        <text class="item-arrow">></text>
      </view>
      <view class="settings-item" @tap="goToContact">
        <text class="item-label">联系我们</text>
        <text class="item-arrow">></text>
      </view>
      <view class="settings-item" @tap="handleLogout">
        <text class="item-label">退出账户</text>
        <text class="item-arrow">></text>
      </view>
    </view>


  </view>
</template>

<script>
import { ref } from 'vue'
import { useStore } from 'vuex'
import { dbService } from '../../utils/db'

export default {
  setup() {
    const store = useStore()
    const isLoading = ref(false)

    // 跳转到修改密码页面
    const goToChangePassword = () => {
      uni.navigateTo({
        url: '/pages/change-password/change-password'
      })
    }

    // 处理数据备份
    const handleBackup = async () => {
      try {
        isLoading.value = true

        // 生成备份文件名
        const backupFileName = `myaccount_backup_${new Date().toISOString().slice(0,10)}.db`
        console.log('准备备份数据，文件名:', backupFileName)

        try {
          console.log('开始请求存储权限')

          // 请求文件系统权限
          await new Promise((resolve, reject) => {
            plus.android.requestPermissions(
              ['android.permission.WRITE_EXTERNAL_STORAGE'],
              function(resultObj) {
                console.log('权限请求结果:', resultObj)
                if (resultObj.granted.length === 1) {
                  resolve()
                } else {
                  reject(new Error('未授予存储权限'))
                }
              },
              function(error) {
                console.error('权限请求失败:', error)
                reject(error)
              }
            )
          })

          console.log('已获得存储权限，准备导出数据')

          // 使用原生 Java 方法
          const Environment = plus.android.importClass("android.os.Environment")
          const File = plus.android.importClass("java.io.File")

          // 获取当前应用的主Activity
          const main = plus.android.runtimeMainActivity()

          // 获取外部存储路径
          const externalStorageDir = Environment.getExternalStorageDirectory()
          const filePath = externalStorageDir.getAbsolutePath() + "/Download/" + backupFileName
          console.log('使用外部存储路径:', filePath)

          // 创建目录
          const fileDir = new File(filePath).getParentFile()
          if (!fileDir.exists()) {
            fileDir.mkdirs()
          }

          // 使用直接复制方法备份数据库
          console.log('使用直接复制方法备份数据库到:', filePath)
          const bytesCopied = await dbService.backupDatabaseByFileCopy(filePath)

          if (bytesCopied > 0) {
            // 先显示文件保存路径的弹窗
            await new Promise((resolve) => {
              uni.showModal({
                title: '备份成功',
                content: `数据已备份到以下路径：\n${filePath}`,
                showCancel: false,
                success: () => {
                  resolve()
                }
              })
            })

            // 用户确认后显示成功提示
            uni.showToast({
              title: '备份成功',
              icon: 'success',
              duration: 2000
            })
          } else {
            throw new Error('文件复制失败，复制了 0 字节')
          }
        } catch (error) {
          console.error('备份过程出错:', error)
          uni.showToast({
            title: '备份失败: ' + error.message,
            icon: 'none',
            duration: 2000
          })
        } finally {
          isLoading.value = false
        }
      } catch (error) {
        console.error('备份操作失败:', error)
        uni.showToast({
          title: '备份失败，请重试',
          icon: 'none',
          duration: 2000
        })
        isLoading.value = false
      }
    }

    // 跳转到联系我们页面
    const goToContact = () => {
      uni.navigateTo({
        url: '/pages/contact/contact'
      })
    }

    // 跳转到 API 设置页面
    const goToApiSettings = () => {
      uni.navigateTo({
        url: '/pages/api-settings/api-settings'
      })
    }

    // 处理退出登录
    const handleLogout = async () => {
      try {
        isLoading.value = true
        await store.dispatch('logout')
        uni.reLaunch({
          url: '/pages/login/login'
        })
      } catch (error) {
        uni.showToast({
          title: '退出失败，请重试',
          icon: 'none'
        })
      } finally {
        isLoading.value = false
      }
    }

    // 跳转到分类管理页面
    const goToCategoryManagement = () => {
      uni.navigateTo({
        url: '/pages/category-management/category-management'
      })
    }

    // 处理清空数据
    const handleClearData = async () => {
      // 显示确认对话框
      uni.showModal({
        title: '警告',
        content: '此操作将清空所有支出记录，但保留用户账户和分类信息。\n\n清空前将自动备份数据到下载文件夹。\n\n此操作不可恢复，确定要继续吗？',
        confirmText: '确定清空',
        confirmColor: '#f56c6c',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            try {
              isLoading.value = true

              // 再次确认
              uni.showModal({
                title: '再次确认',
                content: '您真的要清空所有支出记录吗？此操作不可恢复！',
                confirmText: '确定清空',
                confirmColor: '#f56c6c',
                cancelText: '取消',
                success: async (res2) => {
                  if (res2.confirm) {
                    try {
                      // 执行清空操作
                      const result = await store.dispatch('clearUserData')

                      if (result && result.success) {
                        uni.showModal({
                          title: '清空成功',
                          content: `所有支出记录已清空。\n\n数据备份已保存到下载文件夹：${result.backupFile}`,
                          showCancel: false,
                          success: () => {
                            // 跳转到首页刷新数据
                            uni.switchTab({
                              url: '/pages/index/index'
                            })
                          }
                        })
                      } else {
                        throw new Error('清空操作失败')
                      }
                    } catch (error) {
                      console.error('清空数据失败:', error)
                      uni.showToast({
                        title: error.message || '清空失败，请重试',
                        icon: 'none',
                        duration: 2000
                      })
                    } finally {
                      isLoading.value = false
                    }
                  } else {
                    isLoading.value = false
                  }
                }
              })
            } catch (error) {
              console.error('清空数据失败:', error)
              uni.showToast({
                title: '操作失败，请重试',
                icon: 'none',
                duration: 2000
              })
              isLoading.value = false
            }
          }
        }
      })
    }



    return {
      goToChangePassword,
      handleBackup,
      goToContact,
      goToApiSettings,
      handleLogout,
      goToCategoryManagement,
      handleClearData
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: #4a90e2;
  padding: 30px 16px;
  color: white;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-top: 15px;
  }

  .title {
    color: white;
    font-size: 20px;
    font-weight: 500;
  }
}

.settings-list {
  margin: 12px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);


}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
  transition: background-color 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f5f5f5;
  }

  .item-label {
    font-size: 14px;
    color: #333;
  }

  .item-arrow {
    color: #999;
    font-size: 14px;
  }

  &.danger {
    background-color: rgba(245, 108, 108, 0.05);

    &:active {
      background-color: rgba(245, 108, 108, 0.1);
    }
  }
}

.danger-text {
  color: #f56c6c !important;
  font-weight: 500;
}


</style>