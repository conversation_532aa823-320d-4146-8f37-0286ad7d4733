# SQLite数据库管理器

一个简单易用的SQLite数据库管理工具，可以创建表、定义列、管理数据，支持编辑和删除数据，调整列顺序，以及数据的搜索、筛选、批量操作和导入导出功能。

## 功能特点

### 表管理
- 创建新表
- 查看表详情
- 删除表

### 列管理
- 添加新列
- 设置列属性（类型、主键、非空、唯一）
- 设置外键关联
- 调整列顺序（长按列定义）

### 数据管理
- 添加数据
- 编辑数据（长按数据行）
- 删除数据（长按数据行）
- 搜索和筛选数据
- 批量操作数据
- 导入数据（CSV、JSON）
- 导出数据（CSV、JSON）

## 项目结构

```
├── App.vue                 # 应用入口
├── main.js                 # 主入口文件
├── manifest.json           # 应用配置
├── pages.json              # 页面配置
├── README.md               # 项目说明
├── config/                 # 配置文件
│   └── index.js            # 应用配置
├── pages/                  # 页面文件
│   ├── column/             # 列管理页面
│   │   └── create.vue      # 创建列页面
│   ├── data/               # 数据管理页面
│   │   ├── edit.vue        # 编辑数据页面
│   │   ├── entry.vue       # 数据录入页面
│   │   └── import.vue      # 数据导入页面
│   ├── index/              # 主页
│   │   └── index.vue       # 主页面
│   │   └── init-tables.js  # 表初始化脚本
│   └── table/              # 表管理页面
│       ├── create.vue      # 创建表页面
│       ├── detail.vue      # 表详情页面
│       └── edit.vue        # 编辑表页面
└── utils/                  # 工具类
    ├── common.js           # 通用工具函数
    └── sqlite.js           # SQLite数据库工具类
```

## 开发指南

### 环境要求
- HBuilderX 3.0+
- Node.js 12.0+
- uni-app 框架

### 安装和运行
1. 克隆或下载项目到本地
2. 使用HBuilderX打开项目
3. 在HBuilderX中安装必要的插件（如果提示）
4. 运行项目到模拟器或真机
   - 点击HBuilderX顶部的"运行"按钮
   - 选择运行到"手机或模拟器"
   - 选择目标设备

### 项目配置
项目配置文件位于 `config/index.js`，包含以下配置：
- 应用信息配置：名称、版本、描述等
- 数据库配置：数据库名称、路径、系统表名
- 界面配置：长按触发时间、表格分页等
- 数据类型配置：支持的数据类型及描述
- 导出格式配置：支持的导出格式
- 筛选操作符配置：不同数据类型的筛选操作符
- 帮助信息配置：应用帮助文本

### 数据库结构
系统使用两个表来管理数据库结构：
- `sys_tables`: 存储表信息（ID、名称、描述、创建时间）
- `sys_columns`: 存储列信息（ID、表ID、名称、类型、约束等）

系统还预置了两个业务表：
- `documents`: 文档主表，存储文件的基本信息
- `articles`: 条文表，存储文件中的条文内容

### 自定义开发
1. 修改 `config/index.js` 中的配置
2. 根据需要修改页面和样式
3. 添加新功能时，建议在现有结构基础上扩展
4. 添加新表时，可参考 `pages/index/init-tables.js` 中的示例

## 使用说明

### 创建表
1. 在主页点击右下角"+"按钮
2. 输入表名和描述
3. 点击"创建表"按钮

### 添加列
1. 在表详情页点击右上角"操作"按钮
2. 选择"编辑表结构"
3. 点击"添加列"按钮
4. 输入列信息并保存

### 添加数据
1. 在表详情页切换到"数据内容"标签
2. 点击右上角"操作"按钮
3. 选择"添加数据"
4. 输入数据并保存

### 编辑/删除数据
1. 在表详情页的"数据内容"标签中
2. 长按数据行
3. 选择"编辑数据"或"删除数据"

### 调整列顺序
1. 在表详情页的"列定义"标签中
2. 长按要移动的列
3. 选择"上移一位"或"下移一位"

### 搜索和筛选数据
1. 在表详情页的"数据内容"标签中
2. 使用顶部搜索框输入关键字
3. 点击"筛选"按钮设置筛选条件

### 批量操作数据
1. 在表详情页的"数据内容"标签中
2. 点击"批量"按钮
3. 选择要操作的数据行
4. 执行批量操作

### 导入/导出数据
1. 在表详情页的"数据内容"标签中
2. 点击"导入"或"导出"按钮
3. 选择相应的格式和操作

## 扩展开发

本项目设计为模板应用，可以基于此开发更多功能：

### 添加新页面
1. 在 `pages` 目录下创建新页面
2. 在 `pages.json` 中注册页面
3. 添加导航逻辑

### 添加新功能
1. 在 `utils` 目录下添加新的工具类
2. 在 `config` 目录下添加新的配置
3. 在相应页面中实现功能

### 自定义样式
1. 修改 `App.vue` 中的全局样式
2. 修改各页面的局部样式

### 添加新的数据类型支持
1. 在 `config/index.js` 的 `DATA_TYPES` 中添加新的数据类型
2. 在相关页面中添加对应的处理逻辑

## 性能优化

为了提高应用性能，可以考虑以下几点：

1. 大数据量表格分页加载
2. 使用虚拟列表减少DOM节点
3. 优化SQL查询，添加适当的索引
4. 减少不必要的数据库操作
5. 使用防抖和节流优化用户输入

## 许可证
[MIT License](LICENSE)

## 作者
SQLite数据库管理器团队

## 版本历史
- v1.1.0: 重构优化代码，添加配置文件，改进UI交互，增加文档和条文表
- v1.0.1: 添加数据搜索、筛选、批量操作和导入导出功能
- v1.0.0: 初始版本，基本的表、列和数据管理功能
