<template>
	<view class="content">
		<view class="header">
			<view class="header-left" @click="goBack">
				<text class="header-back">返回</text>
			</view>
			<text class="header-title">导入数据: {{ tableName }}</text>
			<view class="header-right"></view>
		</view>

		<view class="import-container">
			<!-- 导入选项 -->
			<view class="import-options">
				<view class="option-group">
					<text class="option-label">导入格式:</text>
					<view class="format-options">
						<view
							v-for="(format, index) in formats"
							:key="index"
							class="format-option"
							:class="{ 'format-option-selected': selectedFormat === format.value }"
							@click="selectedFormat = format.value"
						>
							<text class="format-text">{{ format.label }}</text>
						</view>
					</view>
				</view>

				<view class="option-group">
					<text class="option-label">导入方式:</text>
					<view class="import-methods">
						<view
							v-for="(method, index) in importMethods"
							:key="index"
							class="method-option"
							:class="{ 'method-option-selected': selectedMethod === method.value }"
							@click="selectedMethod = method.value"
						>
							<text class="method-text">{{ method.label }}</text>
						</view>
					</view>
				</view>

				<!-- 文件选择 -->
				<view v-if="selectedMethod === 'file'" class="file-select">
					<button class="file-button" @click="selectFile">选择文件</button>
					<text v-if="selectedFile" class="file-name">已选择: {{ selectedFile }}</text>
				</view>

				<!-- 文本输入 -->
				<view v-else-if="selectedMethod === 'text'" class="text-input-container">
					<textarea
						class="text-input"
						v-model="importText"
						:placeholder="getPlaceholderByFormat()"
						auto-height
					></textarea>

					<view class="format-help">
						<text class="format-help-title">格式示例:</text>
						<text class="format-help-text">{{ getFormatExample() }}</text>
						<text class="format-help-action" @click="useExample">使用示例数据</text>
						<text class="format-help-note">注意: id、createTime、updateTime 字段会自动生成，导入数据中可以省略这些字段</text>
						<text class="format-help-note">注意: 对于条文表(articles)，必填字段包括 fileName、articleType、articleContent、keywords，如果缺少这些字段，将使用默认值</text>
						<text class="format-help-note">注意: articleType 字段值应为预定义的选项之一: 主体行为, 设施设备, 物料, 方法措施, 作业环境, 应急管理, 资料管理</text>
					</view>
				</view>
			</view>

			<!-- 预览区域 -->
			<view class="preview-section">
				<view class="preview-header">
					<text class="preview-title">数据预览</text>
					<text v-if="previewData.length > 0" class="preview-count">共 {{ previewData.length }} 条记录</text>
				</view>

				<view v-if="isLoading" class="loading-tip">
					<text>解析中...</text>
				</view>

				<view v-else-if="previewData.length === 0" class="empty-tip">
					<text>{{ importError || '暂无预览数据' }}</text>
				</view>

				<view v-else class="preview-table">
					<!-- 表头 -->
					<scroll-view class="preview-header-scroll" scroll-x>
						<view class="preview-row preview-header-row">
							<view
								v-for="(column, index) in previewColumns"
								:key="index"
								class="preview-cell preview-header-cell"
							>
								<text class="preview-header-text">{{ column }}</text>
							</view>
						</view>
					</scroll-view>

					<!-- 表内容 -->
					<scroll-view class="preview-body" scroll-x scroll-y>
						<view
							v-for="(row, rowIndex) in previewData.slice(0, 5)"
							:key="rowIndex"
							class="preview-row"
							:class="{ 'preview-row-even': rowIndex % 2 === 0 }"
						>
							<view
								v-for="(column, colIndex) in previewColumns"
								:key="colIndex"
								class="preview-cell"
							>
								<text class="preview-cell-text">{{ row[column] !== undefined ? row[column] : '' }}</text>
							</view>
						</view>
					</scroll-view>

					<view v-if="previewData.length > 5" class="preview-more">
						<text class="preview-more-text">显示前5条记录，共{{ previewData.length }}条</text>
					</view>
				</view>
			</view>

			<!-- 导入按钮 -->
			<view class="import-actions">
				<button
					class="import-button"
					:disabled="previewData.length === 0 || isImporting"
					@click="importData"
				>
					{{ isImporting ? '导入中...' : '导入数据' }}
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { executeSql, insertData, checkFileNameExists } from '@/utils/sqlite.js';
	import { EXPORT_FORMATS } from '@/config/index.js';
	import { showLoading, hideLoading, showToast, showConfirm } from '@/utils/common.js';

	export default {
		data() {
			return {
				tableId: 0,
				tableName: '',
				columns: [],

				// 导入选项
				formats: EXPORT_FORMATS,
				selectedFormat: 'csv',

				importMethods: [
					{ label: '文件', value: 'file' },
					{ label: '文本', value: 'text' }
				],
				selectedMethod: 'file',

				// 文件导入
				selectedFile: '',
				fileContent: '',

				// 文本导入
				importText: '',

				// 预览数据
				previewData: [],
				previewColumns: [],

				// 状态
				isLoading: false,
				isImporting: false,
				importError: ''
			}
		},
		onLoad(options) {
			this.tableId = parseInt(options.tableId) || 0;
			this.tableName = options.tableName || '';

			// 解析列信息
			if (options.columns) {
				try {
					this.columns = JSON.parse(decodeURIComponent(options.columns));
				} catch (e) {
					console.error('解析列信息失败', e);
				}
			}
		},
		watch: {
			// 监听导入文本变化
			importText() {
				if (this.importText) {
					this.parseImportData();
				} else {
					this.previewData = [];
					this.previewColumns = [];
					this.importError = '';
				}
			},

			// 监听格式变化
			selectedFormat() {
				// 清空预览
				this.previewData = [];
				this.previewColumns = [];
				this.importError = '';

				// 如果有数据，重新解析
				if (this.selectedMethod === 'file' && this.fileContent) {
					this.parseImportData();
				} else if (this.selectedMethod === 'text' && this.importText) {
					this.parseImportData();
				}
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 选择文件
			selectFile() {
				// #ifdef APP-PLUS
				// 在App环境中，使用Android原生方法从Download文件夹选择文件
				if (uni.getSystemInfoSync().platform === 'android') {
					this.selectFileFromDownload();
					return;
				}
				// #endif

				// 在iOS或其他平台上，使用文本输入方式
				if (uni.getSystemInfoSync().platform === 'ios') {
					uni.showModal({
						title: '提示',
						content: 'iOS平台暂不支持直接选择文件，请使用文本输入方式导入数据。',
						showCancel: false,
						success: () => {
							// 切换到文本输入模式
							this.selectedMethod = 'text';
						}
					});
					return;
				}

				// 以下代码仅在H5环境中有效
				// 使用uni-app的文件选择API
				// #ifdef H5
				uni.chooseFile({
					count: 1,
					extension: ['.csv', '.json', '.txt'],
					success: (res) => {
						const filePath = res.tempFilePaths[0];
						this.selectedFile = filePath.split('/').pop();

						// 读取文件内容
						this.readFile(filePath);
					},
					fail: (err) => {
						console.error('选择文件失败', err);
						if (err.errMsg !== 'chooseFile:fail cancel') {
							uni.showToast({
								title: '选择文件失败',
								icon: 'none'
							});
						}
					}
				});
				// #endif
			},

			// 从Download文件夹选择文件
			async selectFileFromDownload() {
				try {
					// 显示加载提示
					showLoading('正在查找文件...');

					// 请求存储权限
					await new Promise((resolve, reject) => {
						plus.android.requestPermissions(
							['android.permission.READ_EXTERNAL_STORAGE'],
							function(resultObj) {
								console.log('权限请求结果:', resultObj);
								if (resultObj.granted.length === 1) {
									resolve();
								} else {
									hideLoading(); // 隐藏加载提示
									reject(new Error('未授予存储权限'));
								}
							},
							function(error) {
								console.error('权限请求失败:', error);
								hideLoading(); // 隐藏加载提示
								reject(error);
							}
						);
					});

					console.log('已获得存储权限，准备选择文件');

					// 使用原生 Java 方法
					const Intent = plus.android.importClass("android.content.Intent");
					const Environment = plus.android.importClass("android.os.Environment");
					const Uri = plus.android.importClass("android.net.Uri");
					const File = plus.android.importClass("java.io.File");
					const FileInputStream = plus.android.importClass("java.io.FileInputStream");
					const BufferedReader = plus.android.importClass("java.io.BufferedReader");
					const InputStreamReader = plus.android.importClass("java.io.InputStreamReader");
					const Context = plus.android.importClass("android.content.Context");

					// 获取Download文件夹路径 - 尝试多种方法
					let downloadPath = '';

					try {
						// 方法1: 使用Environment.DIRECTORY_DOWNLOADS
						const downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
						if (downloadDir) {
							downloadPath = downloadDir.getAbsolutePath();
							console.log('方法1获取Download路径:', downloadPath);
						}
					} catch (e) {
						console.error('方法1获取Download路径失败:', e);
					}

					// 如果方法1失败，尝试方法2
					if (!downloadPath) {
						try {
							// 方法2: 使用Environment.getExternalStorageDirectory() + /Download
							const externalDir = Environment.getExternalStorageDirectory();
							if (externalDir) {
								downloadPath = externalDir.getAbsolutePath() + '/Download';
								console.log('方法2获取Download路径:', downloadPath);
							}
						} catch (e) {
							console.error('方法2获取Download路径失败:', e);
						}
					}

					// 如果方法2也失败，尝试方法3
					if (!downloadPath) {
						try {
							// 方法3: 使用固定路径
							downloadPath = '/storage/emulated/0/Download';
							console.log('方法3使用固定Download路径:', downloadPath);
						} catch (e) {
							console.error('方法3使用固定Download路径失败:', e);
						}
					}

					// 如果所有方法都失败，显示错误
					if (!downloadPath) {
						throw new Error('无法获取Download文件夹路径');
					}

					console.log('最终使用的Download文件夹路径:', downloadPath);

					// 列出Download文件夹中的文件
					const downloadFolder = new File(downloadPath);

					// 检查文件夹是否存在
					if (!downloadFolder.exists()) {
						console.error('Download文件夹不存在:', downloadPath);
						uni.showModal({
							title: '提示',
							content: 'Download文件夹不存在，请确保您的设备有Download文件夹',
							showCancel: false
						});
						return;
					}

					// 检查是否是文件夹
					if (!downloadFolder.isDirectory()) {
						console.error('Download路径不是文件夹:', downloadPath);
						uni.showModal({
							title: '提示',
							content: 'Download路径不是文件夹',
							showCancel: false
						});
						return;
					}

					// 检查是否有读取权限
					if (!downloadFolder.canRead()) {
						console.error('没有Download文件夹的读取权限:', downloadPath);
						uni.showModal({
							title: '提示',
							content: '没有Download文件夹的读取权限，请在设置中授予应用存储权限',
							showCancel: false
						});
						return;
					}

					// 列出文件
					const files = downloadFolder.listFiles();
					console.log('Download文件夹中的文件数量:', files ? files.length : 0);

					if (!files || files.length === 0) {
						uni.showModal({
							title: '提示',
							content: 'Download文件夹中没有文件',
							showCancel: false
						});
						return;
					}

					// 过滤出CSV和JSON文件
					const validFiles = [];
					for (let i = 0; i < files.length; i++) {
						try {
							const file = files[i];
							if (!file) {
								console.warn(`文件索引 ${i} 为null，跳过`);
								continue;
							}

							// 检查文件是否可读
							if (!file.canRead()) {
								console.warn(`文件不可读，跳过:`, file);
								continue;
							}

							// 检查是否是文件而不是目录
							if (!file.isFile()) {
								console.warn(`不是文件，跳过:`, file);
								continue;
							}

							const fileName = file.getName();
							if (!fileName) {
								console.warn(`文件名为空，跳过:`, file);
								continue;
							}

							console.log(`检查文件: ${fileName}`);

							if (fileName.toLowerCase().endsWith('.csv') || fileName.toLowerCase().endsWith('.json')) {
								const filePath = file.getAbsolutePath();
								console.log(`找到有效文件: ${fileName}, 路径: ${filePath}`);
								validFiles.push({
									name: fileName,
									path: filePath
								});
							}
						} catch (e) {
							console.error(`处理文件索引 ${i} 时出错:`, e);
						}
					}

					console.log(`找到 ${validFiles.length} 个有效的CSV/JSON文件`);

					if (validFiles.length === 0) {
						uni.showModal({
							title: '提示',
							content: 'Download文件夹中没有CSV或JSON文件',
							showCancel: false
						});
						return;
					}

					// 隐藏加载提示
					hideLoading();

					// 显示文件选择对话框
					uni.showActionSheet({
						itemList: validFiles.map(file => file.name),
						success: (res) => {
							const selectedFile = validFiles[res.tapIndex];
							this.selectedFile = selectedFile.name;

							// 读取文件内容
							this.readFileFromPath(selectedFile.path);
						}
					});
				} catch (e) {
					// 隐藏加载提示
					hideLoading();

					console.error('选择文件失败:', e);
					uni.showToast({
						title: '选择文件失败: ' + (e.message || e),
						icon: 'none'
					});
				}
			},

			// 从指定路径读取文件内容
			readFileFromPath(filePath) {
				this.isLoading = true;
				this.importError = '';
				console.log('开始读取文件:', filePath);

				try {
					// 使用原生 Java 方法读取文件
					const File = plus.android.importClass("java.io.File");
					const FileInputStream = plus.android.importClass("java.io.FileInputStream");
					const BufferedReader = plus.android.importClass("java.io.BufferedReader");
					const InputStreamReader = plus.android.importClass("java.io.InputStreamReader");
					const StringBuilder = plus.android.importClass("java.lang.StringBuilder");

					// 检查文件是否存在
					const file = new File(filePath);
					if (!file.exists()) {
						throw new Error('文件不存在: ' + filePath);
					}

					// 检查文件是否可读
					if (!file.canRead()) {
						throw new Error('文件不可读: ' + filePath);
					}

					// 检查文件大小
					const fileSize = file.length();
					console.log('文件大小:', fileSize, '字节');

					// 如果文件太大，可能会导致内存问题
					if (fileSize > 10 * 1024 * 1024) { // 10MB
						throw new Error('文件太大，超过10MB');
					}

					// 读取文件内容 - 根据文件类型选择编码
					const fileName = file.getName().toLowerCase();
					let encoding = "GBK"; // 默认使用GBK编码

					// 如果是JSON文件，优先使用UTF-8编码
					if (fileName.endsWith('.json')) {
						encoding = "UTF-8";
						console.log('检测到JSON文件，使用UTF-8编码');
					} else {
						console.log('使用GBK编码');
					}

					console.log('开始读取文件内容，使用', encoding, '编码');
					const fis = new FileInputStream(file);
					const isr = new InputStreamReader(fis, encoding);
					const br = new BufferedReader(isr);
					const sb = new StringBuilder();

					let line = null;
					let lineCount = 0;
					while ((line = br.readLine()) !== null) {
						sb.append(line).append("\n");
						lineCount++;
					}

					// 关闭流
					try {
						br.close();
						isr.close();
						fis.close();
					} catch (closeError) {
						console.error('关闭流失败:', closeError);
					}

					let content = sb.toString();
					console.log('使用', encoding, '编码成功读取了', lineCount, '行数据');

					// 如果是JSON文件，检查是否需要修复编码问题
					if (fileName.endsWith('.json')) {
						// 检查是否包含中文
						const containsChinese = /[\u4e00-\u9fa5]/.test(content);
						console.log('JSON内容' + (containsChinese ? '包含' : '不包含') + '中文字符');

						// 如果不包含中文，可能是编码问题，尝试使用GBK重新读取
						if (!containsChinese) {
							console.log('JSON内容不包含中文，尝试使用GBK编码重新读取');

							try {
								const fis2 = new FileInputStream(file);
								const isr2 = new InputStreamReader(fis2, "GBK");
								const br2 = new BufferedReader(isr2);
								const sb2 = new StringBuilder();

								let line2 = null;
								let lineCount2 = 0;
								while ((line2 = br2.readLine()) !== null) {
									sb2.append(line2).append("\n");
									lineCount2++;
								}

								// 关闭流
								try {
									br2.close();
									isr2.close();
									fis2.close();
								} catch (closeError) {
									console.error('关闭流失败:', closeError);
								}

								const content2 = sb2.toString();

								// 检查GBK编码的内容是否包含中文
								const containsChinese2 = /[\u4e00-\u9fa5]/.test(content2);
								console.log('GBK编码的JSON内容' + (containsChinese2 ? '包含' : '不包含') + '中文字符');

								// 如果GBK编码的内容包含中文，使用GBK编码的内容
								if (containsChinese2) {
									content = content2;
									console.log('使用GBK编码的JSON内容');
								}
							} catch (e) {
								console.error('使用GBK编码重新读取失败:', e);
							}
						}
					}

					// 设置文件内容并解析
					this.fileContent = content;
					console.log('文件内容读取完成');
					this.parseImportData();
				} catch (e) {
					console.error('读取文件失败:', e);
					this.importError = '读取文件失败: ' + (e.message || e);
					this.isLoading = false;

					// 显示错误提示
					uni.showModal({
						title: '读取文件失败',
						content: e.message || String(e),
						showCancel: false
					});
				}
			},

			// 读取文件内容
			readFile(filePath) {
				this.isLoading = true;
				this.importError = '';
				console.log('开始读取文件:', filePath);

				// 根据文件扩展名选择编码
				const isJsonFile = filePath.toLowerCase().endsWith('.json');
				const initialEncoding = isJsonFile ? 'utf8' : 'gbk';

				console.log('文件类型:', isJsonFile ? 'JSON' : 'CSV', '，初始编码:', initialEncoding);

				// 使用选定的编码读取
				uni.getFileSystemManager().readFile({
					filePath: filePath,
					encoding: initialEncoding,
					success: (res) => {
						let content = res.data;
						console.log(`使用 ${initialEncoding} 编码成功读取文件`);

						// 如果是JSON文件，检查是否需要修复编码问题
						if (isJsonFile) {
							// 检查是否包含中文
							const containsChinese = /[\u4e00-\u9fa5]/.test(content);
							console.log('JSON内容' + (containsChinese ? '包含' : '不包含') + '中文字符');

							// 如果不包含中文，可能是编码问题，尝试使用另一种编码重新读取
							if (!containsChinese) {
								const alternativeEncoding = initialEncoding === 'utf8' ? 'gbk' : 'utf8';
								console.log(`JSON内容不包含中文，尝试使用 ${alternativeEncoding} 编码重新读取`);

								uni.getFileSystemManager().readFile({
									filePath: filePath,
									encoding: alternativeEncoding,
									success: (res2) => {
										const content2 = res2.data;

										// 检查另一种编码的内容是否包含中文
										const containsChinese2 = /[\u4e00-\u9fa5]/.test(content2);
										console.log(`${alternativeEncoding} 编码的JSON内容` + (containsChinese2 ? '包含' : '不包含') + '中文字符');

										// 如果另一种编码的内容包含中文，使用另一种编码的内容
										if (containsChinese2) {
											content = content2;
											console.log(`使用 ${alternativeEncoding} 编码的JSON内容`);
										}

										this.fileContent = content;
										this.parseImportData();
									},
									fail: (err2) => {
										console.error(`使用 ${alternativeEncoding} 编码读取失败:`, err2);
										// 仍然使用原始内容
										this.fileContent = content;
										this.parseImportData();
									}
								});
							} else {
								// 包含中文，直接使用
								this.fileContent = content;
								this.parseImportData();
							}
						} else {
							// 非JSON文件，直接使用
							this.fileContent = content;
							this.parseImportData();
						}
					},
					fail: (err) => {
						console.error(`使用 ${initialEncoding} 编码读取失败，尝试另一种编码:`, err);

						// 如果初始编码失败，尝试另一种编码
						const alternativeEncoding = initialEncoding === 'utf8' ? 'gbk' : 'utf8';

						uni.getFileSystemManager().readFile({
							filePath: filePath,
							encoding: alternativeEncoding,
							success: (res) => {
								this.fileContent = res.data;
								console.log(`使用 ${alternativeEncoding} 编码成功读取文件`);
								this.parseImportData();
							},
							fail: (err2) => {
								console.error(`使用 ${alternativeEncoding} 编码也读取失败:`, err2);
								this.importError = '读取文件失败: ' + (err2.errMsg || err2);
								this.isLoading = false;

								// 显示错误提示
								uni.showModal({
									title: '读取文件失败',
									content: '无法读取文件，请确保文件格式正确',
									showCancel: false
								});
							}
						});
					}
				});
			},

			// 解析导入数据
			parseImportData() {
				this.isLoading = true;
				this.importError = '';
				this.previewData = [];
				this.previewColumns = [];

				try {
					const content = this.selectedMethod === 'file' ? this.fileContent : this.importText;

					if (!content) {
						this.isLoading = false;
						return;
					}

					// 自动检测文件格式
					if (this.selectedMethod === 'file' && this.selectedFile) {
						const fileName = this.selectedFile.toLowerCase();
						if (fileName.endsWith('.csv')) {
							this.selectedFormat = 'csv';
						} else if (fileName.endsWith('.json')) {
							this.selectedFormat = 'json';
						}
					}

					// 根据选择的格式解析数据
					switch (this.selectedFormat) {
						case 'csv':
							this.parseCSV(content);
							break;
						case 'json':
							this.parseJSON(content);
							break;
						default:
							// 尝试自动检测格式
							if (content.trim().startsWith('{') || content.trim().startsWith('[')) {
								this.selectedFormat = 'json';
								this.parseJSON(content);
							} else {
								this.selectedFormat = 'csv';
								this.parseCSV(content);
							}
							break;
					}
				} catch (e) {
					console.error('解析数据失败', e);
					this.importError = '解析数据失败: ' + (e.message || e);
				} finally {
					this.isLoading = false;
				}
			},

			// 解析CSV格式
			parseCSV(content) {
				console.log('开始解析CSV数据');

				// 检查BOM标记并移除
				if (content.charCodeAt(0) === 0xFEFF) {
					console.log('检测到BOM标记，移除BOM');
					content = content.slice(1);
				}

				// 分割行 - 支持所有常见的行分隔符
				const lines = content.split(/\r\n|\r|\n/).filter(line => line.trim());
				console.log(`CSV文件包含 ${lines.length} 行数据`);

				if (lines.length === 0) {
					this.importError = 'CSV文件为空';
					return;
				}

				// 检查是否包含中文
				const containsChinese = /[\u4e00-\u9fa5]/.test(content);
				console.log('CSV内容' + (containsChinese ? '包含' : '不包含') + '中文字符');

				// 解析表头
				const headers = this.parseCSVLine(lines[0]);
				console.log('解析到的表头:', headers);

				// 获取表的列信息
				const columnTypes = {};
				for (const column of this.columns) {
					columnTypes[column.name] = column.type;
				}
				console.log('表列类型信息:', columnTypes);

				// 自动生成的字段（id, createTime, updateTime）
				const autoGeneratedFields = ['id', 'createTime', 'updateTime', 'rowid'];
				console.log('自动生成字段:', autoGeneratedFields);

				// 过滤掉自动生成的字段
				const filteredHeaders = headers.filter(header => !autoGeneratedFields.includes(header));
				console.log('过滤后的表头:', filteredHeaders);

				this.previewColumns = filteredHeaders;

				// 解析数据行
				const data = [];
				for (let i = 1; i < lines.length; i++) {
					if (!lines[i].trim()) continue;

					const values = this.parseCSVLine(lines[i]);
					const row = {};

					for (let j = 0; j < headers.length; j++) {
						const header = headers[j];

						// 跳过自动生成的字段
						if (autoGeneratedFields.includes(header)) {
							continue;
						}

						let value = j < values.length ? values[j] : '';

						// 根据列类型转换值
						if (columnTypes[header]) {
							const type = columnTypes[header].toLowerCase();

							// 数字类型
							if (type.includes('int') || type.includes('float') || type.includes('double') || type.includes('decimal') || type.includes('number')) {
								// 如果是空字符串，设为null
								if (value === '') {
									value = null;
								}
								// 否则转换为数字
								else {
									const num = Number(value);
									if (!isNaN(num)) {
										value = num;
									}
								}
							}
							// 布尔类型
							else if (type.includes('bool')) {
								if (value.toLowerCase() === 'true' || value === '1') {
									value = 1;
								} else if (value.toLowerCase() === 'false' || value === '0') {
									value = 0;
								} else if (value === '') {
									value = null;
								}
							}
							// 日期类型
							else if (type.includes('date') || type.includes('time')) {
								if (value === '') {
									value = null;
								}
							}
						}

						row[header] = value;
					}

					// 不再自动添加category字段，因为articles表没有这个列

					data.push(row);

					// 记录前几行数据用于调试
					if (i <= 3) {
						console.log(`第 ${i} 行数据:`, JSON.stringify(row));
					}
				}

				console.log(`成功解析 ${data.length} 行数据`);
				this.previewData = data;
			},

			// 解析CSV行
			parseCSVLine(line) {
				// 简单记录行的开头部分
				console.log('解析CSV行:', line.substring(0, 30) + (line.length > 30 ? '...' : ''));

				// 使用更简单但更可靠的方法解析CSV行
				// 这种方法可能不能处理所有复杂的CSV格式，但对于大多数情况足够了
				const result = [];
				let inQuotes = false;
				let currentValue = '';

				for (let i = 0; i < line.length; i++) {
					const char = line[i];

					if (char === '"') {
						// 如果是引号开始或结束
						inQuotes = !inQuotes;
					} else if (char === ',' && !inQuotes) {
						// 如果是逗号且不在引号内，则添加当前值并重置
						result.push(currentValue);
						currentValue = '';
					} else {
						// 其他字符直接添加
						currentValue += char;
					}
				}

				// 添加最后一个值
				result.push(currentValue);

				// 清理值（去除引号和空格）
				for (let i = 0; i < result.length; i++) {
					let value = result[i];

					// 去除前后引号
					if (value.startsWith('"') && value.endsWith('"')) {
						value = value.substring(1, value.length - 1);
					}

					// 去除前后空格
					value = value.trim();

					result[i] = value;
				}

				return result;
			},

			// 解析JSON格式
			parseJSON(content) {
				try {
					console.log('开始解析JSON数据');

					// 检查BOM标记并移除
					if (content.charCodeAt(0) === 0xFEFF) {
						console.log('检测到BOM标记，移除BOM');
						content = content.slice(1);
					}

					// 检查是否包含中文
					const containsChinese = /[\u4e00-\u9fa5]/.test(content);
					console.log('JSON内容' + (containsChinese ? '包含' : '不包含') + '中文字符');

					// 尝试解析JSON
					let data;
					try {
						data = JSON.parse(content);
						console.log('JSON解析成功');
					} catch (parseError) {
						console.error('标准JSON解析失败，尝试修复:', parseError);

						// 尝试修复JSON字符串中的编码问题
						// 1. 尝试使用不同的编码重新解码内容
						try {
							// 创建一个TextEncoder和TextDecoder
							const TextEncoder = plus.android.importClass("java.nio.charset.Charset");
							const charset = TextEncoder.forName("UTF-8");

							// 将字符串转换为字节数组
							const bytes = [];
							for (let i = 0; i < content.length; i++) {
								bytes.push(content.charCodeAt(i) & 0xFF);
							}

							// 将字节数组转换为ByteBuffer
							const ByteBuffer = plus.android.importClass("java.nio.ByteBuffer");
							const buffer = ByteBuffer.wrap(bytes);

							// 使用UTF-8解码
							const decodedContent = charset.decode(buffer).toString();
							console.log('尝试使用UTF-8重新解码内容');

							// 尝试解析重新解码的内容
							data = JSON.parse(decodedContent);
							console.log('使用重新解码的内容解析JSON成功');
						} catch (decodeError) {
							console.error('重新解码失败，尝试其他方法:', decodeError);

							// 2. 尝试手动修复常见的JSON编码问题
							try {
								// 替换常见的编码问题
								let fixedContent = content
									.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
										return String.fromCharCode(parseInt(hex, 16));
									})
									.replace(/\\"/g, '"')
									.replace(/\\\\/g, '\\');

								// 尝试解析修复后的内容
								data = JSON.parse(fixedContent);
								console.log('使用手动修复的内容解析JSON成功');
							} catch (fixError) {
								console.error('手动修复失败，抛出原始错误:', fixError);
								throw parseError; // 抛出原始错误
							}
						}
					}

					// 检查是否是数组
					if (!Array.isArray(data)) {
						// 如果是对象，尝试将其转换为数组
						if (typeof data === 'object' && data !== null) {
							console.log('JSON数据是对象，尝试转换为数组');
							data = [data];
						} else {
							this.importError = 'JSON数据必须是数组格式';
							return;
						}
					}

					if (data.length === 0) {
						this.importError = 'JSON数据为空';
						return;
					}

					console.log(`JSON数据包含 ${data.length} 条记录`);

					// 获取表的列信息
					const columnTypes = {};
					for (const column of this.columns) {
						columnTypes[column.name] = column.type;
					}
					console.log('表列类型信息:', columnTypes);

					// 自动生成的字段（id, createTime, updateTime）
					const autoGeneratedFields = ['id', 'createTime', 'updateTime', 'rowid'];
					console.log('自动生成字段:', autoGeneratedFields);

					// 获取所有可能的列
					const allColumns = new Set();
					for (const row of data) {
						Object.keys(row).forEach(key => allColumns.add(key));
					}
					console.log('所有可能的列:', Array.from(allColumns));

					// 过滤掉自动生成的字段
					const filteredColumns = Array.from(allColumns).filter(col => !autoGeneratedFields.includes(col));
					console.log('过滤后的列:', filteredColumns);

					// 转换数据类型并处理中文
					for (let i = 0; i < data.length; i++) {
						const row = data[i];
						const filteredRow = {};

						// 处理每一列
						for (const key of Object.keys(row)) {
							// 跳过自动生成的字段
							if (autoGeneratedFields.includes(key)) {
								continue;
							}

							let value = row[key];

							// 处理中文编码问题
							if (typeof value === 'string') {
								// 检查是否包含可能的编码问题
								if (/\\u[0-9a-fA-F]{4}/.test(value)) {
									// 尝试解码Unicode转义序列
									value = value.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
										return String.fromCharCode(parseInt(hex, 16));
									});
									console.log(`修复字段 ${key} 的Unicode编码:`, value);
								}

								// 检查是否包含乱码
								if (this.containsGarbledText(value)) {
									console.log(`字段 ${key} 可能包含乱码:`, value);

									// 尝试修复乱码
									try {
										// 创建一个TextEncoder和TextDecoder
										const TextEncoder = plus.android.importClass("java.nio.charset.Charset");
										const charset = TextEncoder.forName("UTF-8");

										// 将字符串转换为字节数组
										const bytes = [];
										for (let i = 0; i < value.length; i++) {
											bytes.push(value.charCodeAt(i) & 0xFF);
										}

										// 将字节数组转换为ByteBuffer
										const ByteBuffer = plus.android.importClass("java.nio.ByteBuffer");
										const buffer = ByteBuffer.wrap(bytes);

										// 使用UTF-8解码
										const decodedValue = charset.decode(buffer).toString();

										// 如果解码后的值不同，使用解码后的值
										if (decodedValue !== value) {
											value = decodedValue;
											console.log(`修复字段 ${key} 的乱码:`, value);
										}
									} catch (decodeError) {
										console.error(`修复字段 ${key} 的乱码失败:`, decodeError);
									}
								}
							}

							// 根据列类型转换值
							if (columnTypes[key]) {
								const type = columnTypes[key].toLowerCase();

								// 数字类型
								if (type.includes('int') || type.includes('float') || type.includes('double') || type.includes('decimal') || type.includes('number')) {
									// 如果是空字符串或null，设为null
									if (value === '' || value === null) {
										value = null;
									}
									// 如果是字符串，尝试转换为数字
									else if (typeof value === 'string') {
										const num = Number(value);
										if (!isNaN(num)) {
											value = num;
										}
									}
								}
								// 布尔类型
								else if (type.includes('bool')) {
									if (typeof value === 'string') {
										if (value.toLowerCase() === 'true' || value === '1') {
											value = 1;
										} else if (value.toLowerCase() === 'false' || value === '0') {
											value = 0;
										} else if (value === '') {
											value = null;
										}
									} else if (typeof value === 'boolean') {
										value = value ? 1 : 0;
									}
								}
								// 日期类型
								else if (type.includes('date') || type.includes('time')) {
									if (value === '' || value === null) {
										value = null;
									}
								}
							}

							// 更新值
							filteredRow[key] = value;
						}

						// 不再自动添加category字段，因为articles表没有这个列

						// 替换原始行
						data[i] = filteredRow;

						// 记录前几行数据用于调试
						if (i < 3) {
							console.log(`第 ${i+1} 条JSON数据:`, JSON.stringify(filteredRow));
						}
					}

					this.previewColumns = filteredColumns;
					this.previewData = data;
					console.log(`成功解析 ${data.length} 条JSON数据`);
				} catch (e) {
					console.error('JSON解析错误:', e);
					this.importError = 'JSON格式错误: ' + e.message;
				}
			},



			// 根据当前格式获取占位符文本
			getPlaceholderByFormat() {
				switch (this.selectedFormat) {
					case 'csv':
						return '请粘贴CSV格式数据，第一行应为列名...';
					case 'json':
						return '请粘贴JSON格式数据，应为对象数组...';
					default:
						return '请粘贴CSV或JSON格式的数据...';
				}
			},

			// 获取格式示例
			getFormatExample() {
				// 使用列名生成示例
				const columnNames = this.columns.map(col => col.name);

				switch (this.selectedFormat) {
					case 'csv':
						return columnNames.join(',') + '\n' +
							'示例值1,' + '示例值2,'.repeat(columnNames.length - 2) + '示例值3\n' +
							'示例值4,' + '示例值5,'.repeat(columnNames.length - 2) + '示例值6';

					case 'json':
						const obj1 = {};
						const obj2 = {};

						columnNames.forEach((col, index) => {
							obj1[col] = `示例值${index + 1}`;
							obj2[col] = `示例值${index + columnNames.length + 1}`;
						});

						return JSON.stringify([obj1, obj2], null, 2);

					default:
						// 默认返回CSV格式
						return columnNames.join(',') + '\n' +
							'示例值1,' + '示例值2,'.repeat(columnNames.length - 2) + '示例值3\n' +
							'示例值4,' + '示例值5,'.repeat(columnNames.length - 2) + '示例值6';
				}
			},

			// 使用示例数据
			useExample() {
				this.importText = this.getFormatExample();
			},

			// 检测文本是否包含乱码
			containsGarbledText(text) {
				if (!text) return false;

				// 检查是否包含常见的乱码字符
				const garbledPatterns = [
					// 常见乱码字符组合
					/ï¿½/g,  // UTF-8字符在ISO-8859-1中的表示
					/â€œ/g,  // 引号在错误编码下的表示
					/â€/g,   // 引号在错误编码下的表示
					/Ã¢â‚¬â„¢/g, // 常见乱码组合
					/Â/g,    // 常见乱码字符

					// 检查中文是否正常
					// 如果文本中有中文字符，但没有常见的中文标点，可能是乱码
					/[\u4e00-\u9fa5]{3,}[，。？！；：""''（）【】《》]/
				];

				// 检查是否匹配乱码模式
				for (const pattern of garbledPatterns.slice(0, 5)) {
					if (pattern.test(text)) {
						return true; // 包含乱码字符
					}
				}

				// 检查中文是否正常
				const hasChinese = /[\u4e00-\u9fa5]{3,}/.test(text); // 至少3个连续中文字符
				const hasChinesePunctuation = /[，。？！；：""''（）【】《》]/.test(text); // 中文标点

				// 如果有较多中文但没有中文标点，可能是乱码
				if (hasChinese && !hasChinesePunctuation && text.length > 20) {
					// 计算中文字符比例
					let chineseCount = 0;
					for (let i = 0; i < text.length; i++) {
						if (/[\u4e00-\u9fa5]/.test(text[i])) {
							chineseCount++;
						}
					}

					// 如果中文字符比例过低，可能是乱码
					const chineseRatio = chineseCount / text.length;
					if (chineseRatio < 0.3) {
						return true;
					}
				}

				return false;
			},

			// 导入数据
			async importData() {
				if (this.previewData.length === 0) {
					uni.showToast({
						title: '没有数据可导入',
						icon: 'none'
					});
					return;
				}

				// 确认导入
				uni.showModal({
					title: '确认导入',
					content: `确定要导入 ${this.previewData.length} 条数据到表 ${this.tableName} 吗？`,
					success: async (res) => {
						if (res.confirm) {
							await this.performImport();
						}
					}
				});
			},

			// 执行导入
			async performImport() {
				this.isImporting = true;

				try {
					// 创建进度指示器
					const totalRecords = this.previewData.length;
					let successCount = 0;
					let failCount = 0;
					let lastError = null;

					// 显示进度对话框
					showLoading(`导入中... 0/${totalRecords}`);

					console.log('开始导入数据，共', totalRecords, '条记录');

					// 确保所有必填字段都包含在数据中
					for (const row of this.previewData) {
						// 对于articles表，确保必填字段有值
						if (this.tableName === 'articles') {
							// 确保fileName字段有值
							if (!row.fileName) {
								row.fileName = '待完善';
								console.log('为必填字段 fileName 添加默认值: 待完善');
							}

							// 确保articleType字段有值
							if (!row.articleType) {
								row.articleType = '待完善';
								console.log('为必填字段 articleType 添加默认值: 待完善');
							}

							// 确保articleContent字段有值
							if (!row.articleContent) {
								row.articleContent = '待完善';
								console.log('为必填字段 articleContent 添加默认值: 待完善');
							}

							// 确保keywords字段有值
							if (!row.keywords) {
								row.keywords = '待完善';
								console.log('为必填字段 keywords 添加默认值: 待完善');
							}
						}
						// 对于documents表，确保必填字段有值
						else if (this.tableName === 'documents') {
							// 确保fileName字段有值
							if (!row.fileName) {
								row.fileName = '待完善';
								console.log('为必填字段 fileName 添加默认值: 待完善');
							}

							// 确保category字段有值
							if (!row.category) {
								row.category = '待完善';
								console.log('为必填字段 category 添加默认值: 待完善');
							}

							// 确保documentNumber字段有值
							if (!row.documentNumber) {
								row.documentNumber = '待完善';
								console.log('为必填字段 documentNumber 添加默认值: 待完善');
							}
						}
					}

					// 用于跟踪待完善的数据和自动创建的文件名
					const incompleteArticles = [];
					const incompleteDocuments = [];
					const autoCreatedDocuments = [];

					// 使用模拟手动添加数据的方式导入数据
					for (let i = 0; i < this.previewData.length; i++) {
						const row = this.previewData[i];
						try {
							// 准备数据对象，跳过自动生成的字段
							const data = {};
							for (const key in row) {
								// 跳过id, createTime, updateTime字段
								if (['id', 'createTime', 'updateTime', 'rowid'].includes(key)) {
									continue;
								}
								data[key] = row[key];
							}

							console.log(`准备插入第 ${i+1} 行数据:`, JSON.stringify(data));

							// 检查是否有待完善的字段
							let hasIncompleteFields = false;
							if (this.tableName === 'articles') {
								if (data.fileName === '待完善' || data.articleType === '待完善' ||
									data.articleContent === '待完善' || data.keywords === '待完善') {
									hasIncompleteFields = true;
									incompleteArticles.push(data.fileName || `记录${i+1}`);
								}
							} else if (this.tableName === 'documents') {
								if (data.fileName === '待完善' || data.category === '待完善' ||
									data.documentNumber === '待完善') {
									hasIncompleteFields = true;
									incompleteDocuments.push(data.fileName || `记录${i+1}`);
								}
							}

							// 如果是条文表，检查文件名是否存在于文档表中
							if (this.tableName === 'articles' && data.fileName) {
								const fileNameExists = await checkFileNameExists(data.fileName);
								if (!fileNameExists) {
									// 记录自动创建的文档
									autoCreatedDocuments.push(data.fileName);
								}
							}

							// 使用insertData函数插入数据
							const result = await insertData(this.tableName, data);

							if (result) {
								console.log(`第 ${i+1} 行数据插入成功，结果:`, result);
								successCount++;

								// 更新进度指示器
								const progress = successCount + failCount;
								showLoading(`导入中... ${progress}/${totalRecords}`);
							} else {
								console.error(`第 ${i+1} 行数据插入失败`);
								failCount++;

								// 更新进度指示器
								const progress = successCount + failCount;
								showLoading(`导入中... ${progress}/${totalRecords}`);
							}
						} catch (e) {
							console.error(`插入第 ${i+1} 行数据失败:`, e);
							console.error(`完整错误信息:`, JSON.stringify(e));
							lastError = e;
							failCount++;

							// 更新进度指示器
							const progress = successCount + failCount;
							showLoading(`导入中... ${progress}/${totalRecords}`);
						}
					}

					// 隐藏加载提示
					hideLoading();

					// 去重
					const uniqueIncompleteArticles = [...new Set(incompleteArticles)];
					const uniqueIncompleteDocuments = [...new Set(incompleteDocuments)];
					const uniqueAutoCreatedDocuments = [...new Set(autoCreatedDocuments)];

					// 构建详细的导入结果信息
					let resultContent = `成功导入 ${successCount} 条数据，失败 ${failCount} 条\n\n`;

					if (this.tableName === 'articles') {
						resultContent += `待完善条文: ${uniqueIncompleteArticles.length} 条\n`;

						if (uniqueAutoCreatedDocuments.length > 0) {
							resultContent += `自动创建的文件记录: ${uniqueAutoCreatedDocuments.length} 条\n\n`;

							// 如果自动创建的文件太多，只显示前5个
							if (uniqueAutoCreatedDocuments.length <= 5) {
								resultContent += `自动创建的文件名:\n${uniqueAutoCreatedDocuments.join('\n')}\n`;
							} else {
								resultContent += `自动创建的文件名(前5个):\n${uniqueAutoCreatedDocuments.slice(0, 5).join('\n')}...\n`;
							}
						}
					} else if (this.tableName === 'documents') {
						resultContent += `待完善文件信息: ${uniqueIncompleteDocuments.length} 条\n`;
					}

					// 显示结果
					uni.showModal({
						title: '导入完成',
						content: resultContent,
						confirmText: '复制结果',
						cancelText: '确定',
						success: (res) => {
							if (res.confirm) {
								// 复制结果到剪贴板
								uni.setClipboardData({
									data: resultContent,
									success: () => {
										uni.showToast({
											title: '已复制到剪贴板',
											icon: 'success'
										});
										// 返回上一页
										setTimeout(() => {
											uni.navigateBack();
										}, 1000);
									}
								});
							} else {
								// 返回上一页
								uni.navigateBack();
							}
						}
					});
				} catch (e) {
					// 隐藏加载提示
					hideLoading();
					console.error('导入数据失败', e);
					console.error('完整错误信息:', JSON.stringify(e));

					// 分析错误
					let errorMessage = e.message || String(e);
					let friendlyMessage = '导入数据失败';

					// 如果是列不存在错误
					if (errorMessage.includes('has no column')) {
						const match = errorMessage.match(/has no column named (\w+)/);
						if (match && match.length >= 2) {
							const columnName = match[1];

							// 如果是category列不存在
							if (columnName === 'category') {
								friendlyMessage = `导入失败: 条文表(articles)没有 "category" 列，请移除此列或使用正确的表`;
							} else {
								friendlyMessage = `导入失败: 表中不存在列 "${columnName}"，请检查导入数据的格式`;
							}
						} else {
							friendlyMessage = '导入失败: 表中不存在某些列，请检查导入数据的格式';
						}
					}
					// 如果是NOT NULL约束错误
					else if (errorMessage.includes('NOT NULL constraint')) {
						// 尝试找出是哪个字段的问题
						const match = errorMessage.match(/NOT NULL constraint failed: (\w+)\.(\w+)/);
						if (match && match.length >= 3) {
							const tableName = match[1];
							const columnName = match[2];

							// 如果是articleType列
							if (columnName === 'articleType') {
								friendlyMessage = `导入失败: 字段 "articleType" 不能为空，请确保导入的数据中包含此字段，值应为预定义的选项之一`;
							} else {
								friendlyMessage = `导入失败: 字段 "${columnName}" 不能为空，请确保导入的数据中包含此字段`;
							}
						} else {
							friendlyMessage = '导入失败: 某些必填字段缺少值，请确保导入的数据包含所有必填字段';
						}
					}
					// 如果是外键约束错误
					else if (errorMessage.includes('FOREIGN KEY constraint')) {
						friendlyMessage = '导入失败: 外键约束错误，请确保引用的数据存在';
					}
					// 如果是唯一约束错误
					else if (errorMessage.includes('UNIQUE constraint')) {
						friendlyMessage = '导入失败: 唯一约束错误，可能是尝试插入重复的数据';
					}
					// 如果是CHECK约束错误
					else if (errorMessage.includes('CHECK constraint')) {
						friendlyMessage = '导入失败: 检查约束错误，请确保数据符合表的约束条件';
					}
					// 其他错误
					else {
						friendlyMessage = '导入失败: ' + errorMessage;
					}

					// 显示更详细的错误信息
					uni.showModal({
						title: '导入失败',
						content: friendlyMessage,
						showCancel: false
					});
				} finally {
					this.isImporting = false;
				}
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 20rpx 30rpx;
		padding-top: var(--status-bar-height);
	}

	.header-left, .header-right {
		width: 120rpx;
	}

	.header-back {
		color: #FFFFFF;
		font-size: 28rpx;
	}

	.header-title {
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: bold;
		max-width: 400rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.import-container {
		flex: 1;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
	}

	.import-options {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}

	.option-group {
		margin-bottom: 20rpx;
	}

	.option-label {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
	}

	.format-options, .import-methods {
		display: flex;
		flex-wrap: wrap;
	}

	.format-option, .method-option {
		padding: 10rpx 20rpx;
		border: 1rpx solid #DDDDDD;
		border-radius: 8rpx;
		margin-right: 20rpx;
		margin-bottom: 10rpx;
	}

	.format-option-selected, .method-option-selected {
		background-color: #007AFF;
		border-color: #007AFF;
	}

	.format-text, .method-text {
		font-size: 28rpx;
	}

	.format-option-selected .format-text,
	.method-option-selected .method-text {
		color: #FFFFFF;
	}

	.file-select {
		margin-top: 20rpx;
	}

	.file-button {
		background-color: #F0F0F0;
		color: #333333;
		font-size: 28rpx;
		padding: 10rpx 20rpx;
		border-radius: 8rpx;
		margin-bottom: 10rpx;
	}

	.file-name {
		font-size: 24rpx;
		color: #666666;
	}

	.text-input-container {
		margin-top: 20rpx;
	}

	.text-input {
		width: 100%;
		min-height: 200rpx;
		border: 1rpx solid #DDDDDD;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		background-color: #FFFFFF;
	}

	.format-help {
		margin-top: 20rpx;
		background-color: #F8F8F8;
		border-radius: 8rpx;
		padding: 20rpx;
	}

	.format-help-title {
		font-size: 26rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
	}

	.format-help-text {
		font-size: 24rpx;
		color: #666666;
		display: block;
		white-space: pre-wrap;
		word-break: break-all;
		margin-bottom: 10rpx;
	}

	.format-help-action {
		font-size: 24rpx;
		color: #007AFF;
		display: block;
		text-align: right;
	}

	.format-help-note {
		font-size: 24rpx;
		color: #FF6600;
		display: block;
		margin-top: 10rpx;
		padding: 10rpx;
		background-color: #FFF8F0;
		border-left: 4rpx solid #FF6600;
		border-radius: 4rpx;
	}

	.preview-section {
		flex: 1;
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		display: flex;
		flex-direction: column;
	}

	.preview-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.preview-title {
		font-size: 28rpx;
		font-weight: bold;
	}

	.preview-count {
		font-size: 24rpx;
		color: #666666;
	}

	.loading-tip, .empty-tip {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #999999;
		font-size: 28rpx;
	}

	.preview-table {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.preview-header-scroll {
		background-color: #F8F8F8;
	}

	.preview-row {
		display: flex;
		border-bottom: 1rpx solid #EEEEEE;
	}

	.preview-row-even {
		background-color: #F8F8F8;
	}

	.preview-header-row {
		background-color: #F0F0F0;
		border-bottom: 1rpx solid #DDDDDD;
	}

	.preview-cell {
		min-width: 200rpx;
		padding: 10rpx;
		word-break: break-all;
	}

	.preview-header-cell {
		font-weight: bold;
	}

	.preview-header-text {
		font-size: 24rpx;
	}

	.preview-cell-text {
		font-size: 24rpx;
	}

	.preview-body {
		flex: 1;
	}

	.preview-more {
		padding: 10rpx;
		text-align: center;
	}

	.preview-more-text {
		font-size: 24rpx;
		color: #999999;
	}

	.import-actions {
		margin-top: 20rpx;
	}

	.import-button {
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 32rpx;
		padding: 20rpx;
		border-radius: 8rpx;
	}

	.import-button[disabled] {
		background-color: #CCCCCC;
		color: #FFFFFF;
		opacity: 0.7;
	}
</style>
