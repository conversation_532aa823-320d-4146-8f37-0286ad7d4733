<template>
	<view class="content">
		<AppHeader
			:title="tableName"
			:subtitle="getTableDisplayName(tableName)"
		/>

		<view class="form-container">
			<view v-if="columns.length === 0" class="empty-tip">
				<text>加载中...</text>
			</view>

			<view v-else>
				<!-- 数据输入表单 -->
				<template v-for="(column, index) in columns" :key="column ? column.id : index">
					<view
						v-if="column && !isHidden(column)"
						class="form-item"
					>
						<text class="form-label">{{ column.name }} ({{ column.type }})</text>

						<!-- 项目选择器（子项目表专用） -->
						<view
							v-if="tableName === 'subprojects' && column.name === 'projectName'"
							class="input-container"
						>
							<picker
								:value="projectSelectionIndex"
								:range="projectOptions"
								@change="(e) => onProjectSelectionChange(column, e)"
							>
								<view class="picker-view">
									<text v-if="formData[column.name]">{{ formData[column.name] }}</text>
									<text v-else class="picker-placeholder">请选择上级项目</text>
								</view>
							</picker>
						</view>

						<!-- 外键选择器 -->
						<view
							v-else-if="column.is_foreign_key === 1 && foreignKeyData[column.id] && foreignKeyData[column.id].length > 0"
							class="input-container"
						>
							<picker
								:value="formData[column.name]?.index || 0"
								:range="foreignKeyData[column.id]"
								@change="(e) => onForeignKeyChange(column, e)"
							>
								<view class="picker-view">
									<text v-if="formData[column.name]?.value">{{ formData[column.name].value }}</text>
									<text v-else class="picker-placeholder">请选择{{ column.name }}</text>
								</view>
							</picker>
						</view>

						<!-- 类别下拉选择器 -->
						<view
							v-else-if="(column.name === 'category' || column.name === 'articleType') && getColumnOptions(column).length > 0"
							class="input-container"
						>
							<picker
								:value="getCategoryIndex(column)"
								:range="getColumnOptions(column)"
								@change="(e) => onCategoryChange(column, e)"
							>
								<view class="picker-view">
									<text v-if="formData[column.name]">{{ formData[column.name] }}</text>
									<text v-else class="picker-placeholder">请选择{{ column.name }}</text>
								</view>
							</picker>
						</view>

						<!-- 普通输入框 -->
						<view v-else class="input-container">
							<input
								class="form-input"
								:value="formData[column.name]"
								@input="formData[column.name] = $event.detail.value"
								:placeholder="getPlaceholder(column)"
								:disabled="isDisabled(column)"
								:type="getInputType(column)"
							/>
						</view>

						<text v-if="errors[column.name]" class="form-error">{{ errors[column.name] }}</text>
					</view>
				</template>

				<!-- 保存按钮 -->
				<button class="save-button" @click="saveData">保存数据</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getTableColumns,
		getForeignKeyData,
		insertData,
		queryTableData
	} from '@/utils/sqlite.js';
	import { TableUtils } from '@/config/tables.js';
	import { FormUtils } from '@/utils/form.js';
	import AppHeader from '@/components/AppHeader.vue';

	export default {
		components: {
			AppHeader
		},
		data() {
			return {
				tableId: 0,
				tableName: '',
				columns: [],
				formData: {},
				foreignKeyData: {},
				errors: {},
				// 项目选择相关（子项目表专用）
				projects: [],
				projectOptions: [],
				projectSelectionIndex: 0,
				selectedProject: null
			}
		},
		onLoad(options) {
			this.tableId = parseInt(options.tableId) || 0;
			this.tableName = options.tableName || '';

			// 加载表格列信息
			this.loadColumns();
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 获取表的中文显示名称
			getTableDisplayName(tableName) {
				return TableUtils.getTableDisplayName(tableName);
			},

			// 加载列信息
			async loadColumns() {
				try {
					// 获取列信息
					const columns = await getTableColumns(this.tableId);

					if (!columns || columns.length === 0) {
						console.error('未找到列信息');
						uni.showToast({
							title: '未找到列信息，请确保表已正确创建',
							icon: 'none',
							duration: 3000
						});

						// 延迟返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 3000);
						return;
					}

					// 过滤掉无效的列
					const validColumns = columns.filter(column => column && column.name);

					if (validColumns.length === 0) {
						console.error('所有列都无效');
						uni.showToast({
							title: '表结构异常，请重新创建表',
							icon: 'none',
							duration: 3000
						});

						// 延迟返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 3000);
						return;
					}

					console.log('有效列数量:', validColumns.length);

					// 检查是否有带选项的列
					const columnsWithOptions = validColumns.filter(col => col.options);
					if (columnsWithOptions.length > 0) {
						console.log('带选项的列:', columnsWithOptions.map(col => col.name));
						columnsWithOptions.forEach(col => {
							console.log(`${col.name} 的选项:`, col.options);
						});
					}

					// 按order_index排序
					this.columns = validColumns.sort((a, b) => a.order_index - b.order_index);

					// 初始化表单数据
					this.initFormData();

					// 加载外键数据
					await this.loadForeignKeyData();

				// 如果是子项目表，加载项目数据
				if (this.tableName === 'subprojects') {
					await this.loadProjectData();
				}
				} catch (e) {
					console.error('加载列信息失败', e);
					uni.showToast({
						title: '加载列信息失败: ' + (e.message || e),
						icon: 'none',
						duration: 3000
					});

					// 延迟返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 3000);
				}
			},

			// 初始化表单数据
			initFormData() {
				this.formData = FormUtils.initFormData(this.columns);
				console.log('表单数据初始化完成:', Object.keys(this.formData));
			},

			// 加载外键数据
			async loadForeignKeyData() {
				const foreignKeyData = {};

				for (const column of this.columns) {
					// 跳过无效列
					if (!column || !column.name) {
						continue;
					}

					if (column.is_foreign_key === 1 && column.reference_table_id && column.reference_column_id) {
						try {
							const data = await getForeignKeyData(column.reference_table_id, column.reference_column_id);
							if (data && data.length > 0) {
								foreignKeyData[column.id] = data;
								console.log(`已加载 ${column.name} 的外键数据:`, data.length);
							} else {
								console.warn(`未找到外键数据: ${column.name}`);
								// 为空的外键数据添加一个默认选项
								foreignKeyData[column.id] = ['(无数据)'];
							}
						} catch (e) {
							console.error(`加载 ${column.name} 的外键数据失败:`, e);
							// 为错误的外键数据添加一个默认选项
							foreignKeyData[column.id] = ['(加载失败)'];

							// 显示错误提示，但不中断流程
							uni.showToast({
								title: `加载 ${column.name} 的外键数据失败`,
								icon: 'none',
								duration: 2000
							});
						}
					}
				}

				this.foreignKeyData = foreignKeyData;
				console.log('外键数据加载完成:', Object.keys(foreignKeyData).length);
			},

			// 外键选择变更
			onForeignKeyChange(column, e) {
				const index = e.detail.value;
				const data = this.foreignKeyData[column.id];

				if (data && index >= 0 && index < data.length) {
					this.formData[column.name] = {
						index: index,
						value: data[index]
					};
				}
			},

			// 加载项目数据（子项目表专用）
			async loadProjectData() {
				try {
					const projects = await queryTableData('projects');
					this.projects = projects || [];
					this.projectOptions = this.projects.map(p => p.projectName || '未命名项目');

					if (this.projects.length === 0) {
						uni.showToast({
							title: '请先创建项目',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('加载项目数据失败', e);
					uni.showToast({
						title: '加载项目数据失败',
						icon: 'none'
					});
				}
			},

			// 项目选择变更（子项目表专用）
			onProjectSelectionChange(column, e) {
				const index = e.detail.value;
				this.projectSelectionIndex = index;
				this.selectedProject = this.projects[index];

				if (this.selectedProject) {
					// 设置项目名称
					this.formData[column.name] = this.selectedProject.projectName;

					// 设置项目ID（隐藏字段）
					this.formData.projectId = this.selectedProject.id;

					// 自动填入关联信息
					if (this.selectedProject.constructionLocation) {
						this.formData.constructionLocation = this.selectedProject.constructionLocation;
					}
					if (this.selectedProject.legalPerson) {
						this.formData.constructionUnit = this.selectedProject.legalPerson;
					}
					if (this.selectedProject.constructionScale) {
						this.formData.projectDescription = this.selectedProject.constructionScale;
					}
				}
			},

			// 获取输入框占位符
			getPlaceholder(column) {
				return FormUtils.getPlaceholder(column);
			},

			// 判断输入框是否禁用
			isDisabled(column) {
				return FormUtils.isDisabled(column);
			},

			// 判断字段是否隐藏
			isHidden(column) {
				return TableUtils.isHiddenColumn(column);
			},

			// 获取列的选项
			getColumnOptions(column) {
				return FormUtils.getColumnOptions(column);
			},

			// 获取类别索引
			getCategoryIndex(column) {
				if (!column) return 0;
				const options = this.getColumnOptions(column);
				const value = this.formData[column.name];
				return options.indexOf(value) > -1 ? options.indexOf(value) : 0;
			},

			// 类别选择变更
			onCategoryChange(column, e) {
				if (!column) return;
				const index = e.detail.value;
				const options = this.getColumnOptions(column);

				if (options && index >= 0 && index < options.length) {
					this.formData[column.name] = options[index];
				}
			},

			// 获取输入框类型
			getInputType(column) {
				return FormUtils.getInputType(column);
			},

			// 验证表单数据
			validateForm() {
				const { isValid, errors } = FormUtils.validateForm(this.columns, this.formData, this.isHidden);
				this.errors = errors;
				console.log('表单验证结果:', isValid, errors);
				return isValid;
			},

			// 保存数据
			async saveData() {
				// 验证表单
				if (!this.validateForm()) {
					return;
				}

				try {
					// 准备数据
					const data = FormUtils.prepareSaveData(this.columns, this.formData, this.isHidden);

					// 特殊处理：如果是子项目表，确保projectId被包含
					if (this.tableName === 'subprojects' && this.formData.projectId) {
						data.projectId = this.formData.projectId;
					}

					console.log('准备保存数据:', data);

					if (Object.keys(data).length === 0) {
						uni.showToast({
							title: '没有有效数据可保存',
							icon: 'none'
						});
						return;
					}

					// 插入数据
					const result = await insertData(this.tableName, data);

					if (result) {
						// 如果返回的是数字，说明是行ID
						const rowIdMsg = typeof result === 'number' ? `，行ID: ${result}` : '';

						uni.showToast({
							title: `数据添加成功${rowIdMsg}`,
							icon: 'success',
							duration: 2000
						});

						// 返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 2000);
					} else {
						uni.showToast({
							title: '数据添加失败',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('保存数据失败', e);
					uni.showToast({
						title: '保存数据失败: ' + (e.message || e),
						icon: 'none',
						duration: 3000
					});
				}
			}
		}
	}
</script>

<style>
	@import '@/static/styles/form.css';

	/* 页面特定样式 */
	.input-container {
		border: 1px solid #DDDDDD;
	}

	.form-input, .picker-view {
		min-width: 500rpx;
		min-height: 80rpx;
		border: none;
	}
</style>
