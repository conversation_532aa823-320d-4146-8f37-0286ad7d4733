<template>
	<view class="content">
		<view class="header">
			<view class="header-left" @click="goBack">
				<text class="header-back">返回</text>
			</view>
			<view class="header-center">
				<text class="header-title">{{ tableName }}</text>
				<text class="header-subtitle">{{ getTableDisplayName(tableName) }}</text>
			</view>
			<view class="header-right"></view>
		</view>

		<view class="form-container">
			<view v-if="columns.length === 0" class="empty-tip">
				<text>加载中...</text>
			</view>

			<view v-else>
				<!-- 数据输入表单 -->
				<template v-for="(column, index) in columns" :key="column ? column.id : index">
					<view
						v-if="column && !isHidden(column)"
						class="form-item"
					>
						<text class="form-label">{{ column.name }} ({{ column.type }})</text>

						<!-- 项目选择器（子项目表专用） -->
						<view
							v-if="tableName === 'subprojects' && column.name === 'projectName'"
							class="input-container"
						>
							<picker
								:value="projectSelectionIndex"
								:range="projectOptions"
								@change="(e) => onProjectSelectionChange(column, e)"
							>
								<view class="picker-view">
									<text v-if="formData[column.name]">{{ formData[column.name] }}</text>
									<text v-else class="picker-placeholder">请选择上级项目</text>
								</view>
							</picker>
						</view>

						<!-- 外键选择器 -->
						<view
							v-else-if="column.is_foreign_key === 1 && foreignKeyData[column.id] && foreignKeyData[column.id].length > 0"
							class="input-container"
						>
							<picker
								:value="formData[column.name]?.index || 0"
								:range="foreignKeyData[column.id]"
								@change="(e) => onForeignKeyChange(column, e)"
							>
								<view class="picker-view">
									<text v-if="formData[column.name]?.value">{{ formData[column.name].value }}</text>
									<text v-else class="picker-placeholder">请选择{{ column.name }}</text>
								</view>
							</picker>
						</view>

						<!-- 类别下拉选择器 -->
						<view
							v-else-if="(column.name === 'category' || column.name === 'articleType') && getColumnOptions(column).length > 0"
							class="input-container"
						>
							<picker
								:value="getCategoryIndex(column)"
								:range="getColumnOptions(column)"
								@change="(e) => onCategoryChange(column, e)"
							>
								<view class="picker-view">
									<text v-if="formData[column.name]">{{ formData[column.name] }}</text>
									<text v-else class="picker-placeholder">请选择{{ column.name }}</text>
								</view>
							</picker>
						</view>

						<!-- 普通输入框 -->
						<view v-else class="input-container">
							<input
								class="form-input"
								:value="formData[column.name]"
								@input="formData[column.name] = $event.detail.value"
								:placeholder="getPlaceholder(column)"
								:disabled="isDisabled(column)"
								:type="getInputType(column)"
							/>
						</view>

						<text v-if="errors[column.name]" class="form-error">{{ errors[column.name] }}</text>
					</view>
				</template>

				<!-- 保存按钮 -->
				<button class="save-button" @click="saveData">保存数据</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getTableColumns,
		getForeignKeyData,
		insertData,
		queryTableData
	} from '@/utils/sqlite.js';

	export default {
		data() {
			return {
				tableId: 0,
				tableName: '',
				columns: [],
				formData: {},
				foreignKeyData: {},
				errors: {},
				// 项目选择相关（子项目表专用）
				projects: [],
				projectOptions: [],
				projectSelectionIndex: 0,
				selectedProject: null
			}
		},
		onLoad(options) {
			this.tableId = parseInt(options.tableId) || 0;
			this.tableName = options.tableName || '';

			// 加载表格列信息
			this.loadColumns();
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 获取表的中文显示名称
			getTableDisplayName(tableName) {
				const tableNameMap = {
					'articles': '条文表',
					'documents': '文档表',
					'projects': '项目表',
					'subprojects': '子项目表'
				};

				return tableNameMap[tableName] || tableName;
			},

			// 加载列信息
			async loadColumns() {
				try {
					// 获取列信息
					const columns = await getTableColumns(this.tableId);

					if (!columns || columns.length === 0) {
						console.error('未找到列信息');
						uni.showToast({
							title: '未找到列信息，请确保表已正确创建',
							icon: 'none',
							duration: 3000
						});

						// 延迟返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 3000);
						return;
					}

					// 过滤掉无效的列
					const validColumns = columns.filter(column => column && column.name);

					if (validColumns.length === 0) {
						console.error('所有列都无效');
						uni.showToast({
							title: '表结构异常，请重新创建表',
							icon: 'none',
							duration: 3000
						});

						// 延迟返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 3000);
						return;
					}

					console.log('有效列数量:', validColumns.length);

					// 检查是否有带选项的列
					const columnsWithOptions = validColumns.filter(col => col.options);
					if (columnsWithOptions.length > 0) {
						console.log('带选项的列:', columnsWithOptions.map(col => col.name));
						columnsWithOptions.forEach(col => {
							console.log(`${col.name} 的选项:`, col.options);
						});
					}

					// 按order_index排序
					this.columns = validColumns.sort((a, b) => a.order_index - b.order_index);

					// 初始化表单数据
					this.initFormData();

					// 加载外键数据
					await this.loadForeignKeyData();

				// 如果是子项目表，加载项目数据
				if (this.tableName === 'subprojects') {
					await this.loadProjectData();
				}
				} catch (e) {
					console.error('加载列信息失败', e);
					uni.showToast({
						title: '加载列信息失败: ' + (e.message || e),
						icon: 'none',
						duration: 3000
					});

					// 延迟返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 3000);
				}
			},

			// 初始化表单数据
			initFormData() {
				const formData = {};

				for (const column of this.columns) {
					// 跳过无效列或自增主键
					if (!column || !column.name || (column.is_primary_key === 1 && column.type === 'INTEGER')) {
						continue;
					}

					formData[column.name] = '';
				}

				this.formData = formData;
				console.log('表单数据初始化完成:', Object.keys(formData));
			},

			// 加载外键数据
			async loadForeignKeyData() {
				const foreignKeyData = {};

				for (const column of this.columns) {
					// 跳过无效列
					if (!column || !column.name) {
						continue;
					}

					if (column.is_foreign_key === 1 && column.reference_table_id && column.reference_column_id) {
						try {
							const data = await getForeignKeyData(column.reference_table_id, column.reference_column_id);
							if (data && data.length > 0) {
								foreignKeyData[column.id] = data;
								console.log(`已加载 ${column.name} 的外键数据:`, data.length);
							} else {
								console.warn(`未找到外键数据: ${column.name}`);
								// 为空的外键数据添加一个默认选项
								foreignKeyData[column.id] = ['(无数据)'];
							}
						} catch (e) {
							console.error(`加载 ${column.name} 的外键数据失败:`, e);
							// 为错误的外键数据添加一个默认选项
							foreignKeyData[column.id] = ['(加载失败)'];

							// 显示错误提示，但不中断流程
							uni.showToast({
								title: `加载 ${column.name} 的外键数据失败`,
								icon: 'none',
								duration: 2000
							});
						}
					}
				}

				this.foreignKeyData = foreignKeyData;
				console.log('外键数据加载完成:', Object.keys(foreignKeyData).length);
			},

			// 外键选择变更
			onForeignKeyChange(column, e) {
				const index = e.detail.value;
				const data = this.foreignKeyData[column.id];

				if (data && index >= 0 && index < data.length) {
					this.formData[column.name] = {
						index: index,
						value: data[index]
					};
				}
			},

			// 加载项目数据（子项目表专用）
			async loadProjectData() {
				try {
					const projects = await queryTableData('projects');
					this.projects = projects || [];
					this.projectOptions = this.projects.map(p => p.projectName || '未命名项目');

					if (this.projects.length === 0) {
						uni.showToast({
							title: '请先创建项目',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('加载项目数据失败', e);
					uni.showToast({
						title: '加载项目数据失败',
						icon: 'none'
					});
				}
			},

			// 项目选择变更（子项目表专用）
			onProjectSelectionChange(column, e) {
				const index = e.detail.value;
				this.projectSelectionIndex = index;
				this.selectedProject = this.projects[index];

				if (this.selectedProject) {
					// 设置项目名称
					this.formData[column.name] = this.selectedProject.projectName;

					// 设置项目ID（隐藏字段）
					this.formData.projectId = this.selectedProject.id;

					// 自动填入关联信息
					if (this.selectedProject.constructionLocation) {
						this.formData.constructionLocation = this.selectedProject.constructionLocation;
					}
					if (this.selectedProject.legalPerson) {
						this.formData.constructionUnit = this.selectedProject.legalPerson;
					}
					if (this.selectedProject.constructionScale) {
						this.formData.projectDescription = this.selectedProject.constructionScale;
					}
				}
			},

			// 获取输入框占位符
			getPlaceholder(column) {
				if (!column) return '请输入';
				if (column.is_primary_key === 1 && column.type === 'INTEGER') {
					return '自动生成';
				}
				return `请输入${column.name}`;
			},

			// 判断输入框是否禁用
			isDisabled(column) {
				if (!column) return false;
				return column.is_primary_key === 1 && column.type === 'INTEGER';
			},

			// 判断字段是否隐藏
			isHidden(column) {
				if (!column) return true; // 如果列不存在，则隐藏

				// 检查是否有is_hidden标记
				if (column.is_hidden === 1) return true;

				// 检查是否是ID、创建时间或更新时间字段
				const hiddenFields = ['id', 'createTime', 'updateTime'];

				// 打印调试信息
				console.log(`检查列 ${column.name} 是否隐藏:`,
					hiddenFields.includes(column.name) ? '是' : '否',
					'is_hidden =', column.is_hidden);

				return hiddenFields.includes(column.name);
			},

			// 获取列的选项
			getColumnOptions(column) {
				try {
					if (column && column.options) {
						// 尝试解析选项
						let options;
						if (typeof column.options === 'string') {
							options = JSON.parse(column.options);
						} else if (Array.isArray(column.options)) {
							// 如果已经是数组，直接使用
							options = column.options;
						}

						// 确保返回的是数组
						return Array.isArray(options) ? options : [];
					}
				} catch (e) {
					console.error('解析列选项失败', e, column);
				}
				return [];
			},

			// 获取类别索引
			getCategoryIndex(column) {
				if (!column) return 0;
				const options = this.getColumnOptions(column);
				const value = this.formData[column.name];
				return options.indexOf(value) > -1 ? options.indexOf(value) : 0;
			},

			// 类别选择变更
			onCategoryChange(column, e) {
				if (!column) return;
				const index = e.detail.value;
				const options = this.getColumnOptions(column);

				if (options && index >= 0 && index < options.length) {
					this.formData[column.name] = options[index];
				}
			},

			// 获取输入框类型
			getInputType(column) {
				if (!column) return 'text';
				switch (column.type) {
					case 'INTEGER': return 'number';
					case 'REAL': return 'digit';
					default: return 'text';
				}
			},

			// 验证表单数据
			validateForm() {
				const errors = {};
				let isValid = true;

				for (const column of this.columns) {
					// 跳过无效列或自增主键或隐藏字段
					if (!column || !column.name ||
						(column.is_primary_key === 1 && column.type === 'INTEGER') ||
						this.isHidden(column)) {
						continue;
					}

					const value = this.formData[column.name];

					// 检查必填字段
					if (column.is_not_null === 1) {
						if (!value || (typeof value === 'object' && !value.value)) {
							errors[column.name] = `${column.name} 不能为空`;
							isValid = false;
							continue;
						}
					}

					// 检查数据类型
					if (value) {
						const actualValue = typeof value === 'object' ? value.value : value;

						if (column.type === 'INTEGER' && !/^-?\d+$/.test(actualValue)) {
							errors[column.name] = `${column.name} 必须是整数`;
							isValid = false;
						} else if (column.type === 'REAL' && !/^-?\d+(\.\d+)?$/.test(actualValue)) {
							errors[column.name] = `${column.name} 必须是数字`;
							isValid = false;
						}
					}
				}

				this.errors = errors;
				console.log('表单验证结果:', isValid, errors);
				return isValid;
			},

			// 保存数据
			async saveData() {
				// 验证表单
				if (!this.validateForm()) {
					return;
				}

				try {
					// 准备数据
					const data = {};

					for (const column of this.columns) {
						// 跳过无效列或自增主键
						if (!column || !column.name ||
							(column.is_primary_key === 1 && column.type === 'INTEGER')) {
							continue;
						}

						// 对于隐藏字段，只跳过那些不在formData中的字段
						if (this.isHidden(column) && !(column.name in this.formData)) {
							continue;
						}

						const value = this.formData[column.name];

						// 处理外键值
						if (typeof value === 'object' && value.value) {
							data[column.name] = value.value;
						} else if (value || value === 0 || value === '') {
							// 确保空字符串和0也能被保存
							data[column.name] = value;
						} else if (column.is_not_null === 1) {
							// 如果是必填字段但没有值，设置为空字符串
							data[column.name] = '';
						}
					}

					// 特殊处理：如果是子项目表，确保projectId被包含
					if (this.tableName === 'subprojects' && this.formData.projectId) {
						data.projectId = this.formData.projectId;
					}

					console.log('准备保存数据:', data);

					if (Object.keys(data).length === 0) {
						uni.showToast({
							title: '没有有效数据可保存',
							icon: 'none'
						});
						return;
					}

					// 插入数据
					const result = await insertData(this.tableName, data);

					if (result) {
						// 如果返回的是数字，说明是行ID
						const rowIdMsg = typeof result === 'number' ? `，行ID: ${result}` : '';

						uni.showToast({
							title: `数据添加成功${rowIdMsg}`,
							icon: 'success',
							duration: 2000
						});

						// 返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 2000);
					} else {
						uni.showToast({
							title: '数据添加失败',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('保存数据失败', e);
					uni.showToast({
						title: '保存数据失败: ' + (e.message || e),
						icon: 'none',
						duration: 3000
					});
				}
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 30rpx 30rpx 40rpx 30rpx; /* 增加上下内边距 */
		padding-top: calc(var(--status-bar-height) + 20rpx); /* 增加顶部空间 */
		min-height: 120rpx; /* 设置最小高度 */
	}

	.header-left, .header-right {
		width: 120rpx;
		align-self: flex-start; /* 让左右按钮对齐到顶部 */
		margin-top: 10rpx;
	}

	.header-center {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
		max-width: 400rpx;
	}

	.header-back {
		color: #FFFFFF;
		font-size: 28rpx;
	}

	.header-title {
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: bold;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-bottom: 8rpx; /* 与中文标题的间距 */
	}

	.header-subtitle {
		color: #FFFFFF;
		font-size: 28rpx;
		font-weight: normal;
		opacity: 0.9; /* 稍微透明，区分主标题 */
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.form-container {
		flex: 1;
		padding: 30rpx;
	}

	.empty-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 300rpx;
		background-color: #FFFFFF;
		border-radius: 8rpx;
		color: #999999;
		font-size: 28rpx;
	}

	.form-item {
		margin-bottom: 30rpx;
	}

	.input-container {
		width: 100%;
		border: 1px solid #DDDDDD;
		border-radius: 8rpx;
		background-color: #FFFFFF;
		padding: 0;
		margin: 0;
	}

	.form-label {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
	}

	.form-input, .picker-view {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		width: 100%;
		min-width: 500rpx;
		min-height: 80rpx;
		box-sizing: border-box;
		margin: 0;
		border: none;
	}

	.form-input:disabled {
		background-color: #F0F0F0;
		color: #999999;
	}

	.picker-placeholder {
		color: #999999;
	}

	.form-error {
		color: #FF0000;
		font-size: 24rpx;
		margin-top: 10rpx;
	}

	.save-button {
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 32rpx;
		padding: 20rpx;
		border-radius: 8rpx;
		margin-top: 30rpx;
	}
</style>
