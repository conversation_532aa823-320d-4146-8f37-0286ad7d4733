1.你需要将md文件内容逐条或者逐项进行处理。
2.对每条内容你需要提取相关的内容，并转化成json格式，json格式附后。
3.第一个内容为这个md文件的文件名，比如“工程质量安全手册.md”，那第一个fileNama就是“工程质量安全手册”。
4.第二个内容articleType为“主体行为、设施设备、物料、方法措施、作业环境、应急管理、资料管理”中的一个，你需要仔细理解这一条内容讲的是哪方面的内容，是对安全管理单位的主体行为的约束（包括单位和个人，比如建设单位应该怎样或者哪个岗位人员应该怎样），还是对资料管理的要求或者规范，再或者是讲的应急管理，或者是安全管理的中其他因素（设施设备、物料、方法措施、作业环境）。
5.第三个内容articleContent为这条内容原文，不要进行任何修改，哪怕标点。
6.第四个内容keywords为这一条内容的关键词/标签，关键词可以为多个，要涵盖主语、标签、主要内容或者是摘要等。如果内容本身没有主语，你需要向上查找上级标题等方式获取到这条内容的主语，比如，一条内容写的是“与参建各方签订的合同中应当明确安全责任，加强履约管理。”这句话很明显没有看到主语，向上可以查看到这是将建设单位的职责，所以可以知道主语是“建设单位”。我设置关键词的目的是知道这条内容到底讲的什么？要能仅通过这条内容的关键词和原文内容知道这条内容讲的什么，因为放到数据库中的每条数据内容是独立的，是看不到上下文的，所以我需要将上下文内容的关键词或者标签添加到关键词中。
json格式为：
[
  {
    "fileName": "工程质量安全手册",
    "articleType": "主体行为",
    "articleContent": "与参建各方签订的合同中应当明确安全责任，并加强履约管理。",
    "keywords": "建设单位 合同约定 安全责任"
  },
  {
    "fileName": "工程质量安全手册",
    "articleType": "主体行为",
    "articleContent": "按规定将委托的监理单位、监理的内容及监理权限书面通知被监理的建筑施工企业。",
    "keywords": "建设单位 书面告知施工单位 监理单位内容"
  }
]

要求：站在安全管理专家、工程管理专家、语言学专家、资料管理专家等的角度将md内容转换json格式，以便作为安全管理数据库的条文内容。
