/**
 * 表格配置文件
 * 统一管理所有表格的配置信息
 */

// 表名映射（英文 -> 中文）
export const TABLE_DISPLAY_NAMES = {
  'articles': '条文表',
  'documents': '文档表',
  'projects': '项目表',
  'subprojects': '子项目表'
};

// 列名映射（英文 -> 中文）
export const COLUMN_DISPLAY_NAMES = {
  // 通用字段
  'id': 'ID',
  'createTime': '创建时间',
  'updateTime': '更新时间',
  
  // articles表
  'fileName': '文件名',
  'articleType': '条文类型',
  'articleContent': '条文内容',
  'keywords': '关键词',
  
  // documents表
  'category': '分类',
  'documentNumber': '文件编号',
  'publishingUnit': '发布单位',
  
  // projects表
  'projectName': '项目名称',
  'legalPerson': '企业法人',
  'constructionLocation': '建设地点',
  'constructionScale': '建设规模及内容',
  
  // subprojects表
  'subprojectName': '子项目名称',
  'constructionUnit': '建设单位',
  'agentUnit': '代建单位',
  'surveyUnit': '勘察单位',
  'designUnit': '设计单位',
  'supervisionUnit': '监理单位',
  'constructorUnit': '施工单位',
  'projectDescription': '项目描述'
};

// 表格列宽配置
export const COLUMN_STYLES = {
  // articles表
  articles: {
    'fileName': {
      class: 'column-filename',
      style: { 'min-width': '150rpx', 'max-width': '200rpx' }
    },
    'articleType': {
      class: 'column-articletype',
      style: { 'min-width': '120rpx', 'max-width': '150rpx' }
    },
    'articleContent': {
      class: 'column-articlecontent',
      style: { 'min-width': '400rpx', 'width': 'auto', 'flex': '1' }
    },
    'keywords': {
      class: 'column-keywords',
      style: { 'min-width': '150rpx', 'max-width': '200rpx' }
    }
  },
  
  // documents表
  documents: {
    'fileName': {
      class: 'column-filename',
      style: { 'min-width': '150rpx', 'max-width': '200rpx' }
    },
    'category': {
      class: 'column-category',
      style: { 'min-width': '150rpx', 'max-width': '200rpx' }
    },
    'documentNumber': {
      class: 'column-docnumber',
      style: { 'min-width': '200rpx', 'max-width': '250rpx' }
    },
    'publishingUnit': {
      class: 'column-publishunit',
      style: { 'min-width': '200rpx', 'max-width': '300rpx' }
    }
  },
  
  // projects表
  projects: {
    'projectName': {
      class: 'column-projectname',
      style: { 'min-width': '300rpx', 'width': 'auto', 'flex': '1' }
    },
    'legalPerson': {
      class: 'column-legalperson',
      style: { 'min-width': '200rpx', 'max-width': '300rpx' }
    },
    'constructionLocation': {
      class: 'column-constructionlocation',
      style: { 'min-width': '200rpx', 'max-width': '300rpx' }
    },
    'constructionScale': {
      class: 'column-constructionscale',
      style: { 'min-width': '400rpx', 'width': 'auto', 'flex': '2' }
    }
  },
  
  // subprojects表
  subprojects: {
    'projectName': {
      class: 'column-projectname',
      style: { 'min-width': '120rpx', 'max-width': '150rpx' }
    },
    'subprojectName': {
      class: 'column-subprojectname',
      style: { 'min-width': '120rpx', 'max-width': '150rpx' }
    },
    'constructionLocation': {
      class: 'column-constructionlocation',
      style: { 'min-width': '100rpx', 'max-width': '130rpx' }
    },
    'constructionUnit': {
      class: 'column-constructionunit',
      style: { 'min-width': '100rpx', 'max-width': '130rpx' }
    },
    'agentUnit': {
      class: 'column-agentunit',
      style: { 'min-width': '80rpx', 'max-width': '100rpx' }
    },
    'surveyUnit': {
      class: 'column-surveyunit',
      style: { 'min-width': '70rpx', 'max-width': '90rpx' }
    },
    'designUnit': {
      class: 'column-designunit',
      style: { 'min-width': '70rpx', 'max-width': '90rpx' }
    },
    'supervisionUnit': {
      class: 'column-supervisionunit',
      style: { 'min-width': '80rpx', 'max-width': '100rpx' }
    },
    'constructorUnit': {
      class: 'column-constructorunit',
      style: { 'min-width': '100rpx', 'max-width': '130rpx' }
    },
    'projectDescription': {
      class: 'column-projectdescription',
      style: { 'min-width': '120rpx', 'width': 'auto', 'flex': '1' }
    }
  }
};

// 隐藏字段配置
export const HIDDEN_FIELDS = ['id', 'createTime', 'updateTime'];

// 工具函数
export const TableUtils = {
  // 获取表的中文显示名称
  getTableDisplayName(tableName) {
    return TABLE_DISPLAY_NAMES[tableName] || tableName;
  },
  
  // 获取列的中文显示名称
  getColumnDisplayName(columnName) {
    return COLUMN_DISPLAY_NAMES[columnName] || columnName;
  },
  
  // 获取列的CSS类名
  getColumnClass(tableName, columnName) {
    const tableConfig = COLUMN_STYLES[tableName];
    if (tableConfig && tableConfig[columnName]) {
      return tableConfig[columnName].class;
    }
    return '';
  },
  
  // 获取列的样式
  getColumnStyle(tableName, columnName) {
    const tableConfig = COLUMN_STYLES[tableName];
    if (tableConfig && tableConfig[columnName]) {
      return tableConfig[columnName].style;
    }
    return {};
  },
  
  // 判断字段是否隐藏
  isHiddenField(columnName) {
    return HIDDEN_FIELDS.includes(columnName);
  },
  
  // 判断列是否隐藏
  isHiddenColumn(column) {
    if (!column) return false;
    return column.is_hidden === 1 || this.isHiddenField(column.name);
  }
};
