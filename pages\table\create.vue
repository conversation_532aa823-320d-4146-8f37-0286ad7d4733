<template>
	<view class="content">
		<view class="header">
			<view class="header-left" @click="goBack">
				<text class="header-back">返回</text>
			</view>
			<text class="header-title">创建数据表</text>
			<view class="header-right"></view>
		</view>

		<view class="form-container">
			<!-- 表名 -->
			<view class="form-item">
				<text class="form-label">表名</text>
				<view class="input-container">
					<input
						class="form-input"
						type="text"
						:value="tableName"
						@input="tableName = $event.detail.value"
						placeholder="请输入表名（英文字母和下划线）"
					/>
				</view>
				<text v-if="tableNameError" class="form-error">{{ tableNameError }}</text>
			</view>

			<!-- 表描述 -->
			<view class="form-item">
				<text class="form-label">表描述</text>
				<view class="input-container">
					<textarea
						class="form-textarea"
						:value="tableDescription"
						@input="tableDescription = $event.detail.value"
						placeholder="请输入表描述（可选）"
					/>
				</view>
			</view>

			<!-- 列定义 -->
			<view class="form-item">
				<view class="form-header">
					<text class="form-label">列定义</text>
					<view class="form-action" @click="addColumn">
						<text class="form-action-text">添加列</text>
					</view>
				</view>

				<view v-if="columns.length === 0" class="empty-tip">
					<text>请添加至少一列</text>
				</view>

				<view v-else class="column-list">
					<view
						v-for="(column, index) in columns"
						:key="index"
						class="column-item"
					>
						<view class="column-item-header">
							<text class="column-name">{{ column.name }}</text>
							<view class="column-actions">
								<view class="column-action" @click="deleteColumn(index)">
									<text class="column-action-text">删除</text>
								</view>
							</view>
						</view>

						<view class="column-details">
							<text class="column-type">{{ getColumnTypeText(column.type) }}</text>
							<view class="column-attributes">
								<text v-if="column.isPrimaryKey" class="column-attribute">主键</text>
								<text v-if="column.isNotNull" class="column-attribute">不允许为空</text>
								<text v-if="column.isUnique" class="column-attribute">唯一</text>
							</view>
							<text v-if="column.isForeignKey" class="column-foreign-key">
								外键关联: 表ID={{ column.referenceTableId }}, 列ID={{ column.referenceColumnId }}
							</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 保存按钮 -->
			<button class="save-button" @click="saveTable">保存表</button>
		</view>
	</view>
</template>

<script>
	import { createTable } from '@/utils/sqlite.js';

	export default {
		data() {
			return {
				tableName: '',
				tableDescription: '',
				columns: [],
				tableNameError: ''
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 添加列
			addColumn() {
				uni.navigateTo({
					url: '/pages/column/create',
					events: {
						// 监听列创建页面返回的数据
						columnCreated: (column) => {
							// 检查列名是否重复
							if (this.columns.some(c => c.name === column.name)) {
								uni.showToast({
									title: `列名 '${column.name}' 已存在`,
									icon: 'none'
								});
								return;
							}

							// 如果是主键，检查是否已有其他主键
							if (column.isPrimaryKey && this.columns.some(c => c.isPrimaryKey)) {
								uni.showToast({
									title: '一个表只能有一个主键',
									icon: 'none'
								});
								return;
							}

							// 添加列
							this.columns.push(column);
						}
					}
				});
			},

			// 删除列
			deleteColumn(index) {
				this.columns.splice(index, 1);
			},

			// 获取列类型文本
			getColumnTypeText(type) {
				switch (type) {
					case 'TEXT': return '文本 (TEXT)';
					case 'INTEGER': return '整数 (INTEGER)';
					case 'REAL': return '小数 (REAL)';
					case 'BLOB': return '二进制 (BLOB)';
					default: return type;
				}
			},

			// 验证表名
			validateTableName() {
				if (!this.tableName) {
					this.tableNameError = '表名不能为空';
					return false;
				}

				if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(this.tableName)) {
					this.tableNameError = '表名只能包含字母、数字和下划线，且必须以字母开头';
					return false;
				}

				this.tableNameError = '';
				return true;
			},

			// 保存表
			async saveTable() {
				// 验证表名
				if (!this.validateTableName()) {
					return;
				}

				// 验证列
				if (this.columns.length === 0) {
					uni.showToast({
						title: '请至少添加一列',
						icon: 'none'
					});
					return;
				}

				// 验证是否有主键
				if (!this.columns.some(column => column.isPrimaryKey)) {
					uni.showToast({
						title: '请至少设置一个主键列',
						icon: 'none'
					});
					return;
				}

				try {
					// 创建表
					const tableId = await createTable(
						this.tableName,
						this.tableDescription,
						this.columns
					);

					uni.showToast({
						title: '表创建成功',
						icon: 'success'
					});

					// 返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} catch (e) {
					console.error('创建表失败', e);
					uni.showToast({
						title: '创建表失败: ' + (e.message || e),
						icon: 'none'
					});
				}
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 20rpx 30rpx;
		padding-top: var(--status-bar-height);
	}

	.header-left, .header-right {
		width: 120rpx;
	}

	.header-back {
		color: #FFFFFF;
		font-size: 28rpx;
	}

	.header-title {
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: bold;
	}

	.form-container {
		flex: 1;
		padding: 30rpx;
	}

	.form-item {
		margin-bottom: 30rpx;
	}

	.input-container {
		width: 100%;
		border: 1px solid #DDDDDD;
		border-radius: 8rpx;
		background-color: #FFFFFF;
		padding: 0;
		margin: 0;
	}

	.form-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.form-label {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.form-action {
		background-color: #007AFF;
		padding: 10rpx 20rpx;
		border-radius: 6rpx;
	}

	.form-action-text {
		color: #FFFFFF;
		font-size: 24rpx;
	}

	.form-input, .form-textarea {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		width: 100%;
		min-width: 500rpx;
		min-height: 80rpx;
		box-sizing: border-box;
		margin: 0;
		border: none;
	}

	.form-textarea {
		height: 160rpx;
		min-height: 160rpx;
	}

	.form-error {
		color: #FF0000;
		font-size: 24rpx;
		margin-top: 10rpx;
	}

	.empty-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 200rpx;
		background-color: #FFFFFF;
		border-radius: 8rpx;
		color: #999999;
		font-size: 28rpx;
	}

	.column-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.column-item {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx;
	}

	.column-item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.column-name {
		font-size: 30rpx;
		font-weight: bold;
	}

	.column-actions {
		display: flex;
		gap: 10rpx;
	}

	.column-action {
		background-color: #FF3B30;
		padding: 6rpx 12rpx;
		border-radius: 4rpx;
	}

	.column-action-text {
		color: #FFFFFF;
		font-size: 22rpx;
	}

	.column-details {
		display: flex;
		flex-direction: column;
		gap: 6rpx;
	}

	.column-type {
		font-size: 26rpx;
		color: #333333;
	}

	.column-attributes {
		display: flex;
		flex-wrap: wrap;
		gap: 10rpx;
	}

	.column-attribute {
		background-color: #F0F0F0;
		padding: 4rpx 12rpx;
		border-radius: 4rpx;
		font-size: 22rpx;
		color: #666666;
	}

	.column-foreign-key {
		font-size: 22rpx;
		color: #666666;
		font-style: italic;
		margin-top: 6rpx;
	}

	.save-button {
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 32rpx;
		padding: 20rpx;
		border-radius: 8rpx;
		margin-top: 30rpx;
	}
</style>
