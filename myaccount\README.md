# MyBooking记账 App

一个基于 uni-app 开发的移动端记账应用，支持多用户、分类管理、支出记录等功能。

## 项目结构

```
myaccount/
├── pages/                # 页面文件
│   ├── index/           # 主页（记账页面）
│   ├── login/           # 登录页面
│   ├── register/        # 注册页面
│   ├── details/         # 明细页面
│   ├── settings/        # 设置页面
│   ├── change-password/ # 修改密码页面
│   ├── forgot-password/ # 忘记密码页面
│   ├── contact/         # 联系我们页面
│   ├── category-management/ # 分类管理页面
│   └── api-settings/    # API设置页面
├── services/            # 业务逻辑服务
│   └── localDataService.js  # 本地数据服务，处理用户、分类、支出等数据
├── utils/              # 工具类
│   ├── db.js          # 数据库操作工具
│   └── validator.js   # 表单验证工具
├── store/             # Vuex状态管理
│   └── index.js       # 状态管理配置
├── static/            # 静态资源
│   └── styles/        # 样式文件
│       └── common.scss # 公共样式
├── src/               # 源代码目录
│   └── androidPrivate/ # Android私有配置
├── App.vue            # 应用入口组件
├── main.js            # 应用入口文件
├── pages.json         # 页面配置
├── manifest.json      # 应用配置
└── package.json       # 项目依赖配置
```

## 技术栈

- 框架：uni-app
- 前端：Vue 3 + Vuex
- UI：自定义组件
- 数据存储：SQLite（App本地数据库）
- 构建工具：HBuilderX

## 已实现功能

### 1. 用户管理
- [x] 用户注册
- [x] 用户登录
- [x] 用户退出

### 2. 分类管理
- [x] 默认分类创建
- [x] 分类树形展示
- [x] 分类金额统计
- [x] 分类占比计算

### 3. 支出记录
- [x] 支出记录添加
- [x] 支出金额统计
- [x] 支出时间记录
- [x] 支出分类关联

### 4. 界面功能
- [x] 响应式布局
- [x] 表单验证
- [x] 错误提示
- [x] 加载状态
- [x] 用户友好提示

## 待实现功能

### 1. 数据管理
- [ ] 支出记录编辑
- [ ] 支出记录删除
- [x] 支出记录查询
- [ ] 数据导出功能
- [x] 数据备份恢复

### 2. 分类管理
- [x] 自定义分类添加
- [x] 分类编辑
- [x] 分类删除
- [x] 分类排序调整

### 3. 统计分析
- [ ] 月度支出统计
- [ ] 年度支出统计
- [ ] 支出趋势分析
- [ ] 分类支出占比图表
- [ ] 自定义时间段统计

### 4. 高级功能
- [ ] 预算管理
- [ ] 收入记录
- [ ] 多币种支持
- [ ] 账单导出
- [ ] 数据同步

## 存在的问题

1. 数据安全
   - 密码加密方式需要加强
   - 缺少数据备份机制
   - 本地数据库安全性有待提升

2. 性能优化
   - 大量数据加载可能存在性能问题
   - 分类树渲染可能需要虚拟列表优化
   - 数据库查询优化空间较大

3. 用户体验
   - 缺少数据修改功能
   - 缺少数据删除功能
   - 缺少数据筛选和搜索功能
   - 统计分析功能不足

4. 代码质量
   - 需要添加单元测试
   - 需要添加代码注释
   - 需要规范化错误处理
   - 组件复用性可以提升

## 优化方向

1. 技术架构
   - 引入 TypeScript 增强代码健壮性
   - 添加 ESLint 规范代码风格
   - 引入单元测试框架
   - 优化项目结构，增加组件复用

2. 功能扩展
   - 添加云同步功能
   - 支持多设备数据同步
   - 添加数据分析报表
   - 支持预算管理
   - 添加收入记录功能
   - 支持账单导出

3. 性能优化
   - 引入虚拟列表
   - 优化数据库查询
   - 添加数据缓存机制
   - 优化大数据量处理

4. 用户体验
   - 优化错误提示
   - 添加操作引导
   - 优化表单交互
   - 添加手势操作
   - 支持主题切换

## 数据库表结构

### users 表
```sql
CREATE TABLE users (
    user_id TEXT PRIMARY KEY,
    username TEXT UNIQUE,
    password TEXT,
    email TEXT,
    created_at TEXT
)
```

### categories 表
```sql
CREATE TABLE categories (
    category_id TEXT PRIMARY KEY,
    user_id TEXT,
    parent_id TEXT,
    name TEXT,
    level INTEGER,
    sort_order INTEGER,
    total_amount REAL DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
)
```

### expenses 表
```sql
CREATE TABLE expenses (
    expense_id TEXT PRIMARY KEY,
    user_id TEXT,
    category_id TEXT,
    amount REAL,
    target TEXT,
    record_date TEXT,
    created_at TEXT,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (category_id) REFERENCES categories(category_id)
)
```

## 打包说明

1. 环境要求
   - HBuilderX 3.0+
   - Node.js 12+
   - Android SDK（安卓打包）
   - iOS 开发证书（iOS打包）

2. 打包步骤
   ```
   1. 使用 HBuilderX 打开项目
   2. 检查 manifest.json 配置
   3. 更新版本号
   4. 选择发行 -> 原生App-云打包
   5. 选择证书配置
   6. 开始打包
   ```

3. 注意事项
   - 确保 manifest.json 配置正确
   - 检查应用图标和启动图配置
   - 检查权限配置
   - 测试各项功能正常

## 维护者

- [@sean440](https://github.com/sean440)

## 许可证

[MIT](LICENSE) © Sean440