/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0;
  box-sizing: border-box;
}
.header {
  background: #4a90e2;
  padding: 30px 16px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 30px;
}
.header .header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding-top: 15px;
}
.header .logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 12px;
}
.header .title {
  color: white;
  font-size: 20px;
  font-weight: 500;
}
.form-container {
  flex: 1;
  padding: 0 16px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}
.form-content {
  width: 100%;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}
.form-item {
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
}
.form-item .label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
}
.form-item .input-field {
  width: 100%;
  height: 40px;
  padding: 0 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}
.form-item .input-field:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}
.form-item .error-text {
  display: block;
  margin-top: 4px;
  color: #ff4d4f;
  font-size: 12px;
}
.submit-button {
  width: 100%;
  height: 40px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  margin-top: 24px;
  transition: background-color 0.3s ease;
}
.submit-button:hover {
  background: #357abd;
}
.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}
.back-link {
  text-align: center;
  margin-top: 16px;
  color: #4a90e2;
  font-size: 14px;
  cursor: pointer;
}
.back-link:hover {
  text-decoration: underline;
}