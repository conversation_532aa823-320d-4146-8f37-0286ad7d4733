
.app-header[data-v-a54da7ff] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #007AFF;
  padding: 0.78125rem 0.9375rem 0.9375rem 0.9375rem;
  padding-top: calc(var(--status-bar-height) + 0.46875rem);
  min-height: 3.125rem;
}
.header-left[data-v-a54da7ff], .header-right[data-v-a54da7ff] {
  width: 3.75rem;
  align-self: flex-start;
  margin-top: 0.3125rem;
}
.header-center[data-v-a54da7ff] {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 12.5rem;
}
.header-back[data-v-a54da7ff], .header-action[data-v-a54da7ff] {
  color: #FFFFFF;
  font-size: 0.875rem;
}
.header-title[data-v-a54da7ff] {
  color: #FFFFFF;
  font-size: 1.125rem;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0.25rem;
}
.header-subtitle[data-v-a54da7ff] {
  color: #FFFFFF;
  font-size: 0.875rem;
  font-weight: normal;
  opacity: 0.9;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/**
 * 表单通用样式
 * 统一管理所有表单的样式定义
 */

/* 基础容器样式 */
.content {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}
.form-container {
  flex: 1;
  padding: 0.9375rem;
}
.form-content {
  padding: 0.9375rem;
  padding-bottom: 9.375rem; /* 增加底部空间，避免输入法遮挡 */
}

/* 表单项样式 */
.form-item {
  margin-bottom: 1.25rem;
}
.form-label {
  display: block;
  font-size: 0.875rem;
  color: #333333;
  margin-bottom: 0.625rem;
  font-weight: 500;
}
.input-container {
  background-color: #FFFFFF;
  border-radius: 0.25rem;
  border: 0.0625rem solid #E5E5E5;
  overflow: hidden;
}
.form-input {
  width: 100%;
  padding: 0.75rem 0.625rem;
  font-size: 0.875rem;
  color: #333333;
  background-color: transparent;
  border: none;
  outline: none;
}
.form-input:disabled {
  background-color: #F0F0F0;
  color: #999999;
}
.form-textarea {
  width: 100%;
  padding: 0.75rem 0.625rem;
  font-size: 0.875rem;
  color: #333333;
  background-color: transparent;
  border: none;
  outline: none;
  min-height: 3.75rem;
  resize: none;
}

/* 选择器样式 */
.picker-view {
  padding: 0.75rem 0.625rem;
  font-size: 0.875rem;
  color: #333333;
  background-color: #FFFFFF;
}
.picker-placeholder {
  color: #999999;
}

/* 错误和提示样式 */
.form-error {
  display: block;
  color: #FF3B30;
  font-size: 0.75rem;
  margin-top: 0.3125rem;
}
.form-hint {
  display: block;
  color: #666666;
  font-size: 0.75rem;
  margin-top: 0.3125rem;
  font-style: italic;
}

/* 按钮样式 */
.button-group {
  display: flex;
  gap: 0.625rem;
  margin-top: 1.875rem;
  padding-bottom: 1.25rem;
}
.save-button {
  background-color: #007AFF;
  color: #FFFFFF;
  font-size: 1rem;
  padding: 0.625rem;
  border-radius: 0.25rem;
  margin-top: 0.9375rem;
  border: none;
}
.save-button:disabled {
  background-color: #CCCCCC;
  color: #999999;
}
.cancel-button {
  flex: 1;
  height: 2.75rem;
  background-color: #F2F2F7;
  color: #666666;
  border: none;
  border-radius: 0.25rem;
  font-size: 1rem;
  font-weight: 500;
}

/* 提示信息样式 */
.empty-tip {
  text-align: center;
  padding: 3.125rem 0;
  color: #999999;
  font-size: 0.875rem;
}
.loading-tip {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 9.375rem;
  background-color: #FFFFFF;
  border-radius: 0.25rem;
  color: #999999;
  font-size: 0.875rem;
}

/* 搜索和操作栏样式 */
.search-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.625rem;
  width: 100%;
  padding: 0 0.3125rem;
}
.search-input-container {
  position: relative;
  width: 100%;
}
.search-input {
  background-color: #FFFFFF;
  border-radius: 0.25rem;
  padding: 0.625rem 1.875rem 0.625rem 0.625rem;
  font-size: 0.9375rem;
  border: 0.03125rem solid #EEEEEE;
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
  height: 2.5rem;
}
.search-clear {
  position: absolute;
  right: 0.625rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.25rem;
  color: #999999;
  width: 1.5625rem;
  height: 1.5625rem;
  text-align: center;
  line-height: 1.5625rem;
  z-index: 10;
}

/* 操作按钮行样式 */
.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.625rem;
  flex-wrap: nowrap;
  padding: 0 0.3125rem;
  overflow-x: auto;
}
.action-button {
  background-color: #F0F0F0;
  border-radius: 0.25rem;
  padding: 0.46875rem 0.3125rem;
  margin: 0 0.25rem 0 0;
  min-width: 3.125rem;
  text-align: center;
  flex: 1;
  max-width: 4.375rem;
  white-space: nowrap;
}
.action-button:last-child {
  margin-right: 0;
}
.action-button-text {
  font-size: 0.75rem;
  color: #333333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 智能输入按钮样式 */
.smart-button {
  background-color: #007AFF;
}
.smart-button .action-button-text {
  color: #FFFFFF;
}

/* 筛选标签样式 */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0.625rem;
}
.filter-tag {
  background-color: #E0F0FF;
  border-radius: 0.25rem;
  padding: 0.1875rem 0.3125rem;
  margin-right: 0.3125rem;
  margin-bottom: 0.3125rem;
  display: flex;
  align-items: center;
}
.filter-tag-text {
  font-size: 0.75rem;
  color: #007AFF;
}
.filter-tag-remove {
  font-size: 0.875rem;
  color: #007AFF;
  margin-left: 0.3125rem;
  width: 0.9375rem;
  height: 0.9375rem;
  text-align: center;
  line-height: 0.9375rem;
}
.clear-all-filters {
  font-size: 0.75rem;
  color: #FF6B6B;
  margin-left: 0.3125rem;
  padding: 0.1875rem 0.3125rem;
}

/* 页面特定样式 */
.input-container {
		border: 1px solid #DDDDDD;
}
.form-input, .picker-view {
		min-width: 15.625rem;
		min-height: 2.5rem;
		border: none;
}
