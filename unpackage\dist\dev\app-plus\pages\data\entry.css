
.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 0.9375rem 0.9375rem 1.25rem 0.9375rem; /* 增加上下内边距 */
		padding-top: calc(var(--status-bar-height) + 0.625rem); /* 增加顶部空间 */
		min-height: 3.75rem; /* 设置最小高度 */
}
.header-left, .header-right {
		width: 3.75rem;
		align-self: flex-start; /* 让左右按钮对齐到顶部 */
		margin-top: 0.3125rem;
}
.header-center {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
		max-width: 12.5rem;
}
.header-back {
		color: #FFFFFF;
		font-size: 0.875rem;
}
.header-title {
		color: #FFFFFF;
		font-size: 1.125rem;
		font-weight: bold;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-bottom: 0.25rem; /* 与中文标题的间距 */
}
.header-subtitle {
		color: #FFFFFF;
		font-size: 0.875rem;
		font-weight: normal;
		opacity: 0.9; /* 稍微透明，区分主标题 */
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
}
.form-container {
		flex: 1;
		padding: 0.9375rem;
}
.empty-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 9.375rem;
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		color: #999999;
		font-size: 0.875rem;
}
.form-item {
		margin-bottom: 0.9375rem;
}
.input-container {
		width: 100%;
		border: 1px solid #DDDDDD;
		border-radius: 0.25rem;
		background-color: #FFFFFF;
		padding: 0;
		margin: 0;
}
.form-label {
		font-size: 0.875rem;
		font-weight: bold;
		margin-bottom: 0.3125rem;
		display: block;
}
.form-input, .picker-view {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem;
		font-size: 0.875rem;
		width: 100%;
		min-width: 15.625rem;
		min-height: 2.5rem;
		box-sizing: border-box;
		margin: 0;
		border: none;
}
.form-input:disabled {
		background-color: #F0F0F0;
		color: #999999;
}
.picker-placeholder {
		color: #999999;
}
.form-error {
		color: #FF0000;
		font-size: 0.75rem;
		margin-top: 0.3125rem;
}
.save-button {
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 1rem;
		padding: 0.625rem;
		border-radius: 0.25rem;
		margin-top: 0.9375rem;
}
