<template>
  <view class="app-header">
    <view class="header-left" @click="goBack">
      <text class="header-back">返回</text>
    </view>
    <view class="header-center">
      <text class="header-title">{{ title }}</text>
      <text v-if="subtitle" class="header-subtitle">{{ subtitle }}</text>
    </view>
    <view class="header-right" @click="onRightClick">
      <text v-if="rightText" class="header-action">{{ rightText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AppHeader',
  props: {
    // 主标题
    title: {
      type: String,
      default: ''
    },
    // 副标题
    subtitle: {
      type: String,
      default: ''
    },
    // 右侧按钮文字
    rightText: {
      type: String,
      default: ''
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      if (this.showBack) {
        uni.navigateBack();
      }
    },
    
    // 右侧按钮点击事件
    onRightClick() {
      if (this.rightText) {
        this.$emit('right-click');
      }
    }
  }
};
</script>

<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #007AFF;
  padding: 25rpx 30rpx 30rpx 30rpx;
  padding-top: calc(var(--status-bar-height) + 15rpx);
  min-height: 100rpx;
}

.header-left, .header-right {
  width: 120rpx;
  align-self: flex-start;
  margin-top: 10rpx;
}

.header-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 400rpx;
}

.header-back, .header-action {
  color: #FFFFFF;
  font-size: 28rpx;
}

.header-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 8rpx;
}

.header-subtitle {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: normal;
  opacity: 0.9;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
