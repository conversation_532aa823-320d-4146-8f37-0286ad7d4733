/* 公共样式文件 */

/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0;
  box-sizing: border-box;
}

/* 头部样式 */
.header {
  background: #4a90e2;
  padding: 30px 16px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 30px;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-top: 15px;
  }

  .logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    margin-right: 12px;
  }

  .title {
    color: white;
    font-size: 20px;
    font-weight: 500;
  }
  
  .header-buttons {
    position: absolute;
    right: 0;
    display: flex;
    
    .header-button {
      color: white;
      font-size: 14px;
      margin-left: 15px;
      padding: 5px 0;
    }
  }
}

/* 表单容器样式 */
.form-container {
  flex: 1;
  padding: 0 20px;
  
  .form-content {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }
}

/* 表单项样式 */
.form-item {
  margin-bottom: 20px;
  
  .label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
  }
  
  .input-field {
    width: 100%;
    height: 44px;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 0 15px;
    font-size: 14px;
    box-sizing: border-box;
  }
  
  .error-text {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 5px;
    display: block;
  }
}

/* 按钮样式 */
.submit-button {
  width: 100%;
  height: 44px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  margin-top: 10px;
  margin-bottom: 20px;
  
  &:active {
    opacity: 0.8;
  }
  
  &[disabled] {
    background: #cccccc;
    opacity: 0.6;
  }
}

/* 链接样式 */
.link-text {
  color: #4a90e2;
  font-size: 14px;
  text-align: center;
  margin-top: 15px;
  
  &:active {
    opacity: 0.8;
  }
}

/* 列表样式 */
.list-container {
  flex: 1;
  padding: 0 15px;
  
  .list-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    
    .item-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      
      .item-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
      
      .item-value {
        font-size: 16px;
        font-weight: 500;
        color: #ff4d4f;
      }
    }
    
    .item-content {
      font-size: 14px;
      color: #666;
      margin-bottom: 5px;
    }
    
    .item-footer {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #999;
    }
  }
}

/* 模态框样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  
  .modal-content {
    width: 80%;
    background: white;
    border-radius: 10px;
    padding: 20px;
    
    .modal-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 15px;
      text-align: center;
    }
    
    .modal-body {
      margin-bottom: 20px;
    }
    
    .modal-footer {
      display: flex;
      justify-content: space-between;
      
      .modal-button {
        flex: 1;
        height: 40px;
        border-radius: 5px;
        font-size: 14px;
        margin: 0 5px;
        
        &.cancel {
          background: #f5f5f5;
          color: #666;
        }
        
        &.confirm {
          background: #4a90e2;
          color: white;
        }
      }
    }
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  
  .loading-text {
    font-size: 14px;
    color: #666;
    margin-left: 10px;
  }
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  
  .empty-icon {
    width: 80px;
    height: 80px;
    margin-bottom: 15px;
  }
  
  .empty-text {
    font-size: 14px;
    color: #999;
  }
}
