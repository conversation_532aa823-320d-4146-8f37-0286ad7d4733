<template>
	<view class="content">
		<view class="header">
			<view class="header-left" @click="goBack">
				<text class="header-back">返回</text>
			</view>
			<view class="header-center">
				<text class="header-title">{{ tableName }}</text>
				<text class="header-subtitle">{{ getTableDisplayName(tableName) }}</text>
			</view>
			<view class="header-right" @click="showActions">
				<text class="header-action">操作</text>
			</view>
		</view>

		<view class="tabs">
			<view
				class="tab-item"
				:class="{ 'tab-item-active': activeTab === 'columns' }"
				@click="activeTab = 'columns'"
			>
				<text class="tab-text">列定义</text>
			</view>
			<view
				class="tab-item"
				:class="{ 'tab-item-active': activeTab === 'data' }"
				@click="activeTab = 'data'"
			>
				<text class="tab-text">数据内容</text>
			</view>
		</view>

		<!-- 列定义 -->
		<view v-if="activeTab === 'columns'" class="tab-content">
			<view class="columns-tip" v-if="activeTab === 'columns' && columns.length > 0">
				<text class="columns-tip-text">提示: 长按列可以调整列的顺序</text>
			</view>

			<view v-if="isLoading" class="loading-tip">
				<text>加载中...</text>
			</view>

			<view v-else-if="columns.length === 0" class="empty-tip">
				<text>没有列定义</text>
			</view>

			<view v-else class="column-list">
				<view
					v-for="(column, index) in columns"
					:key="column.id"
					class="column-item"
					@longpress="showColumnActions(column, index)"
				>
					<view class="column-item-header">
						<text class="column-name">{{ getColumnDisplayName(column.name) }}</text>
						<text class="column-order">顺序: {{ column.order_index + 1 }}</text>
					</view>

					<view class="column-details">
						<text class="column-type">{{ getColumnTypeText(column.type) }}</text>
						<view class="column-attributes">
							<text v-if="column.is_primary_key === 1" class="column-attribute">主键</text>
							<text v-if="column.is_not_null === 1" class="column-attribute">不允许为空</text>
							<text v-if="column.is_unique === 1" class="column-attribute">唯一</text>
						</view>
						<text v-if="column.is_foreign_key === 1" class="column-foreign-key">
							外键关联: 表ID={{ column.reference_table_id }}, 列ID={{ column.reference_column_id }}
						</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 数据内容 -->
		<view v-if="activeTab === 'data'" class="tab-content">
			<view class="data-tip">
				<text class="data-tip-text">提示: 长按数据行(约{{ longPressThreshold/1000 }}秒)可编辑或删除该行数据</text>
			</view>

			<!-- 搜索栏 - 单独一行 -->
			<view class="search-bar">
				<view class="search-input-container">
					<input
						class="search-input"
						type="text"
						v-model="searchText"
						placeholder="搜索数据..."
						@input="onSearchInput"
					/>
					<text v-if="searchText" class="search-clear" @click="clearSearch">×</text>
				</view>
			</view>

			<!-- 操作按钮行 - 所有按钮在一行 -->
			<view class="action-bar">
				<view class="action-button" @click="showFilterOptions">
					<text class="action-button-text">筛选</text>
				</view>
				<view class="action-button" @click="showBatchOptions">
					<text class="action-button-text">批量</text>
				</view>
				<view class="action-button" @click="showImportOptions">
					<text class="action-button-text">导入</text>
				</view>
				<view class="action-button" @click="showExportOptions">
					<text class="action-button-text">导出</text>
				</view>
				<view v-if="tableName === 'articles'" class="action-button smart-button" @click="goToSmartImport">
					<text class="action-button-text">智能输入</text>
				</view>
			</view>

			<!-- 筛选条件显示 -->
			<view v-if="activeFilters.length > 0" class="active-filters">
				<view
					v-for="(filter, index) in activeFilters"
					:key="index"
					class="filter-tag"
				>
					<text class="filter-tag-text">{{ filter.column }}: {{ filter.operator }} {{ filter.value }}</text>
					<text class="filter-tag-remove" @click="removeFilter(index)">×</text>
				</view>
				<text class="clear-all-filters" @click="clearAllFilters">清除全部</text>
			</view>

			<view v-if="isLoading" class="loading-tip">
				<text>加载中...</text>
			</view>

			<view v-else-if="filteredData.length === 0" class="empty-tip">
				<text>{{ searchText || activeFilters.length > 0 ? '没有匹配的数据' : '暂无数据' }}</text>

				<!-- 数据行数显示 -->
				<view class="data-count-info-empty">
					<text class="data-count-text">{{
						searchText || activeFilters.length > 0
						? `筛选结果: 0/${tableData.length} 条数据`
						: `共 0 条数据`
					}}</text>
				</view>
			</view>

			<view v-else class="data-table">
				<!-- 数据行数显示 -->
				<view class="data-count-info">
					<text class="data-count-text">{{
						searchText || activeFilters.length > 0
						? `筛选结果: ${filteredData.length} 条数据`
						: `共 ${tableData.length} 条数据`
					}}</text>
				</view>

				<!-- 整体滚动视图 -->
				<scroll-view class="table-scroll" scroll-x scroll-y>
					<!-- 表头 (固定在顶部) -->
					<view class="table-header-container">
						<view class="table-row">
							<!-- 批量模式下的空白单元格，与复选框对齐 -->
							<view v-if="batchMode" class="table-cell table-cell-checkbox table-header-cell">
								<text></text>
							</view>

							<template v-for="(column, index) in columns" :key="column ? column.id : index">
								<view
									v-if="column && !isHiddenColumn(column)"
									class="table-cell table-header-cell"
									:class="getColumnClass(column.name)"
									:style="getColumnStyle(column.name)"
								>
									<text class="table-header-text">{{ getColumnDisplayName(column.name) }}</text>
								</view>
							</template>
						</view>
					</view>

					<!-- 表格选择头部 -->
					<view v-if="batchMode" class="table-batch-header">
						<view class="batch-select-all">
							<view
								class="checkbox"
								:class="{ 'checkbox-selected': allRowsSelected }"
								@click="toggleSelectAll"
							></view>
							<text class="batch-select-text">全选</text>
						</view>
						<view class="batch-actions">
							<text class="batch-action-text" @click="deleteSelectedRows">删除所选 ({{ selectedRows.length }})</text>
							<text class="batch-action-cancel" @click="cancelBatchMode">取消</text>
						</view>
					</view>

					<!-- 表格数据行 -->
					<view
						v-for="(row, rowIndex) in filteredData"
						:key="rowIndex"
						class="table-row"
						:class="{
							'table-row-even': rowIndex % 2 === 0,
							'table-row-selected': isRowSelected(row)
						}"
						@touchstart="batchMode ? null : handleTouchStart(row, $event)"
						@touchend="batchMode ? null : handleTouchEnd()"
						@touchmove="batchMode ? null : handleTouchMove()"
						@click="batchMode ? toggleRowSelection(row) : null"
					>
						<!-- 批量选择复选框 -->
						<view v-if="batchMode" class="table-cell table-cell-checkbox">
							<view
								class="checkbox"
								:class="{ 'checkbox-selected': isRowSelected(row) }"
							></view>
						</view>

						<!-- 数据单元格 -->
						<template v-for="(column, colIndex) in columns" :key="column ? column.id : colIndex">
							<view
								v-if="column && !isHiddenColumn(column)"
								class="table-cell"
								:class="getColumnClass(column.name)"
								:style="getColumnStyle(column.name)"
							>
								<text
									class="table-cell-text"
									:class="{
										'text-primary': column.is_primary_key === 1,
										'text-highlight': isHighlighted(row, column.name),
										'text-wrap': shouldWrapText(column.name)
									}"
								>
									{{ formatCellValue(row[column.name], column.type) }}
								</text>
							</view>
						</template>
					</view>
				</scroll-view>
			</view>
		</view>

		<view v-if="activeTab === 'data'" class="fab-button" @click="addData">
			<text class="fab-icon">+</text>
		</view>
	</view>
</template>

<script>
	import {
		getTableColumns,
		queryTableData,
		dropTable,
		deleteTableRow,
		updateTableRow,
		updateColumnsOrder
	} from '@/utils/sqlite.js';
	import { UI_CONFIG, FILTER_OPERATORS, EXPORT_FORMATS } from '@/config/index.js';
	import { showLoading, hideLoading, showToast, showConfirm, debounce } from '@/utils/common.js';

	export default {
		data() {
			// 打印调试信息
			console.log('初始化表格详情页，UI_CONFIG:', UI_CONFIG);

			return {
				tableId: 0,
				tableName: '',
				columns: [],
				tableData: [],
				activeTab: 'data', // 默认显示数据标签页，方便调试
				isLoading: false,

				// 搜索和筛选
				searchText: '',
				searchDebounceTimer: null,
				activeFilters: [],

				// 批量操作
				batchMode: false,
				selectedRows: [],

				// 导出选项
				exportFormat: 'csv',

				// 长按相关
				longPressTimer: null,
				longPressThreshold: UI_CONFIG.longPressThreshold || 800, // 长按触发时间，单位毫秒，默认800ms
				currentTouchRow: null,
				isTouchMoved: false,

				// 调试信息
				debugInfo: {
					lastLoadTime: null,
					rowIdStatus: 'unknown',
					dataRowCount: 0
				}
			}
		},

		computed: {
			// 过滤后的数据
			filteredData() {
				if (!this.tableData.length) return [];

				let result = [...this.tableData];

				// 应用搜索过滤
				if (this.searchText) {
					const searchLower = this.searchText.toLowerCase();
					result = result.filter(row => {
						// 在所有列中搜索
						for (const column of this.columns) {
							const value = row[column.name];
							if (value !== undefined && value !== null) {
								const strValue = String(value).toLowerCase();
								if (strValue.includes(searchLower)) {
									return true;
								}
							}
						}
						return false;
					});
				}

				// 应用高级筛选
				if (this.activeFilters.length) {
					for (const filter of this.activeFilters) {
						result = result.filter(row => {
							const value = row[filter.column];
							if (value === undefined || value === null) {
								return false;
							}

							const filterValue = filter.value;

							switch (filter.operator) {
								case '=':
									return String(value) === String(filterValue);
								case '!=':
									return String(value) !== String(filterValue);
								case '>':
									return Number(value) > Number(filterValue);
								case '<':
									return Number(value) < Number(filterValue);
								case '>=':
									return Number(value) >= Number(filterValue);
								case '<=':
									return Number(value) <= Number(filterValue);
								case '包含':
									return String(value).includes(String(filterValue));
								case '不包含':
									return !String(value).includes(String(filterValue));
								case '开头是':
									return String(value).startsWith(String(filterValue));
								case '结尾是':
									return String(value).endsWith(String(filterValue));
								default:
									return true;
							}
						});
					}
				}

				return result;
			},

			// 是否全选
			allRowsSelected() {
				return this.filteredData.length > 0 && this.selectedRows.length === this.filteredData.length;
			}
		},
		onLoad(options) {
			this.tableId = parseInt(options.id) || 0;
			this.tableName = options.name || '';

			// 加载表格数据
			this.loadTableData();
		},
		onShow() {
			// 每次显示页面时刷新数据
			this.loadTableData();
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 显示操作菜单
			showActions() {
				uni.showActionSheet({
					itemList: ['编辑表结构', '删除表'],
					itemColor: '#000000',
					success: (res) => {
						if (res.tapIndex === 0) {
							this.editTableStructure();
						} else if (res.tapIndex === 1) {
							this.confirmDeleteTable();
						}
					}
				});
			},

			// 编辑表结构
			editTableStructure() {
				uni.navigateTo({
					url: `/pages/table/edit?id=${this.tableId}&name=${this.tableName}`
				});
			},

			// 确认删除表
			confirmDeleteTable() {
				uni.showModal({
					title: '删除表',
					content: `确定要删除表 '${this.tableName}' 吗？此操作不可撤销，表中的所有数据都将丢失。`,
					confirmText: '删除',
					confirmColor: '#FF0000',
					success: (res) => {
						if (res.confirm) {
							this.deleteTable();
						}
					}
				});
			},

			// 删除表
			async deleteTable() {
				try {
					const success = await dropTable(this.tableId, this.tableName);

					if (success) {
						uni.showToast({
							title: '表已删除',
							icon: 'success'
						});

						// 返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else {
						uni.showToast({
							title: '删除表失败',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('删除表失败', e);
					uni.showToast({
						title: '删除表失败: ' + (e.message || e),
						icon: 'none'
					});
				}
			},

			// 加载表格数据
			async loadTableData() {
				this.isLoading = true;

				try {
					// 加载列信息
					await this.loadColumns();

					// 加载表数据
					const data = await queryTableData(this.tableName);

					// 检查数据是否包含rowid
					if (data && data.length > 0) {
						const hasRowId = data.every(row => row.rowid !== undefined);
						console.log(`表 ${this.tableName} 数据加载完成，共 ${data.length} 行，是否都有rowid: ${hasRowId}`);

						// 更新调试信息
						this.debugInfo.lastLoadTime = new Date().toLocaleTimeString();
						this.debugInfo.rowIdStatus = hasRowId ? 'present' : 'missing';
						this.debugInfo.dataRowCount = data.length;

						// 如果有数据但没有rowid，尝试添加
						if (!hasRowId) {
							console.warn(`表 ${this.tableName} 的数据缺少rowid，尝试添加`);
							// 打印第一行数据，帮助调试
							console.log('第一行数据:', JSON.stringify(data[0]));

							// 尝试手动添加rowid
							for (let i = 0; i < data.length; i++) {
								// 如果没有rowid，尝试使用id字段
								if (data[i].id !== undefined) {
									data[i].rowid = data[i].id;
									console.log(`为第 ${i+1} 行数据添加rowid = ${data[i].id}`);
								} else {
									// 如果没有id字段，使用索引作为临时rowid
									data[i].rowid = i + 1;
									console.log(`为第 ${i+1} 行数据添加临时rowid = ${i+1}`);
								}
							}

							// 再次检查
							const hasRowIdNow = data.every(row => row.rowid !== undefined);
							console.log(`手动添加rowid后，是否都有rowid: ${hasRowIdNow}`);
							this.debugInfo.rowIdStatus = hasRowIdNow ? 'added' : 'failed';
						}
					} else {
						console.log(`表 ${this.tableName} 没有数据`);
						this.debugInfo.dataRowCount = 0;
						this.debugInfo.rowIdStatus = 'no_data';
					}

					this.tableData = data;
				} catch (e) {
					console.error('加载表格数据失败', e);
					uni.showToast({
						title: '加载表格数据失败',
						icon: 'none'
					});
				} finally {
					this.isLoading = false;
				}
			},

			// 加载列信息
			async loadColumns() {
				try {
					const columns = await getTableColumns(this.tableId);

					if (!columns || columns.length === 0) {
						console.error('未找到列信息');
						uni.showToast({
							title: '未找到列信息，请确保表已正确创建',
							icon: 'none',
							duration: 3000
						});
						return;
					}

					// 过滤掉无效的列
					const validColumns = columns.filter(column => column && column.name);

					if (validColumns.length === 0) {
						console.error('所有列都无效');
						uni.showToast({
							title: '表结构异常，请重新创建表',
							icon: 'none',
							duration: 3000
						});
						return;
					}

					console.log('有效列数量:', validColumns.length);

					// 按order_index排序
					this.columns = validColumns.sort((a, b) => a.order_index - b.order_index);
				} catch (e) {
					console.error('加载列信息失败', e);
					uni.showToast({
						title: '加载列信息失败: ' + (e.message || e),
						icon: 'none',
						duration: 3000
					});
				}
			},

			// 获取列类型文本
			getColumnTypeText(type) {
				switch (type) {
					case 'TEXT': return '文本 (TEXT)';
					case 'INTEGER': return '整数 (INTEGER)';
					case 'REAL': return '小数 (REAL)';
					case 'BLOB': return '二进制 (BLOB)';
					default: return type;
				}
			},

			// 格式化单元格值
			formatCellValue(value, type) {
				if (value === undefined || value === null) {
					return '';
				}

				switch (type) {
					case 'INTEGER':
						return value.toString();
					case 'REAL':
						// 格式化小数，最多显示4位小数
						return typeof value === 'number' ? value.toFixed(4).replace(/\.?0+$/, '') : value;
					case 'TEXT':
						// 检查是否是日期时间格式 (YYYY-MM-DD HH:MM:SS)
						if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(value)) {
							return value; // 已经是格式化的日期时间，直接返回
						}

						// 特殊处理articles表的articleContent列，不截断显示
						if (this.tableName === 'articles' &&
							(this.columns.find(c => c.name === 'articleContent' && value === this.tableData.find(r => r[c.name] === value)?.[c.name]))) {
							return value;
						}

						// 特殊处理documents表的内容，不截断显示
						if (this.tableName === 'documents') {
							return value;
						}

						// 如果文本太长，截断显示
						if (typeof value === 'string' && value.length > 50) {
							return value.substring(0, 50) + '...';
						}
						return value;
					default:
						return value.toString();
				}
			},

			// 判断列是否隐藏
			isHiddenColumn(column) {
				if (!column) return false;
				return column.is_hidden === 1;
			},

			// 获取列的中文显示名称
			getColumnDisplayName(columnName) {
				const columnNameMap = {
					// 通用字段
					'id': 'ID',
					'createTime': '创建时间',
					'updateTime': '更新时间',

					// articles表
					'fileName': '文件名',
					'articleType': '条文类型',
					'articleContent': '条文内容',
					'keywords': '关键词',

					// documents表
					'category': '分类',
					'documentNumber': '文件编号',
					'publishingUnit': '发布单位',

					// projects表
					'projectName': '项目名称',
					'legalPerson': '企业法人',
					'constructionLocation': '建设地点',
					'constructionScale': '建设规模及内容',

					// subprojects表
					'subprojectName': '子项目名称',
					'constructionUnit': '建设单位',
					'agentUnit': '代建单位',
					'surveyUnit': '勘察单位',
					'designUnit': '设计单位',
					'supervisionUnit': '监理单位',
					'constructorUnit': '施工单位',
					'projectDescription': '项目描述'
				};

				return columnNameMap[columnName] || columnName;
			},

			// 获取表的中文显示名称
			getTableDisplayName(tableName) {
				const tableNameMap = {
					'articles': '条文表',
					'documents': '文档表',
					'projects': '项目表',
					'subprojects': '子项目表'
				};

				return tableNameMap[tableName] || tableName;
			},

			// 获取列的CSS类
			getColumnClass(columnName) {
				if (this.tableName === 'articles') {
					switch (columnName) {
						case 'fileName':
							return 'column-filename';
						case 'articleType':
							return 'column-articletype';
						case 'articleContent':
							return 'column-articlecontent';
						case 'keywords':
							return 'column-keywords';
						default:
							return '';
					}
				} else if (this.tableName === 'documents') {
					switch (columnName) {
						case 'fileName':
							return 'column-filename';
						case 'category':
							return 'column-category';
						case 'documentNumber':
							return 'column-docnumber';
						case 'publishingUnit':
							return 'column-publishunit';
						default:
							return '';
					}
				} else if (this.tableName === 'projects') {
					switch (columnName) {
						case 'projectName':
							return 'column-projectname';
						case 'legalPerson':
							return 'column-legalperson';
						case 'constructionLocation':
							return 'column-constructionlocation';
						case 'constructionScale':
							return 'column-constructionscale';
						default:
							return '';
					}
				} else if (this.tableName === 'subprojects') {
					switch (columnName) {
						case 'projectName':
							return 'column-projectname';
						case 'subprojectName':
							return 'column-subprojectname';
						case 'constructionLocation':
							return 'column-constructionlocation';
						case 'constructionUnit':
							return 'column-constructionunit';
						case 'agentUnit':
							return 'column-agentunit';
						case 'surveyUnit':
							return 'column-surveyunit';
						case 'designUnit':
							return 'column-designunit';
						case 'supervisionUnit':
							return 'column-supervisionunit';
						case 'constructorUnit':
							return 'column-constructorunit';
						case 'projectDescription':
							return 'column-projectdescription';
						default:
							return '';
					}
				}
				return '';
			},

			// 获取列的样式
			getColumnStyle(columnName) {
				if (this.tableName === 'articles') {
					switch (columnName) {
						case 'fileName':
							return 'min-width: 150rpx; max-width: 200rpx;';
						case 'articleType':
							return 'min-width: 120rpx; max-width: 150rpx;';
						case 'articleContent':
							return 'min-width: 400rpx; width: auto; flex: 1;';
						case 'keywords':
							return 'min-width: 150rpx; max-width: 200rpx;';
						default:
							return '';
					}
				} else if (this.tableName === 'documents') {
					switch (columnName) {
						case 'fileName':
							return 'min-width: 200rpx; max-width: 300rpx;';
						case 'category':
							return 'min-width: 150rpx; max-width: 200rpx;';
						case 'documentNumber':
							return 'min-width: 200rpx; max-width: 250rpx;';
						case 'publishingUnit':
							return 'min-width: 200rpx; max-width: 300rpx;';
						default:
							return '';
					}
				} else if (this.tableName === 'projects') {
					switch (columnName) {
						case 'projectName':
							return 'min-width: 300rpx; width: auto; flex: 1;';
						case 'legalPerson':
							return 'min-width: 200rpx; max-width: 300rpx;';
						case 'constructionLocation':
							return 'min-width: 200rpx; max-width: 300rpx;';
						case 'constructionScale':
							return 'min-width: 400rpx; width: auto; flex: 2;';
						default:
							return 'min-width: 150rpx; max-width: 200rpx;';
					}
				} else if (this.tableName === 'subprojects') {
					switch (columnName) {
						case 'projectName':
							return 'min-width: 120rpx; max-width: 150rpx;';
						case 'subprojectName':
							return 'min-width: 120rpx; max-width: 150rpx;';
						case 'constructionLocation':
							return 'min-width: 80rpx; max-width: 100rpx;';
						case 'constructionUnit':
							return 'min-width: 100rpx; max-width: 130rpx;';
						case 'agentUnit':
							return 'min-width: 80rpx; max-width: 100rpx;';
						case 'surveyUnit':
							return 'min-width: 70rpx; max-width: 90rpx;';
						case 'designUnit':
							return 'min-width: 70rpx; max-width: 90rpx;';
						case 'supervisionUnit':
							return 'min-width: 80rpx; max-width: 100rpx;';
						case 'constructorUnit':
							return 'min-width: 100rpx; max-width: 130rpx;';
						case 'projectDescription':
							return 'min-width: 120rpx; width: auto; flex: 1;';
						default:
							return 'min-width: 70rpx; max-width: 90rpx;';
					}
				}
				return '';
			},

			// 判断是否应该换行显示文本
			shouldWrapText(columnName) {
				if (this.tableName === 'articles' && columnName === 'articleContent') {
					return true;
				}
				if (this.tableName === 'projects' && columnName === 'constructionScale') {
					return true;
				}
				if (this.tableName === 'subprojects' && columnName === 'projectDescription') {
					return true;
				}
				return false;
			},

			// 添加数据
			addData() {
				uni.navigateTo({
					url: `/pages/data/entry?tableId=${this.tableId}&tableName=${this.tableName}`
				});
			},

			// 显示行操作菜单
			showRowActions(row) {
				// 确保行存在且有rowid
				if (!row || !row.rowid) {
					uni.showToast({
						title: '无法操作此行，缺少行ID',
						icon: 'none'
					});
					return;
				}

				// 显示操作菜单
				uni.showActionSheet({
					itemList: ['编辑数据', '删除数据'],
					success: (res) => {
						if (res.tapIndex === 0) {
							// 编辑数据
							this.editRow(row);
						} else if (res.tapIndex === 1) {
							// 删除数据
							this.showDeleteRowConfirm(row);
						}
					}
				});
			},

			// 编辑行数据
			editRow(row) {
				// 将行数据转换为JSON字符串并编码
				const rowDataStr = encodeURIComponent(JSON.stringify(row));

				// 导航到编辑页面
				uni.navigateTo({
					url: `/pages/data/edit?tableId=${this.tableId}&tableName=${this.tableName}&rowId=${row.rowid}&rowData=${rowDataStr}`
				});
			},

			// 显示删除行确认对话框
			showDeleteRowConfirm(row) {
				// 确保行存在
				if (!row) {
					uni.showToast({
						title: '无法删除，行数据无效',
						icon: 'none'
					});
					return;
				}

				// 构建行内容预览
				let rowPreview = '';
				for (const column of this.columns) {
					// 确保列存在
					if (!column || !column.name) continue;

					if (row[column.name] !== undefined && row[column.name] !== null && row[column.name] !== '') {
						rowPreview += `${column.name}: ${row[column.name]}, `;
						if (rowPreview.length > 50) {
							rowPreview = rowPreview.substring(0, 50) + '...';
							break;
						}
					}
				}

				// 如果没有内容预览，使用行ID
				if (!rowPreview) {
					rowPreview = `行ID: ${row.rowid}`;
				}

				// 显示确认对话框
				uni.showModal({
					title: '删除数据',
					content: `确定要删除此行数据吗？\n${rowPreview}`,
					confirmText: '删除',
					confirmColor: '#FF0000',
					success: (res) => {
						if (res.confirm) {
							this.deleteRow(row.rowid);
						}
					}
				});
			},

			// 删除行
			async deleteRow(rowId) {
				try {
					const success = await deleteTableRow(this.tableName, rowId);

					if (success) {
						uni.showToast({
							title: '数据已删除',
							icon: 'success'
						});

						// 重新加载数据
						this.loadTableData();
					} else {
						uni.showToast({
							title: '删除数据失败',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('删除数据失败', e);
					uni.showToast({
						title: '删除数据失败: ' + (e.message || e),
						icon: 'none'
					});
				}
			},

			// 显示列操作菜单
			showColumnActions(column, index) {
				// 准备操作选项
				const actions = [];

				// 如果不是第一列，可以上移
				if (index > 0) {
					actions.push('上移一位');
				}

				// 如果不是最后一列，可以下移
				if (index < this.columns.length - 1) {
					actions.push('下移一位');
				}

				// 如果没有可用操作，提示用户
				if (actions.length === 0) {
					uni.showToast({
						title: '无法移动此列',
						icon: 'none'
					});
					return;
				}

				// 显示操作菜单
				uni.showActionSheet({
					itemList: actions,
					success: (res) => {
						const action = actions[res.tapIndex];

						if (action === '上移一位') {
							this.moveColumnUp(column, index);
						} else if (action === '下移一位') {
							this.moveColumnDown(column, index);
						}
					}
				});
			},

			// 上移列
			async moveColumnUp(column, index) {
				if (index <= 0) return;

				// 获取上一列
				const prevColumn = this.columns[index - 1];

				// 交换顺序
				const updatedColumns = [...this.columns];
				const temp = updatedColumns[index].order_index;
				updatedColumns[index].order_index = updatedColumns[index - 1].order_index;
				updatedColumns[index - 1].order_index = temp;

				// 更新数据库
				await this.updateColumnOrder([
					{ id: column.id, order_index: updatedColumns[index].order_index },
					{ id: prevColumn.id, order_index: updatedColumns[index - 1].order_index }
				]);
			},

			// 下移列
			async moveColumnDown(column, index) {
				if (index >= this.columns.length - 1) return;

				// 获取下一列
				const nextColumn = this.columns[index + 1];

				// 交换顺序
				const updatedColumns = [...this.columns];
				const temp = updatedColumns[index].order_index;
				updatedColumns[index].order_index = updatedColumns[index + 1].order_index;
				updatedColumns[index + 1].order_index = temp;

				// 更新数据库
				await this.updateColumnOrder([
					{ id: column.id, order_index: updatedColumns[index].order_index },
					{ id: nextColumn.id, order_index: updatedColumns[index + 1].order_index }
				]);
			},

			// 更新列顺序
			async updateColumnOrder(columns) {
				try {
					const success = await updateColumnsOrder(this.tableId, columns);

					if (success) {
						uni.showToast({
							title: '列顺序已更新',
							icon: 'success'
						});

						// 重新加载列信息
						await this.loadColumns();
					} else {
						uni.showToast({
							title: '更新列顺序失败',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('更新列顺序失败', e);
					uni.showToast({
						title: '更新列顺序失败: ' + (e.message || e),
						icon: 'none'
					});
				}
			},

			// ===== 搜索和筛选功能 =====

			// 搜索输入处理（防抖）
			onSearchInput: debounce(function() {
				// 搜索逻辑已在计算属性中处理
				console.log('搜索:', this.searchText);
			}, UI_CONFIG.searchDebounceTime),

			// 清除搜索
			clearSearch() {
				this.searchText = '';
			},

			// 判断单元格是否需要高亮显示（匹配搜索文本）
			isHighlighted(row, columnName) {
				if (!this.searchText) return false;

				const value = row[columnName];
				if (value === undefined || value === null) return false;

				const strValue = String(value).toLowerCase();
				const searchLower = this.searchText.toLowerCase();

				return strValue.includes(searchLower);
			},

			// 显示筛选选项
			showFilterOptions() {
				// 确保有列
				if (!this.columns || this.columns.length === 0) {
					uni.showToast({
						title: '没有可筛选的列',
						icon: 'none'
					});
					return;
				}

				// 准备列选项（过滤掉无效列）
				const validColumns = this.columns.filter(col => col && col.name);
				if (validColumns.length === 0) {
					uni.showToast({
						title: '没有可筛选的列',
						icon: 'none'
					});
					return;
				}

				const columnOptions = validColumns.map(col => col.name);

				// 显示列选择
				uni.showActionSheet({
					itemList: columnOptions,
					success: (res) => {
						const selectedColumn = columnOptions[res.tapIndex];
						this.showFilterOperators(selectedColumn);
					}
				});
			},

			// 显示筛选操作符
			showFilterOperators(columnName) {
				// 获取列类型
				const column = this.columns.find(col => col && col.name === columnName);

				// 确保列存在
				if (!column) {
					uni.showToast({
						title: '未找到列信息',
						icon: 'none'
					});
					return;
				}

				let operators;

				// 根据列类型提供不同的操作符
				if (column.type === 'TEXT') {
					operators = FILTER_OPERATORS.TEXT;
				} else if (column.type === 'INTEGER' || column.type === 'REAL') {
					operators = FILTER_OPERATORS.NUMERIC;
				} else {
					operators = FILTER_OPERATORS.COMMON;
				}

				// 显示操作符选择
				uni.showActionSheet({
					itemList: operators.map(op => op.label),
					success: (res) => {
						const selectedOperator = operators[res.tapIndex].value;
						this.showFilterValueInput(columnName, selectedOperator);
					}
				});
			},

			// 显示筛选值输入
			showFilterValueInput(columnName, operator) {
				uni.showModal({
					title: '输入筛选值',
					content: `为 ${columnName} ${operator} ? 输入值:`,
					editable: true,
					placeholderText: '输入筛选值',
					success: (res) => {
						if (res.confirm && res.content) {
							// 添加筛选条件
							this.addFilter(columnName, operator, res.content);
						}
					}
				});
			},

			// 添加筛选条件
			addFilter(column, operator, value) {
				this.activeFilters.push({
					column,
					operator,
					value
				});
			},

			// 移除筛选条件
			removeFilter(index) {
				this.activeFilters.splice(index, 1);
			},

			// 清除所有筛选条件
			clearAllFilters() {
				this.activeFilters = [];
			},

			// ===== 批量操作功能 =====

			// 显示批量操作选项
			showBatchOptions() {
				if (this.batchMode) {
					this.cancelBatchMode();
					return;
				}

				// 进入批量模式
				this.batchMode = true;
				this.selectedRows = [];
			},

			// 取消批量模式
			cancelBatchMode() {
				this.batchMode = false;
				this.selectedRows = [];
			},

			// 切换行选择
			toggleRowSelection(row) {
				const index = this.selectedRows.findIndex(r => r.rowid === row.rowid);

				if (index >= 0) {
					// 已选中，取消选择
					this.selectedRows.splice(index, 1);
				} else {
					// 未选中，添加选择
					this.selectedRows.push(row);
				}
			},

			// 判断行是否被选中
			isRowSelected(row) {
				return this.selectedRows.some(r => r.rowid === row.rowid);
			},

			// 全选/取消全选
			toggleSelectAll() {
				if (this.allRowsSelected) {
					// 已全选，取消全选
					this.selectedRows = [];
				} else {
					// 未全选，全选
					this.selectedRows = [...this.filteredData];
				}
			},

			// 删除选中的行
			deleteSelectedRows() {
				if (this.selectedRows.length === 0) {
					uni.showToast({
						title: '请先选择要删除的数据',
						icon: 'none'
					});
					return;
				}

				uni.showModal({
					title: '批量删除',
					content: `确定要删除选中的 ${this.selectedRows.length} 条数据吗？此操作不可恢复。`,
					confirmText: '删除',
					confirmColor: '#FF0000',
					success: async (res) => {
						if (res.confirm) {
							await this.performBatchDelete();
						}
					}
				});
			},

			// 执行批量删除
			async performBatchDelete() {
				try {
					uni.showLoading({
						title: '正在删除...'
					});

					let successCount = 0;
					let failCount = 0;

					// 逐个删除选中的行
					for (const row of this.selectedRows) {
						try {
							const success = await deleteTableRow(this.tableName, row.rowid);
							if (success) {
								successCount++;
							} else {
								failCount++;
							}
						} catch (e) {
							console.error('删除行失败', e);
							failCount++;
						}
					}

					uni.hideLoading();

					// 显示结果
					uni.showToast({
						title: `删除完成: ${successCount}成功, ${failCount}失败`,
						icon: 'none',
						duration: 2000
					});

					// 重新加载数据
					await this.loadTableData();

					// 退出批量模式
					this.cancelBatchMode();
				} catch (e) {
					uni.hideLoading();
					console.error('批量删除失败', e);
					uni.showToast({
						title: '批量删除失败: ' + (e.message || e),
						icon: 'none'
					});
				}
			},

			// ===== 自定义长按处理 =====

			// 处理触摸开始
			handleTouchStart(row, event) {
				// 清除之前的定时器
				if (this.longPressTimer) {
					clearTimeout(this.longPressTimer);
				}

				// 检查行是否有rowid
				if (!row || !row.rowid) {
					console.warn('行数据缺少rowid，无法处理长按事件:', row);

					// 尝试从行数据中找到可能的ID字段
					if (row) {
						const possibleIds = ['id', 'ID', 'Id', 'rowId', 'row_id'];
						for (const idField of possibleIds) {
							if (row[idField] !== undefined) {
								console.log(`找到可能的ID字段 ${idField}:`, row[idField]);
								// 添加rowid
								row.rowid = row[idField];
								console.log('已添加rowid:', row.rowid);
								break;
							}
						}
					}
				}

				// 记录当前触摸的行
				this.currentTouchRow = row;
				this.isTouchMoved = false;

				// 设置长按定时器
				this.longPressTimer = setTimeout(() => {
					if (!this.isTouchMoved && this.currentTouchRow) {
						// 再次检查rowid
						if (!this.currentTouchRow.rowid) {
							console.warn('长按触发时行数据仍然缺少rowid');

							// 显示提示
							uni.showToast({
								title: '无法操作此行，缺少行ID',
								icon: 'none'
							});
							return;
						}

						// 触发长按操作
						this.showRowActions(this.currentTouchRow);

						// 添加触感反馈（如果设备支持）
						if (uni.vibrateShort) {
							uni.vibrateShort();
						}
					}
				}, this.longPressThreshold);
			},

			// 处理触摸结束
			handleTouchEnd() {
				// 清除定时器
				if (this.longPressTimer) {
					clearTimeout(this.longPressTimer);
					this.longPressTimer = null;
				}

				// 重置状态
				this.currentTouchRow = null;
			},

			// 处理触摸移动
			handleTouchMove() {
				// 标记已移动
				this.isTouchMoved = true;

				// 清除定时器
				if (this.longPressTimer) {
					clearTimeout(this.longPressTimer);
					this.longPressTimer = null;
				}
			},

			// ===== 导入功能 =====

			// 显示导入选项
			showImportOptions() {
				// 将列信息传递给导入页面
				const columnsStr = encodeURIComponent(JSON.stringify(this.columns));

				// 导航到导入页面
				uni.navigateTo({
					url: `/pages/data/import?tableId=${this.tableId}&tableName=${this.tableName}&columns=${columnsStr}`
				});
			},

			// 跳转到智能输入页面
			goToSmartImport() {
				if (this.tableName !== 'articles') {
					uni.showToast({
						title: '智能输入功能仅适用于条文表',
						icon: 'none'
					});
					return;
				}

				// 导航到智能输入页面
				uni.navigateTo({
					url: `/pages/data/smart-import?tableId=${this.tableId}&tableName=${this.tableName}`
				});
			},

			// ===== 导出功能 =====

			// 显示导出选项
			showExportOptions() {
				uni.showActionSheet({
					itemList: EXPORT_FORMATS.map(format => `导出为${format.label}`),
					success: (res) => {
						const format = EXPORT_FORMATS[res.tapIndex].value;
						this.exportData(format);
					}
				});
			},

			// 导出数据
			async exportData(format) {
				try {
					// 检查是否有数据
					if (this.filteredData.length === 0) {
						uni.showToast({
							title: '没有数据可导出',
							icon: 'none'
						});
						return;
					}

					// 只支持CSV和JSON格式
					if (format !== 'csv' && format !== 'json') {
						uni.showToast({
							title: '仅支持CSV和JSON格式',
							icon: 'none'
						});
						return;
					}

					uni.showLoading({
						title: '正在导出...'
					});

					let content = '';
					const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
					let fileName = `${this.tableName}_${timestamp}`;
					let mimeType = '';

					// 根据格式生成内容 - 使用筛选后的数据
					switch (format) {
						case 'csv':
							content = this.generateCSV();
							fileName += '.csv';
							mimeType = 'text/csv';
							break;
						case 'json':
							content = this.generateJSON();
							fileName += '.json';
							mimeType = 'application/json';
							break;
					}

					// #ifdef H5
					// 在H5环境中，使用Blob和a标签下载
					try {
						const blob = new Blob([content], { type: mimeType });
						const url = URL.createObjectURL(blob);

						// 创建下载链接
						const a = document.createElement('a');
						a.href = url;
						a.download = fileName;
						document.body.appendChild(a);
						a.click();
						document.body.removeChild(a);
						URL.revokeObjectURL(url);

						uni.hideLoading();

						uni.showToast({
							title: '导出成功',
							icon: 'success'
						});
					} catch (e) {
						console.error('H5导出错误:', e);
						throw e;
					}
					// #endif

					// #ifdef APP-PLUS
					// 在APP环境中，根据平台使用不同的导出方法
					try {
						// 检查平台
						const platform = uni.getSystemInfoSync().platform;
						console.log('当前平台:', platform);

						if (platform === 'android') {
							// Android平台 - 保存到Download文件夹
							// 请求存储权限
							await new Promise((resolve, reject) => {
								plus.android.requestPermissions(
									['android.permission.WRITE_EXTERNAL_STORAGE'],
									function(resultObj) {
										console.log('权限请求结果:', resultObj);
										if (resultObj.granted.length === 1) {
											resolve();
										} else {
											reject(new Error('未授予存储权限'));
										}
									},
									function(error) {
										console.error('权限请求失败:', error);
										reject(error);
									}
								);
							});

							console.log('已获得存储权限，准备导出数据');

							// 使用原生 Java 方法
							const Environment = plus.android.importClass("android.os.Environment");
							const File = plus.android.importClass("java.io.File");
							const FileOutputStream = plus.android.importClass("java.io.FileOutputStream");
							const OutputStreamWriter = plus.android.importClass("java.io.OutputStreamWriter");
							const BufferedWriter = plus.android.importClass("java.io.BufferedWriter");

							// 获取外部存储路径
							const externalStorageDir = Environment.getExternalStorageDirectory();
							const filePath = externalStorageDir.getAbsolutePath() + "/Download/" + fileName;
							console.log('使用外部存储路径:', filePath);

							// 创建目录
							const fileDir = new File(filePath).getParentFile();
							if (!fileDir.exists()) {
								fileDir.mkdirs();
							}

							// 写入文件
							const fos = new FileOutputStream(filePath);
							const osw = new OutputStreamWriter(fos, "UTF-8");
							const bw = new BufferedWriter(osw);

							bw.write(content);
							bw.flush();
							bw.close();
							osw.close();
							fos.close();

							console.log('数据导出成功:', filePath);

							uni.hideLoading();

							// 显示成功消息
							uni.showModal({
								title: '导出成功',
								content: `已导出 ${this.filteredData.length} 条数据到：\n${filePath}`,
								showCancel: false,
								confirmText: '确定'
							});
						} else if (platform === 'ios') {
							// iOS平台 - 保存到应用沙盒
							console.log('iOS平台导出数据');

							// 获取应用沙盒文档目录
							const dirPath = plus.io.convertLocalFileSystemURL('_doc');
							const filePath = dirPath + '/' + fileName;
							console.log('iOS文件路径:', filePath);

							// 创建文件并写入内容
							const fileEntry = await new Promise((resolve, reject) => {
								plus.io.requestFileSystem(plus.io.PRIVATE_DOC, fs => {
									fs.root.getFile(fileName, { create: true, exclusive: false }, fileEntry => {
										resolve(fileEntry);
									}, err => {
										reject(err);
									});
								}, err => {
									reject(err);
								});
							});

							// 写入文件内容
							await new Promise((resolve, reject) => {
								fileEntry.createWriter(writer => {
									writer.onwrite = () => {
										resolve();
									};
									writer.onerror = err => {
										reject(err);
									};
									// 写入内容
									writer.write(new Blob([content], { type: mimeType }));
								}, err => {
									reject(err);
								});
							});

							console.log('iOS数据导出成功:', filePath);

							uni.hideLoading();

							// 显示成功消息
							uni.showModal({
								title: '导出成功',
								content: `已导出 ${this.filteredData.length} 条数据到文档目录。\n您可以通过iTunes文件共享或其他方式访问该文件。`,
								showCancel: false,
								confirmText: '确定'
							});
						} else {
							// 其他平台
							throw new Error('不支持的平台: ' + platform);
						}
					} catch (e) {
						console.error('APP导出错误:', e);
						throw e;
					}
					// #endif

					// #ifdef MP-WEIXIN
					// 在微信小程序中，保存到本地文件
					try {
						const fs = uni.getFileSystemManager();
						const userPath = wx.env.USER_DATA_PATH;
						const filePath = `${userPath}/${fileName}`;

						// 写入文件
						fs.writeFile({
							filePath: filePath,
							data: content,
							encoding: 'utf8',
							success: () => {
								uni.hideLoading();

								// 保存文件到本地
								uni.saveFile({
									tempFilePath: filePath,
									success: (res) => {
										uni.showModal({
											title: '导出成功',
											content: `已导出 ${this.filteredData.length} 条数据。`,
											showCancel: false,
											confirmText: '确定'
										});
									},
									fail: (err) => {
										console.error('保存文件失败', err);
										uni.showToast({
											title: '保存文件失败',
											icon: 'none'
										});
									}
								});
							},
							fail: (err) => {
								console.error('写入文件失败', err);
								throw err;
							}
						});
					} catch (e) {
						console.error('微信小程序导出错误:', e);
						throw e;
					}
					// #endif
				} catch (e) {
					uni.hideLoading();
					console.error('导出数据失败', e);
					uni.showToast({
						title: '导出数据失败: ' + (e.message || e),
						icon: 'none'
					});
				}
			},



			// 生成CSV格式
			generateCSV() {
				// 获取列名
				const headers = this.columns.map(col => col.name);

				// 创建CSV内容
				// 添加BOM标记，确保Excel等应用能正确识别UTF-8编码的中文
				let csv = '\ufeff';

				// 添加表头
				csv += headers.join(',') + '\n';

				// 添加数据行
				for (const row of this.filteredData) {
					const rowData = headers.map(header => {
						const value = row[header];
						if (value === undefined || value === null) {
							return '';
						}

						// 处理包含逗号、引号或换行符的值
						const strValue = String(value);
						if (strValue.includes(',') || strValue.includes('"') || strValue.includes('\n')) {
							return '"' + strValue.replace(/"/g, '""') + '"';
						}

						return strValue;
					});

					csv += rowData.join(',') + '\n';
				}

				return csv;
			},

			// 生成JSON格式
			generateJSON() {
				// 创建数据数组
				const data = this.filteredData.map(row => {
					const newRow = {};
					for (const column of this.columns) {
						newRow[column.name] = row[column.name];
					}
					return newRow;
				});

				// 转换为JSON字符串（美化格式）
				// 添加BOM标记，确保文本编辑器能正确识别UTF-8编码的中文
				return '\ufeff' + JSON.stringify(data, null, 2);
			},


		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 30rpx 30rpx 40rpx 30rpx; /* 增加上下内边距 */
		padding-top: calc(var(--status-bar-height) + 20rpx); /* 增加顶部空间 */
		min-height: 120rpx; /* 设置最小高度 */
	}

	.header-left, .header-right {
		width: 120rpx;
		align-self: flex-start; /* 让左右按钮对齐到顶部 */
		margin-top: 10rpx;
	}

	.header-center {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
		max-width: 400rpx;
	}

	.header-back, .header-action {
		color: #FFFFFF;
		font-size: 28rpx;
	}

	.header-title {
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: bold;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-bottom: 8rpx; /* 与中文标题的间距 */
	}

	.header-subtitle {
		color: #FFFFFF;
		font-size: 28rpx;
		font-weight: normal;
		opacity: 0.9; /* 稍微透明，区分主标题 */
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.tabs {
		display: flex;
		background-color: #FFFFFF;
		border-bottom: 1rpx solid #EEEEEE;
	}

	.tab-item {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80rpx;
		position: relative;
	}

	.tab-item-active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 25%;
		width: 50%;
		height: 4rpx;
		background-color: #007AFF;
	}

	.tab-text {
		font-size: 28rpx;
		color: #333333;
	}

	.tab-item-active .tab-text {
		color: #007AFF;
		font-weight: bold;
	}

	.tab-content {
		flex: 1;
		padding: 30rpx;
		overflow-y: auto;
	}

	.empty-tip, .loading-tip {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 300rpx;
		background-color: #FFFFFF;
		border-radius: 8rpx;
		color: #999999;
		font-size: 28rpx;
	}

	.loading-tip {
		background-color: #F8F8F8;
	}

	/* 调试信息样式 */
	.debug-info {
		margin-top: 30rpx;
		padding: 20rpx;
		background-color: #f8f8f8;
		border: 1px solid #ddd;
		border-radius: 10rpx;
		text-align: left;
		width: 80%;
	}

	.debug-info-title {
		font-weight: bold;
		color: #666;
		margin-bottom: 10rpx;
		display: block;
	}

	.debug-info-item {
		font-size: 24rpx;
		color: #666;
		margin: 5rpx 0;
		display: block;
	}

	/* 数据行数显示样式 */
	.data-count-info {
		padding: 10rpx 20rpx;
		background-color: #f8f8f8;
		border-bottom: 1px solid #eee;
		text-align: right;
	}

	.data-count-text {
		font-size: 24rpx;
		color: #666;
	}

	.data-count-info-empty {
		margin-top: 20rpx;
		padding: 10rpx;
		background-color: #f8f8f8;
		border-radius: 8rpx;
		text-align: center;
	}

	.column-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.column-item {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx;
	}

	.column-item-header {
		margin-bottom: 10rpx;
	}

	.column-name {
		font-size: 30rpx;
		font-weight: bold;
	}

	.column-order {
		font-size: 24rpx;
		color: #666666;
	}

	.columns-tip {
		margin-bottom: 10rpx;
	}

	.columns-tip-text {
		font-size: 24rpx;
		color: #666666;
		font-style: italic;
	}

	.column-details {
		display: flex;
		flex-direction: column;
		gap: 6rpx;
	}

	.column-type {
		font-size: 26rpx;
		color: #333333;
	}

	.column-attributes {
		display: flex;
		flex-wrap: wrap;
		gap: 10rpx;
	}

	.column-attribute {
		background-color: #F0F0F0;
		padding: 4rpx 12rpx;
		border-radius: 4rpx;
		font-size: 22rpx;
		color: #666666;
	}

	.column-foreign-key {
		font-size: 22rpx;
		color: #666666;
		font-style: italic;
		margin-top: 6rpx;
	}

	.data-table {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		overflow: hidden;
		width: 100%;
	}

	.table-scroll {
		width: 100%;
		height: 1000rpx; /* 增加高度以显示更多内容 */
		background-color: #FFFFFF;
	}

	.table-header-container {
		width: 100%;
		border-bottom: 1rpx solid #EEEEEE;
	}

	.table-header-cell {
		background-color: #F8F8F8;
		font-weight: bold;
		position: sticky;
		top: 0;
		z-index: 10;
	}

	.data-tip {
		margin-bottom: 10rpx;
	}

	.data-tip-text {
		font-size: 24rpx;
		color: #666666;
		font-style: italic;
	}

	.table-row {
		display: flex;
		border-bottom: 1rpx solid #EEEEEE;
		transition: background-color 0.2s;
		width: 100%;
		min-width: fit-content;
	}

	.table-row:active {
		background-color: #E0F0FF;
	}

	.table-row-even {
		background-color: #FFFFFF;
	}

	.table-row:last-child {
		border-bottom: none;
	}

	.text-primary {
		color: #007AFF;
		font-weight: bold;
	}

	.text-highlight {
		background-color: #FFFF00;
		padding: 0 4rpx;
		border-radius: 4rpx;
	}

	.text-wrap {
		white-space: normal;
		word-break: break-word;
		overflow: visible;
	}

	.table-row-selected {
		background-color: #E0F0FF;
	}

	/* 搜索和操作栏样式 */
	.search-bar {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
		width: 100%; /* 确保搜索栏占满整个宽度 */
		padding: 0 10rpx; /* 添加左右内边距，减少与屏幕边缘的距离 */
	}

	.search-input-container {
		position: relative;
		width: 100%; /* 搜索框占据整行宽度 */
	}

	.search-input {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx 60rpx 20rpx 20rpx; /* 进一步增加上下内边距，使输入框更高 */
		font-size: 30rpx; /* 增加字体大小 */
		border: 1rpx solid #EEEEEE;
		width: 100%;
		box-sizing: border-box;
		min-width: 0; /* 确保在Flex容器中可以正确缩放 */
		height: 80rpx; /* 固定高度使输入框更大 */
	}

	.search-clear {
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);
		font-size: 40rpx; /* 增大清除按钮 */
		color: #999999;
		width: 50rpx; /* 增大点击区域 */
		height: 50rpx; /* 增大点击区域 */
		text-align: center;
		line-height: 50rpx;
		z-index: 10; /* 确保在输入框上方 */
	}

	.action-buttons {
		display: flex;
		flex-shrink: 0; /* 防止按钮区域被压缩 */
	}

	.action-button {
		background-color: #F0F0F0;
		border-radius: 8rpx;
		padding: 15rpx 10rpx; /* 减小左右内边距，使按钮更紧凑 */
		margin: 0 8rpx 0 0; /* 减小右边距，移除底部边距 */
		min-width: 100rpx; /* 减小最小宽度 */
		text-align: center; /* 文本居中 */
		flex: 1; /* 按钮平均分配空间 */
		max-width: 140rpx; /* 限制最大宽度 */
		white-space: nowrap; /* 防止文本换行 */
	}

	/* 智能输入按钮样式 */
	.smart-button {
		background-color: #007AFF;
	}

	.smart-button .action-button-text {
		color: #FFFFFF;
	}

	.action-button:last-child {
		margin-right: 0;
	}

	/* 隐藏水平滚动条 */
	.action-bar::-webkit-scrollbar {
		display: none;
	}

	.action-button-text {
		font-size: 24rpx;
		color: #333333;
		white-space: nowrap; /* 防止文本换行 */
		overflow: hidden; /* 隐藏溢出内容 */
		text-overflow: ellipsis; /* 文本溢出显示省略号 */
	}

	/* 操作按钮行样式 */
	.action-bar {
		display: flex;
		justify-content: space-between; /* 均匀分布按钮 */
		margin-bottom: 20rpx;
		flex-wrap: nowrap; /* 防止按钮换行 */
		padding: 0 10rpx; /* 添加左右内边距，与搜索框对齐 */
		overflow-x: auto; /* 如果空间不足，允许水平滚动 */
	}

	/* 筛选标签样式 */
	.active-filters {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 20rpx;
	}

	.filter-tag {
		background-color: #E0F0FF;
		border-radius: 8rpx;
		padding: 6rpx 10rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		display: flex;
		align-items: center;
	}

	.filter-tag-text {
		font-size: 24rpx;
		color: #007AFF;
	}

	.filter-tag-remove {
		font-size: 28rpx;
		color: #007AFF;
		margin-left: 10rpx;
		width: 30rpx;
		height: 30rpx;
		text-align: center;
		line-height: 30rpx;
	}

	.clear-all-filters {
		font-size: 24rpx;
		color: #FF6B6B;
		margin-left: 10rpx;
		padding: 6rpx 10rpx;
	}

	/* 批量操作样式 */
	.table-batch-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		background-color: #F0F0F0;
		border-bottom: 1rpx solid #EEEEEE;
		width: 100%;
	}

	.batch-select-all {
		display: flex;
		align-items: center;
	}

	.batch-select-text {
		font-size: 28rpx;
		margin-left: 10rpx;
	}

	.batch-actions {
		display: flex;
		align-items: center;
	}

	.batch-action-text {
		font-size: 28rpx;
		color: #FF6B6B;
		margin-right: 20rpx;
	}

	.batch-action-cancel {
		font-size: 28rpx;
		color: #999999;
	}

	.checkbox {
		width: 36rpx;
		height: 36rpx;
		border-radius: 4rpx;
		border: 1rpx solid #CCCCCC;
		background-color: #FFFFFF;
	}

	.checkbox-selected {
		background-color: #007AFF;
		border-color: #007AFF;
		position: relative;
	}

	.checkbox-selected::after {
		content: '';
		position: absolute;
		width: 20rpx;
		height: 10rpx;
		border-left: 3rpx solid #FFFFFF;
		border-bottom: 3rpx solid #FFFFFF;
		transform: rotate(-45deg);
		top: 8rpx;
		left: 6rpx;
	}

	.table-cell-checkbox {
		width: 60rpx;
		flex: none;
	}

	.table-cell {
		min-width: 200rpx;
		padding: 20rpx;
		border-right: 1rpx solid #EEEEEE;
	}

	/* 特定表格列的样式 */
	.column-filename {
		min-width: 150rpx;
		max-width: 200rpx;
	}

	.column-articletype {
		min-width: 120rpx;
		max-width: 150rpx;
	}

	.column-articlecontent {
		min-width: 400rpx;
		width: auto;
		flex: 1;
	}

	.column-keywords {
		min-width: 150rpx;
		max-width: 200rpx;
	}

	.column-category {
		min-width: 150rpx;
		max-width: 200rpx;
	}

	.column-docnumber {
		min-width: 200rpx;
		max-width: 250rpx;
	}

	.column-publishunit {
		min-width: 200rpx;
		max-width: 300rpx;
	}

	/* 项目表列样式 */
	.column-projectname {
		min-width: 300rpx;
		width: auto;
		flex: 1;
	}

	.column-legalperson {
		min-width: 200rpx;
		max-width: 300rpx;
	}

	.column-constructionlocation {
		min-width: 200rpx;
		max-width: 300rpx;
	}

	.column-constructionscale {
		min-width: 400rpx;
		width: auto;
		flex: 2;
	}

	/* 子项目表列样式 */
	.column-subprojectname {
		min-width: 120rpx;
		max-width: 150rpx;
	}

	.column-constructionunit {
		min-width: 100rpx;
		max-width: 130rpx;
	}

	.column-agentunit {
		min-width: 80rpx;
		max-width: 100rpx;
	}

	.column-surveyunit {
		min-width: 70rpx;
		max-width: 90rpx;
	}

	.column-designunit {
		min-width: 70rpx;
		max-width: 90rpx;
	}

	.column-supervisionunit {
		min-width: 80rpx;
		max-width: 100rpx;
	}

	.column-constructorunit {
		min-width: 100rpx;
		max-width: 130rpx;
	}

	.column-projectdescription {
		min-width: 120rpx;
		width: auto;
		flex: 1;
	}

	.table-cell:last-child {
		border-right: none;
	}

	.table-header-text {
		font-size: 26rpx;
		color: #333333;
	}

	.table-cell-text {
		font-size: 26rpx;
		color: #666666;
		display: block;
		width: 100%;
	}

	/* 移除了.table-body样式，因为我们现在使用单一滚动视图 */

	.fab-button {
		position: fixed;
		right: 30rpx;
		bottom: 30rpx;
		width: 100rpx;
		height: 100rpx;
		background-color: #007AFF;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
	}

	.fab-icon {
		color: #FFFFFF;
		font-size: 60rpx;
		font-weight: bold;
	}
</style>
