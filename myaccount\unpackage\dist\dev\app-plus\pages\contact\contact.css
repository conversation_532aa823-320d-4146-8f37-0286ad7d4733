/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  min-height: 100vh;
  background: #f5f5f5;
}
.header {
  background: #4a90e2;
  padding: 30px 16px;
  color: white;
}
.header .header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding-top: 15px;
}
.header .title {
  color: white;
  font-size: 20px;
  font-weight: 500;
}
.contact-list {
  margin: 12px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.contact-item {
  padding: 16px;
  border-bottom: 1px solid #eee;
}
.contact-item:last-child {
  border-bottom: none;
}
.contact-item .contact-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}
.contact-item .contact-value {
  font-size: 16px;
  color: #4a90e2;
}
.contact-item .qr-code {
  width: 200px;
  height: 200px;
  display: block;
  margin: 0 auto;
}
.contact-item .user-guide {
  margin-top: 10px;
}
.contact-item .guide-section {
  margin-bottom: 16px;
}
.contact-item .guide-section:last-child {
  margin-bottom: 0;
}
.contact-item .guide-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  display: block;
}
.contact-item .guide-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  display: block;
}
.contact-item .guide-list {
  display: flex;
  flex-direction: column;
}
.contact-item .guide-item {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 6px;
  padding-left: 4px;
}
.contact-item .guide-item:last-child {
  margin-bottom: 0;
}