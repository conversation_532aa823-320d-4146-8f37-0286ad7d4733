<template>
  <view class="container">
    <view class="header">
      <view class="header-content">
        <view class="start-date">始于:{{ earliestDate || '暂无记录' }}</view>
        <image src="/static/logo.jpg" mode="aspectFit" class="logo"></image>
        <text class="title">{{ store.state.user?.username || '未登录' }}的账本</text>
        <view class="header-buttons">
          <view class="header-button" @tap="goToDetails">明细</view>
          <view class="header-button" @tap="goToSettings">设置</view>
        </view>
      </view>
    </view>

    <scroll-view scroll-y class="expense-list">
      <!-- 表头 -->
      <view class="expense-row header-row">
        <view class="cell category-cell">分类</view>
        <view class="cell target-cell">标的</view>
        <view class="cell amount-cell">支出</view>
        <view class="cell total-cell">总额</view>
        <view class="cell percentage-cell">占比</view>
      </view>

      <!-- 总支出行 -->
      <view class="expense-row total-row">
        <view class="cell category-cell">总支出</view>
        <view class="cell target-cell">-</view>
        <view class="cell amount-cell">-</view>
        <view class="cell total-cell">{{ calculateTotalAmount().toFixed(2) }}</view>
        <view class="cell percentage-cell">100%</view>
      </view>

      <view class="expense-content">
        <!-- 分类数据 -->
        <template v-if="categories && categories.length">
          <!-- 一级分类 -->
          <view v-for="(category, index) in categories" :key="category.category_id" class="category-group">
            <view class="expense-row level-1">
              <view class="cell category-cell">{{ (index + 1) + '. ' + category.name }}</view>
              <view class="cell target-cell">-</view>
              <view class="cell amount-cell">-</view>
              <view class="cell total-cell">{{ calculateCategoryTotal(category).toFixed(2) }}</view>
              <view class="cell percentage-cell">{{ calculatePercentage(calculateCategoryTotal(category)) }}%</view>
            </view>

            <!-- 二级分类 -->
            <template v-if="category.children && category.children.length">
              <view v-for="(subCategory, subIndex) in category.children" :key="subCategory.category_id" class="category-group">
                <view class="expense-row level-2">
                  <view class="cell category-cell">{{ (index + 1) + '.' + (subIndex + 1) + ' ' + subCategory.name }}</view>
                  <view class="cell target-cell">-</view>
                  <view class="cell amount-cell">-</view>
                  <view class="cell total-cell">{{ calculateCategoryTotal(subCategory).toFixed(2) }}</view>
                  <view class="cell percentage-cell">{{ calculatePercentage(calculateCategoryTotal(subCategory)) }}%</view>
                </view>

                <!-- 三级分类 -->
                <template v-if="subCategory.children && subCategory.children.length">
                  <view v-for="(item, itemIndex) in subCategory.children" :key="item.category_id" class="expense-row level-3">
                    <view class="cell category-cell">{{ item.name }}</view>
                    <view class="cell target-cell">
                      <input
                        type="text"
                        :value="currentInputs.get(item.category_id)?.target || ''"
                        @input="e => handleTargetInput(item.category_id, e.detail.value)"
                        class="input-field"
                        placeholder="输入标的"
                      />
                    </view>
                    <view class="cell amount-cell">
                      <input
                        type="digit"
                        :value="currentInputs.get(item.category_id)?.amount || ''"
                        @input="e => handleAmountInput(item.category_id, e.detail.value)"
                        class="input-field"
                        placeholder="输入金额"
                      />
                    </view>
                    <view class="cell total-cell">{{ (tempTotals.get(item.category_id) || item.total_amount || 0).toFixed(2) }}</view>
                    <view class="cell percentage-cell">{{ calculatePercentage(tempTotals.get(item.category_id) || item.total_amount || 0) }}%</view>
                  </view>
                </template>
              </view>
            </template>
          </view>
        </template>
      </view>
    </scroll-view>

    <view class="footer">
      <view class="footer-content">
        <view class="tip-text">提示：可输入负数对冲错误记录</view>
        <view class="button-row">
          <button class="ai-input-button" @tap="showAiInputDialog">智能输入</button>
          <button class="save-button" @tap="saveExpenses" :disabled="isLoading">
            {{ isLoading ? '保存中...' : '保存记录' }}
          </button>
        </view>
      </view>
    </view>

    <!-- 智能输入弹窗 -->
    <view class="ai-input-modal" v-if="showModal">
      <view class="modal-mask" @tap="closeModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">智能输入</text>
          <text class="modal-close" @tap="closeModal">×</text>
        </view>
        <view class="modal-body">
          <textarea
            class="ai-input-textarea"
            v-model="aiInputText"
            placeholder="请输入消费记录，例如：今天在超市买了水果花了50元，在餐厅吃饭花了100元"
          ></textarea>
        </view>
        <view class="modal-footer">
          <button class="modal-button cancel" @tap="closeModal">取消</button>
          <button class="modal-button confirm" @tap="handleConfirm" :disabled="isProcessing">
            {{ isProcessing ? '处理中...' : '确定' }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { dbService } from '../../utils/db'

export default {
  setup() {
    const store = useStore()
    const isLoading = ref(false)
    const currentInputs = ref(new Map())
    const tempTotals = ref(new Map())
    const isInitialized = ref(false)
    const earliestDate = ref(null)
    const showModal = ref(false)
    const isProcessing = ref(false)
    const aiInputText = ref('')

    // 使用computed获取Vuex中的分类数据
    const categories = computed(() => {
      console.log('Computing categories from store:', store.state.categories)
      return store.state.categories
    })

    // 处理退出登录
    const handleLogout = async () => {
      try {
        isLoading.value = true
        await store.dispatch('logout')
        uni.navigateTo({
          url: '/pages/login/login'
        })
      } catch (error) {
        uni.showToast({
          title: '退出失败，请重试',
          icon: 'none'
        })
      } finally {
        isLoading.value = false
      }
    }

    const goToDetails = () => {
      uni.navigateTo({
        url: '/pages/details/details'
      })
    }

    const goToSettings = () => {
      console.log('点击设置按钮')
      uni.navigateTo({
        url: '/pages/settings/settings',
        success: () => {
          console.log('跳转成功')
        },
        fail: (err) => {
          console.error('跳转失败:', err)
        }
      })
    }

    // 计算每个分类的临时总额
    const calculateTempTotal = (categoryId) => {
      const input = currentInputs.value.get(categoryId)
      const category = findCategoryById(categoryId)
      if (!category) return 0

      const inputAmount = input?.amount ? parseFloat(input.amount) : 0
      return (category.total_amount || 0) + inputAmount
    }

    // 计算分类的总额（包括子分类）
    const calculateCategoryTotal = (category) => {
      if (!category.children || category.children.length === 0) {
        const tempTotal = tempTotals.value.get(category.category_id)
        return tempTotal !== undefined ? tempTotal : (category.total_amount || 0)
      }

      return category.children.reduce((total, child) => {
        return total + calculateCategoryTotal(child)
      }, 0)
    }

    // 计算分类的占比
    const calculatePercentage = (amount) => {
      const total = calculateTotalAmount()
      return total > 0 ? ((amount / total) * 100).toFixed(2) : '0'
    }

    // 计算总支出
    const calculateTotalAmount = () => {
      if (!categories.value || categories.value.length === 0) return 0
      return categories.value.reduce((total, category) => {
        return total + calculateCategoryTotal(category)
      }, 0)
    }

    // 处理金额输入
    const handleAmountInput = (categoryId, value) => {
      console.log('Handling amount input:', { categoryId, value })
      const input = currentInputs.value.get(categoryId) || { target: '', amount: '' }
      input.amount = value
      currentInputs.value.set(categoryId, input)

      // 更新临时总额
      const tempTotal = calculateTempTotal(categoryId)
      tempTotals.value.set(categoryId, tempTotal)
    }

    // 处理标的输入
    const handleTargetInput = (categoryId, value) => {
      console.log('Handling target input:', { categoryId, value })
      const input = currentInputs.value.get(categoryId) || { target: '', amount: '' }
      input.target = value
      currentInputs.value.set(categoryId, input)
    }

    // 根据ID查找分类
    const findCategoryById = (categoryId) => {
      const searchInCategories = (categories) => {
        for (const category of categories) {
          if (category.category_id === categoryId) return category
          if (category.children && category.children.length > 0) {
            const found = searchInCategories(category.children)
            if (found) return found
          }
        }
        return null
      }
      return searchInCategories(categories.value)
    }

    // 保存支出记录
    const saveExpenses = async () => {
      const records = []
      currentInputs.value.forEach((input, categoryId) => {
        if (input.amount && parseFloat(input.amount) !== 0) {
          records.push({
            category_id: categoryId,
            target: input.target || '',
            amount: parseFloat(input.amount)
          })
        }
      })

      if (records.length === 0) {
        uni.showToast({
          title: '请至少输入一条支出记录',
          icon: 'none'
        })
        return
      }

      isLoading.value = true
      try {
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')
        const date = `${year}-${month}-${day}`

        const result = await store.dispatch('saveExpense', {
          date,
          records
        })

        if (result) {
          currentInputs.value.clear()
          tempTotals.value.clear()
          await fetchEarliestDate()

          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('保存支出失败:', error)
        uni.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        })
      } finally {
        isLoading.value = false
      }
    }

    // 获取最早支出日期
    const fetchEarliestDate = async () => {
      try {
        const result = await dbService.selectSql(
          'SELECT MIN(record_date) as earliest_date FROM expenses WHERE user_id = ?',
          [store.state.user.user_id]
        )

        if (result && result.length > 0 && result[0].earliest_date) {
          const date = new Date(result[0].earliest_date)
          const year = date.getFullYear()
          const month = date.getMonth() + 1
          const day = date.getDate()
          earliestDate.value = `${year}年${month}月${day}日`
        } else {
          earliestDate.value = '暂无记录'
        }
      } catch (error) {
        console.error('获取最早日期失败:', error)
        earliestDate.value = '暂无记录'
      }
    }

    // 初始化数据
    const initializeData = async () => {
      if (isInitialized.value) return

      try {
        console.log('开始获取分类数据...')
        const result = await store.dispatch('fetchCategories')
        console.log('获取到的分类数据:', result)

        if (result && result.length > 0) {
          console.log('构建的分类树:', result)
          store.commit('updateCategories', result)
          console.log('分类数据已更新到store')
        } else {
          console.log('未获取到分类数据')
        }
        isInitialized.value = true
      } catch (error) {
        console.error('获取分类数据失败:', error)
      }
    }

    // 显示智能输入弹窗
    const showAiInputDialog = () => {
      showModal.value = true
      aiInputText.value = ''
    }

    // 关闭弹窗
    const closeModal = () => {
      if (!isProcessing.value) {
        showModal.value = false
        aiInputText.value = ''
      }
    }

    // 处理确认按钮点击
    const handleConfirm = async () => {
      if (!aiInputText.value.trim()) {
        uni.showToast({
          title: '请输入消费记录',
          icon: 'none'
        })
        return
      }

      isProcessing.value = true
      try {
        await handleAiInput()
        showModal.value = false
        aiInputText.value = ''
      } catch (error) {
        console.error('处理失败:', error)
      } finally {
        isProcessing.value = false
      }
    }

    // 获取所有三级分类名称
    const getAllThirdLevelCategories = () => {
      const thirdLevelCategories = []
      const traverse = (categories) => {
        for (const category of categories) {
          if (category.children && category.children.length > 0) {
            traverse(category.children)
          } else {
            thirdLevelCategories.push(category.name)
          }
        }
      }
      traverse(categories.value)
      return thirdLevelCategories
    }

    // 处理智能输入
    const handleAiInput = async () => {
      if (!aiInputText.value.trim()) {
        uni.showToast({
          title: '请输入消费记录',
          icon: 'none'
        })
        return
      }

      // 获取所有三级分类名称
      const availableCategories = getAllThirdLevelCategories()
      console.log('可用的三级分类:', availableCategories)

      try {
        // 调用 deepseek API
        const response = await uni.request({
          url: 'https://api.deepseek.com/v1/chat/completions',
          method: 'POST',
          data: {
            messages: [
              {
                role: "system",
                content: `你是一个专业的消费记录分析助手。请分析用户输入的消费记录，并返回一个JSON数组，每个元素包含以下字段：
{
  "category_id": "分类ID",
  "target": "消费标的",
  "amount": 金额
}

可用的分类ID包括：
${availableCategories.map(cat => `'${cat}'`).join(', ')}

非常重要的要求：
1. 只返回JSON数组，不要包含其他文字
2. 【特别重要】当用户输入多个同类消费时（如"早饭20，午饭30，晚饭30"），你必须将它们合并为一个元素，将金额相加（如80），并将消费标的合并（如"早饭,午饭,晚饭"）
3. 【特别重要】如果多个消费项目属于同一个分类ID，必须将它们合并为一个元素，将金额相加
4. 金额必须是数字类型，可以是负数（表示对冲错误记录），不要包含货币符号或单位
5. 分类ID必须从上述列表中选择，不要创建新的分类ID
6. 如果无法确定具体分类，使用列表中的最后一个分类作为默认分类

示例输入："早饭20，午饭30，晚饭30"
正确的输出（假设这些都属于"餐饮"分类）：
[{"category_id":"餐饮","target":"早饭,午饭,晚饭","amount":80}]

错误的输出（不要这样做）：
[{"category_id":"餐饮","target":"早饭","amount":20},{"category_id":"餐饮","target":"午饭","amount":30},{"category_id":"餐饮","target":"晚饭","amount":30}]`
              },
              {
                role: "user",
                content: aiInputText.value
              }
            ],
            model: "deepseek-chat",
            temperature: 0.2
          },
          header: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer sk-38ff2a6b9f754b059fa42839d5a4b426'
          }
        })

        if (response.statusCode === 200 && response.data) {
          try {
            const responseText = response.data.choices[0].message.content
            // 清理响应文本，确保它是有效的 JSON
            const cleanResponseText = responseText.replace(/```json\n|\n```/g, '').trim()
            console.log('API 返回的原始数据:', cleanResponseText)

            let parsedResponse
            try {
              parsedResponse = JSON.parse(cleanResponseText)
            } catch (e) {
              // 如果解析失败，尝试提取 JSON 部分
              const jsonMatch = cleanResponseText.match(/\[.*\]/)
              if (jsonMatch) {
                parsedResponse = JSON.parse(jsonMatch[0])
              } else {
                throw new Error('无法解析返回的 JSON 数据')
              }
            }

            if (Array.isArray(parsedResponse)) {
              // 验证每个条目是否包含必要的字段
              const validEntries = parsedResponse.filter(entry =>
                entry.category_id &&
                entry.target &&
                typeof entry.amount === 'number' &&
                availableCategories.includes(entry.category_id) // 验证分类ID是否有效
              )

              if (validEntries.length > 0) {
                // 自动填充数据
                fillExpenseData(validEntries)
                return true // 表示处理成功
              } else {
                throw new Error('API 返回的数据格式不正确：缺少必要字段或分类ID无效')
              }
            } else {
              throw new Error('API 返回的数据不是数组格式')
            }
          } catch (parseError) {
            console.error('JSON 解析错误:', parseError)
            throw new Error('API 返回数据格式不正确')
          }
        } else {
          throw new Error(`API 请求失败: ${response.statusCode}`)
        }
      } catch (error) {
        console.error('智能输入处理失败:', error)
        uni.showToast({
          title: error.message || '处理失败，请重试',
          icon: 'none',
          duration: 3000
        })
        throw error // 重新抛出错误，让调用者知道处理失败
      }
    }

    // 自动填充数据
    const fillExpenseData = (categories) => {
      // 先将相同分类的数据合并
      const mergedData = new Map()

      // 遍历所有分类数据，合并相同分类的金额
      categories.forEach(item => {
        const targetCategory = findCategoryByName(item.category_id)
        if (!targetCategory) {
          console.warn(`未找到匹配的分类: ${item.category_id}`)
          return
        }

        const categoryId = targetCategory.category_id
        if (mergedData.has(categoryId)) {
          // 如果已经有该分类的数据，则将金额相加
          const existingData = mergedData.get(categoryId)
          existingData.amount += item.amount
          // 合并消费标的，用逗号分隔
          if (existingData.target !== item.target) {
            existingData.target = `${existingData.target}, ${item.target}`
          }
        } else {
          // 如果还没有该分类的数据，则新建一个
          mergedData.set(categoryId, {
            categoryId: categoryId,
            target: item.target,
            amount: item.amount
          })
        }
      })

      // 将合并后的数据填充到输入框
      mergedData.forEach((mergedItem, categoryId) => {
        // 更新输入框数据
        const input = currentInputs.value.get(categoryId) || { target: '', amount: '' }

        // 如果输入框已有数据，则将金额相加
        if (input.amount) {
          const currentAmount = parseFloat(input.amount) || 0
          mergedItem.amount += currentAmount

          // 如果已有消费标的且不同，则合并
          if (input.target && input.target !== mergedItem.target) {
            mergedItem.target = `${input.target}, ${mergedItem.target}`
          }
        }

        input.target = mergedItem.target
        input.amount = mergedItem.amount.toString()
        currentInputs.value.set(categoryId, input)

        // 更新临时总额
        const tempTotal = calculateTempTotal(categoryId)
        tempTotals.value.set(categoryId, tempTotal)

        // 查找分类名称用于日志输出
        const category = findCategoryById(categoryId)
        const categoryName = category ? category.name : categoryId
        console.log(`已填充数据到分类 ${categoryName}:`, input)
      })
    }

    // 根据分类名称查找分类
    const findCategoryByName = (categoryName) => {
      const searchInCategories = (categories) => {
        for (const category of categories) {
          // 如果是三级分类，直接比较名称
          if (!category.children || category.children.length === 0) {
            if (category.name === categoryName) {
              return category
            }
          }
          // 如果是二级分类，搜索其子分类
          else if (category.children) {
            for (const subCategory of category.children) {
              // 如果是三级分类，直接比较名称
              if (!subCategory.children || subCategory.children.length === 0) {
                if (subCategory.name === categoryName) {
                  return subCategory
                }
              }
              // 如果是二级分类，搜索其子分类
              else if (subCategory.children) {
                for (const item of subCategory.children) {
                  if (item.name === categoryName) {
                    return item
                  }
                }
              }
            }
          }
        }
        return null
      }
      return searchInCategories(categories.value)
    }





    // 页面加载时初始化
    onMounted(async () => {
      console.log('页面加载，开始初始化数据...')
      console.log('当前用户状态:', store.state.user)

      if (!store.state.user) {
        console.log('用户未登录，跳转到登录页面')
        uni.reLaunch({
          url: '/pages/login/login'
        })
        return
      }

      // 设置导航栏标题
      uni.setNavigationBarTitle({
        title: `${store.state.user.username}的账本`
      })

      await Promise.all([
        initializeData(),
        fetchEarliestDate()
      ])
      console.log('数据初始化完成')
    })

    return {
      store,
      categories,
      isLoading,
      currentInputs,
      tempTotals,
      calculateCategoryTotal,
      calculatePercentage,
      calculateTotalAmount,
      handleAmountInput,
      handleTargetInput,
      saveExpenses,
      handleLogout,
      goToDetails,
      goToSettings,
      earliestDate,
      showModal,
      isProcessing,
      aiInputText,
      showAiInputDialog,
      closeModal,
      handleConfirm
    }
  }
}
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.header {
  background: #4a90e2;
  padding: 8px;
  color: white;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 960px;
    margin: 0 auto;
    padding: 0 8px;
    position: relative;
  }

  .start-date {
    color: white;
    font-size: 14px;
    min-width: 140px;
  }

  .logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .title {
    color: white;
    font-size: 16px;
    font-weight: 500;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 36px;
    white-space: nowrap;
  }

  .header-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 140px;
    gap: 10px;
  }

  .header-button {
    padding: 4px 12px;
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.25);
    }
  }
}

.expense-list {
  flex: 1;
  background: white;
  margin: 4px 2px;
  max-width: 960px;
  width: calc(100% - 4px);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  height: calc(100vh - 48px);
  overflow: hidden;
}

.header-row {
  background: #4a90e2;
  color: white;
  font-weight: 500;
  position: sticky;
  top: 0;
  z-index: 2;
  border-radius: 8px 8px 0 0;
  margin-bottom: 1px;
  padding: 0 4px;

  .cell {
    padding: 8px 2px;
    font-size: 13px;
    font-weight: 500;
  }
}

.total-row {
  background-color: #f8f9fa;
  font-weight: 600;
  border-bottom: 2px solid #eee;
  margin-bottom: 2px;
  padding: 0 4px;

  .cell {
    padding: 6px 2px;
    font-size: 15px;
    color: #333;
  }
}

.expense-content {
  padding: 0 4px;
}

.expense-row {
  display: flex;
  border-bottom: 1px solid #eee;
  transition: background-color 0.3s ease;
  align-items: center;

  &:hover {
    background-color: #f8f9fa;
  }
}

.cell {
  padding: 4px 1px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
}

.category-cell {
  flex: 3;
  padding-left: 4px;
}

.target-cell {
  flex: 2.1;
  padding: 1px 4px 1px 8px;
}

.amount-cell {
  flex: 2.1;
  padding: 1px 2px 1px 8px;
  margin-right: -4px;
}

.total-cell {
  flex: 1.6;
  text-align: right;
  padding-right: 4px;
  padding-left: 0;
  min-width: 80px;
  font-size: 12px;
}

.percentage-cell {
  flex: 0.8;
  text-align: right;
  padding-right: 4px;
  min-width: 50px;
  font-size: 12px;
}

.level-1 {
  background-color: #f5f5f5;
  font-weight: 600;
  font-size: 13px;
  min-height: 32px;

  .category-cell {
    padding-left: 4px;
    padding-right: 4px;
  }
}

.level-2 {
  background-color: #fafafa;
  font-size: 12px;
  min-height: 30px;

  .category-cell {
    padding-left: 10px;
  }
}

.level-3 {
  font-size: 12px;
  min-height: 28px;

  .category-cell {
    padding-left: 15px;
  }

  .target-cell {
    .input-field {
      margin: 0;
      width: 99%;
      box-sizing: border-box;
      text-align: left;
      padding: 0 4px 0 4px;
    }
  }

  .amount-cell {
    .input-field {
      margin: 0;
      width: 97%;
      box-sizing: border-box;
      text-align: left;
      padding: 0 4px 0 4px;
    }
  }

  .input-field {
    height: 24px;
    font-size: 11px;
    line-height: 24px;
    min-width: 60px;
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    transition: all 0.3s ease;
    display: block;
    background: #fff;

    &:focus {
      border-color: #4a90e2;
      outline: none;
      box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.1);
    }

    &::placeholder {
      color: #999;
      font-size: 11px;
    }
  }
}

.footer {
  padding: 12px;
  background: white;
  border-top: 1px solid #eee;

  .footer-content {
    max-width: 960px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .tip-text {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
      width: 100%;
      text-align: center;
    }

    .button-row {
      display: flex;
      justify-content: center;
      gap: 12px;
      width: 100%;
    }
  }

  .ai-input-button {
    background: #67c23a;
    color: white;
    border: none;
    padding: 8px 24px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #5daf34;
    }
  }

  .save-button {
    background: #4a90e2;
    color: white;
    border: none;
    padding: 8px 24px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #357abd;
    }

    &:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  }
}

.ai-input-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .modal-content {
    position: relative;
    width: 90%;
    max-width: 600px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    z-index: 1;
    margin: 0 auto;
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;

    .modal-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .modal-close {
      font-size: 24px;
      color: #999;
      cursor: pointer;
      padding: 4px;
    }
  }

  .modal-body {
    padding: 20px;
    box-sizing: border-box;

    .ai-input-textarea {
      width: 100%;
      height: 200px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding: 12px;
      font-size: 14px;
      line-height: 1.5;
      background-color: #fff;
      box-sizing: border-box;
      resize: none;

      &:focus {
        border-color: #4a90e2;
        outline: none;
      }

      &::placeholder {
        color: #999;
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 12px 20px;
    border-top: 1px solid #eee;
    gap: 12px;
    box-sizing: border-box;

    .modal-button {
      font-size: 14px;
      padding: 8px 24px;
      border-radius: 4px;
      transition: background-color 0.3s ease;
      white-space: nowrap;
      min-width: 80px;
      text-align: center;

      &.cancel {
        background-color: #f5f5f5;
        color: #606266;
        border: 1px solid #dcdfe6;

        &:active {
          background-color: #e9e9e9;
        }
      }

      &.confirm {
        background-color: #4a90e2;
        color: #fff;
        border: none;

        &:active {
          background-color: #357abd;
        }

        &:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>
