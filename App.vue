<script>
	import { openDatabase, initSystemTables, migrateSystemTables, closeDatabase } from '@/utils/sqlite.js';

	export default {
		onLaunch: async function() {
			console.log('App Launch');

			// 初始化数据库
			try {
				await openDatabase();
				await initSystemTables();

				// 确保系统表结构是最新的
				await migrateSystemTables();
				console.log('数据库初始化成功，表的创建将在用户界面中处理');
			} catch (e) {
				console.error('数据库初始化失败', e);
			}
		},
		onShow: function() {
			console.log('App Show');
		},
		onHide: function() {
			console.log('App Hide');
		},
		onUnload: async function() {
			// 关闭数据库
			try {
				await closeDatabase();
			} catch (e) {
				console.error('关闭数据库失败', e);
			}
		}
	}
</script>

<style>
	/* 全局CSS */

	/* 重置样式 */
	page {
		font-size: 28rpx;
		color: #333333;
		background-color: #F5F5F5;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
	}

	/* 通用样式 */
	.container {
		padding: 30rpx;
	}

	.flex-row {
		display: flex;
		flex-direction: row;
	}

	.flex-column {
		display: flex;
		flex-direction: column;
	}

	.flex-center {
		justify-content: center;
		align-items: center;
	}

	.flex-between {
		justify-content: space-between;
	}

	.flex-around {
		justify-content: space-around;
	}

	.flex-wrap {
		flex-wrap: wrap;
	}

	.flex-1 {
		flex: 1;
	}

	/* 文本样式 */
	.text-center {
		text-align: center;
	}

	.text-left {
		text-align: left;
	}

	.text-right {
		text-align: right;
	}

	.text-primary {
		color: #007AFF;
	}

	.text-success {
		color: #4CD964;
	}

	.text-warning {
		color: #F0AD4E;
	}

	.text-danger {
		color: #DD524D;
	}

	.text-muted {
		color: #8F8F94;
	}

	.text-ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	/* 边距样式 */
	.m-10 { margin: 10rpx; }
	.m-20 { margin: 20rpx; }
	.m-30 { margin: 30rpx; }

	.mt-10 { margin-top: 10rpx; }
	.mt-20 { margin-top: 20rpx; }
	.mt-30 { margin-top: 30rpx; }

	.mb-10 { margin-bottom: 10rpx; }
	.mb-20 { margin-bottom: 20rpx; }
	.mb-30 { margin-bottom: 30rpx; }

	.ml-10 { margin-left: 10rpx; }
	.ml-20 { margin-left: 20rpx; }
	.ml-30 { margin-left: 30rpx; }

	.mr-10 { margin-right: 10rpx; }
	.mr-20 { margin-right: 20rpx; }
	.mr-30 { margin-right: 30rpx; }

	.p-10 { padding: 10rpx; }
	.p-20 { padding: 20rpx; }
	.p-30 { padding: 30rpx; }

	/* 按钮样式 */
	.btn {
		padding: 20rpx 30rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		text-align: center;
	}

	.btn-primary {
		background-color: #007AFF;
		color: #FFFFFF;
	}

	.btn-success {
		background-color: #4CD964;
		color: #FFFFFF;
	}

	.btn-warning {
		background-color: #F0AD4E;
		color: #FFFFFF;
	}

	.btn-danger {
		background-color: #DD524D;
		color: #FFFFFF;
	}

	.btn-default {
		background-color: #F8F8F8;
		color: #333333;
		border: 1rpx solid #DDDDDD;
	}

	/* 卡片样式 */
	.card {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	/* 表单样式 */
	.form-item {
		margin-bottom: 30rpx;
	}

	.form-label {
		font-size: 28rpx;
		margin-bottom: 10rpx;
		display: block;
	}

	.form-input {
		width: 100%;
		height: 80rpx;
		border: 1rpx solid #DDDDDD;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		background-color: #FFFFFF;
	}

	.form-textarea {
		width: 100%;
		height: 200rpx;
		border: 1rpx solid #DDDDDD;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		background-color: #FFFFFF;
	}

	.form-select {
		width: 100%;
		height: 80rpx;
		border: 1rpx solid #DDDDDD;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		background-color: #FFFFFF;
	}

	.form-error {
		color: #DD524D;
		font-size: 24rpx;
		margin-top: 10rpx;
	}
</style>
