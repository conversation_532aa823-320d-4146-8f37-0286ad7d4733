if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global = uni.requireGlobal();
  ArrayBuffer = global.ArrayBuffer;
  Int8Array = global.Int8Array;
  Uint8Array = global.Uint8Array;
  Uint8ClampedArray = global.Uint8ClampedArray;
  Int16Array = global.Int16Array;
  Uint16Array = global.Uint16Array;
  Int32Array = global.Int32Array;
  Uint32Array = global.Uint32Array;
  Float32Array = global.Float32Array;
  Float64Array = global.Float64Array;
  BigInt64Array = global.BigInt64Array;
  BigUint64Array = global.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  const APP_INFO = {
    // 应用名称
    name: "SQLite数据库管理器",
    // 应用版本
    version: "1.1.0",
    // 应用描述
    description: "一个简单易用的SQLite数据库管理工具，可以创建表、定义列、管理数据，支持编辑和删除数据，调整列顺序，以及数据的搜索、筛选、批量操作和导入导出功能",
    // 应用作者
    author: "SQLite数据库管理器团队",
    // 应用主题色
    themeColor: "#007AFF"
  };
  const DB_CONFIG = {
    // 数据库名称
    name: "sqlite_manager.db",
    // 数据库路径
    path: "_doc/sqlite_manager.db",
    // 系统表名
    systemTables: {
      tables: "sys_tables",
      columns: "sys_columns"
    }
  };
  const UI_CONFIG = {
    // 长按触发时间（毫秒）
    longPressThreshold: 800,
    // 表格每页显示的数据条数
    tablePageSize: 50,
    // 表格最大高度
    tableMaxHeight: 800,
    // 搜索防抖时间（毫秒）
    searchDebounceTime: 300
  };
  const EXPORT_FORMATS = [
    {
      label: "CSV",
      value: "csv",
      mimeType: "text/csv",
      extension: ".csv",
      description: "逗号分隔值，可用Excel打开"
    },
    {
      label: "JSON",
      value: "json",
      mimeType: "application/json",
      extension: ".json",
      description: "JavaScript对象表示法"
    }
  ];
  const FILTER_OPERATORS = {
    // 文本类型操作符
    TEXT: [
      { label: "等于", value: "=" },
      { label: "不等于", value: "!=" },
      { label: "包含", value: "包含" },
      { label: "不包含", value: "不包含" },
      { label: "开头是", value: "开头是" },
      { label: "结尾是", value: "结尾是" }
    ],
    // 数值类型操作符
    NUMERIC: [
      { label: "等于", value: "=" },
      { label: "不等于", value: "!=" },
      { label: "大于", value: ">" },
      { label: "小于", value: "<" },
      { label: "大于等于", value: ">=" },
      { label: "小于等于", value: "<=" }
    ],
    // 通用操作符
    COMMON: [
      { label: "等于", value: "=" },
      { label: "不等于", value: "!=" }
    ]
  };
  const HELP_INFO = {
    title: "应用帮助",
    content: 'SQLite数据库管理器使用说明：\n\n1. 点击右下角"+"按钮创建新表\n2. 点击表格项目查看表详情\n3. 在表详情页可以：\n   - 查看列定义\n   - 长按列调整顺序\n   - 查看和管理数据\n   - 长按数据行编辑或删除\n4. 点击表详情页右上角"操作"可以：\n   - 编辑表结构（添加/删除列）\n   - 删除表\n5. 数据管理功能：\n   - 搜索：在搜索框输入关键字\n   - 筛选：点击筛选按钮设置条件\n   - 批量：点击批量按钮进行多选操作\n   - 导入：支持CSV、JSON格式\n   - 导出：支持CSV、JSON格式'
  };
  const DB_NAME = DB_CONFIG.name;
  const DB_PATH = DB_CONFIG.path;
  const TABLE_TABLES$1 = DB_CONFIG.systemTables.tables;
  const TABLE_COLUMNS$1 = DB_CONFIG.systemTables.columns;
  let isDbOpen = false;
  const ERROR_MESSAGES = {
    DB_OPEN_FAILED: "数据库打开失败",
    DB_CLOSE_FAILED: "数据库关闭失败",
    TABLE_CREATE_FAILED: "创建表失败",
    TABLE_DROP_FAILED: "删除表失败",
    COLUMN_ADD_FAILED: "添加列失败",
    COLUMN_DELETE_FAILED: "删除列失败",
    DATA_INSERT_FAILED: "插入数据失败",
    DATA_UPDATE_FAILED: "更新数据失败",
    DATA_DELETE_FAILED: "删除数据失败",
    QUERY_FAILED: "查询失败"
  };
  function openDatabase() {
    return new Promise((resolve, reject) => {
      if (isDbOpen) {
        formatAppLog("log", "at utils/sqlite.js:39", "数据库已经打开，无需重复打开");
        executeSql("PRAGMA foreign_keys = ON").then(() => {
          formatAppLog("log", "at utils/sqlite.js:43", "外键约束已启用");
          resolve(true);
        }).catch((e) => {
          formatAppLog("error", "at utils/sqlite.js:47", "启用外键约束失败", e);
          resolve(true);
        });
        return;
      }
      try {
        plus.sqlite.closeDatabase({
          name: DB_NAME,
          success() {
            formatAppLog("log", "at utils/sqlite.js:58", "预防性关闭数据库成功");
          },
          fail() {
          }
        });
      } catch (e) {
      }
      plus.sqlite.openDatabase({
        name: DB_NAME,
        path: DB_PATH,
        success(e) {
          formatAppLog("log", "at utils/sqlite.js:73", "数据库打开成功");
          isDbOpen = true;
          executeSql("PRAGMA foreign_keys = ON").then(() => {
            formatAppLog("log", "at utils/sqlite.js:79", "外键约束已启用");
            resolve(true);
          }).catch((e2) => {
            formatAppLog("error", "at utils/sqlite.js:83", "启用外键约束失败", e2);
            resolve(true);
          });
        },
        fail(e) {
          formatAppLog("error", "at utils/sqlite.js:88", ERROR_MESSAGES.DB_OPEN_FAILED, e);
          reject(e);
        }
      });
    });
  }
  function closeDatabase() {
    return new Promise((resolve, reject) => {
      if (!isDbOpen) {
        formatAppLog("log", "at utils/sqlite.js:103", "数据库未打开，无需关闭");
        resolve(true);
        return;
      }
      plus.sqlite.closeDatabase({
        name: DB_NAME,
        success(e) {
          formatAppLog("log", "at utils/sqlite.js:111", "数据库关闭成功");
          isDbOpen = false;
          resolve(true);
        },
        fail(e) {
          formatAppLog("error", "at utils/sqlite.js:116", ERROR_MESSAGES.DB_CLOSE_FAILED, e);
          reject(e);
        }
      });
    });
  }
  function executeSql(sql) {
    return new Promise((resolve, reject) => {
      plus.sqlite.executeSql({
        name: DB_NAME,
        sql,
        success(e) {
          resolve(e);
        },
        fail(e) {
          formatAppLog("error", "at utils/sqlite.js:137", "SQL执行失败", sql, e);
          reject(e);
        }
      });
    });
  }
  function selectSql(sql) {
    return new Promise((resolve, reject) => {
      plus.sqlite.selectSql({
        name: DB_NAME,
        sql,
        success(e) {
          resolve(e);
        },
        fail(e) {
          formatAppLog("error", "at utils/sqlite.js:158", "SQL查询失败", sql, e);
          reject(e);
        }
      });
    });
  }
  async function initSystemTables() {
    try {
      await executeSql(`
      CREATE TABLE IF NOT EXISTS ${TABLE_TABLES$1} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        created_at INTEGER NOT NULL
      )
    `);
      await executeSql(`
      CREATE TABLE IF NOT EXISTS ${TABLE_COLUMNS$1} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        is_primary_key INTEGER DEFAULT 0,
        is_not_null INTEGER DEFAULT 0,
        is_unique INTEGER DEFAULT 0,
        is_foreign_key INTEGER DEFAULT 0,
        reference_table_id INTEGER,
        reference_column_id INTEGER,
        order_index INTEGER DEFAULT 0,
        FOREIGN KEY (table_id) REFERENCES ${TABLE_TABLES$1}(id) ON DELETE CASCADE
      )
    `);
      await migrateSystemTables();
      return true;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:204", "初始化系统表失败", e);
      return false;
    }
  }
  async function migrateSystemTables() {
    try {
      formatAppLog("log", "at utils/sqlite.js:215", "开始检查系统表结构...");
      const hasIsHidden = await checkColumnExists(TABLE_COLUMNS$1, "is_hidden");
      if (!hasIsHidden) {
        formatAppLog("log", "at utils/sqlite.js:220", "添加is_hidden列到sys_columns表");
        await executeSql(`ALTER TABLE ${TABLE_COLUMNS$1} ADD COLUMN is_hidden INTEGER DEFAULT 0`);
      }
      const hasOptions = await checkColumnExists(TABLE_COLUMNS$1, "options");
      if (!hasOptions) {
        formatAppLog("log", "at utils/sqlite.js:227", "添加options列到sys_columns表");
        await executeSql(`ALTER TABLE ${TABLE_COLUMNS$1} ADD COLUMN options TEXT`);
      }
      const hasDefaultValue = await checkColumnExists(TABLE_COLUMNS$1, "default_value");
      if (!hasDefaultValue) {
        formatAppLog("log", "at utils/sqlite.js:234", "添加default_value列到sys_columns表");
        await executeSql(`ALTER TABLE ${TABLE_COLUMNS$1} ADD COLUMN default_value TEXT`);
      }
      formatAppLog("log", "at utils/sqlite.js:238", "系统表结构检查完成");
      return true;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:241", "迁移系统表结构失败", e);
      return false;
    }
  }
  async function checkColumnExists(tableName, columnName) {
    try {
      const columns = await selectSql(`PRAGMA table_info(${tableName})`);
      return columns.some((col) => col.name === columnName);
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:258", `检查列 ${columnName} 是否存在失败`, e);
      return false;
    }
  }
  async function getAllTables() {
    try {
      const tables = await selectSql(`SELECT * FROM ${TABLE_TABLES$1} ORDER BY created_at DESC`);
      return tables;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:302", "获取表列表失败", e);
      return [];
    }
  }
  async function getTableColumns(tableId) {
    try {
      const columns = await selectSql(`SELECT * FROM ${TABLE_COLUMNS$1} WHERE table_id = ${tableId} ORDER BY order_index ASC`);
      return columns;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:317", "获取列列表失败", e);
      return [];
    }
  }
  async function createTable(tableName, description, columns) {
    try {
      await executeSql(`
      INSERT INTO ${TABLE_TABLES$1} (name, description, created_at)
      VALUES ('${tableName}', '${description}', ${Date.now()})
    `);
      const tables = await selectSql(`SELECT id FROM ${TABLE_TABLES$1} WHERE name = '${tableName}'`);
      if (!tables || tables.length === 0) {
        throw new Error("创建表失败");
      }
      const tableId = tables[0].id;
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        await executeSql(`
        INSERT INTO ${TABLE_COLUMNS$1} (
          table_id, name, type, is_primary_key, is_not_null, is_unique,
          is_foreign_key, reference_table_id, reference_column_id, order_index
        ) VALUES (
          ${tableId}, '${column.name}', '${column.type}', ${column.isPrimaryKey ? 1 : 0},
          ${column.isNotNull ? 1 : 0}, ${column.isUnique ? 1 : 0}, ${column.isForeignKey ? 1 : 0},
          ${column.referenceTableId || "NULL"}, ${column.referenceColumnId || "NULL"}, ${i}
        )
      `);
      }
      const createTableSql = buildCreateTableSql(tableName, columns);
      await executeSql(createTableSql);
      return tableId;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:366", "创建表失败", e);
      throw e;
    }
  }
  async function dropTable(tableId, tableName) {
    try {
      await executeSql(`DROP TABLE IF EXISTS ${tableName}`);
      await executeSql(`DELETE FROM ${TABLE_TABLES$1} WHERE id = ${tableId}`);
      await executeSql(`DELETE FROM ${TABLE_COLUMNS$1} WHERE table_id = ${tableId}`);
      return true;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:390", "删除表失败", e);
      return false;
    }
  }
  async function dropAllTablesWithName(tableName) {
    try {
      formatAppLog("log", "at utils/sqlite.js:402", `开始删除所有名为 ${tableName} 的表的系统记录`);
      const tables = await selectSql(`SELECT id FROM ${TABLE_TABLES$1} WHERE name = '${tableName}'`);
      formatAppLog("log", "at utils/sqlite.js:406", `找到 ${tables.length} 个名为 ${tableName} 的表`);
      for (const table of tables) {
        await executeSql(`DELETE FROM ${TABLE_COLUMNS$1} WHERE table_id = ${table.id}`);
        formatAppLog("log", "at utils/sqlite.js:412", `已删除表 ${tableName} 的列记录，ID: ${table.id}`);
        await executeSql(`DELETE FROM ${TABLE_TABLES$1} WHERE id = ${table.id}`);
        formatAppLog("log", "at utils/sqlite.js:416", `已删除表 ${tableName} 的系统记录，ID: ${table.id}`);
      }
      formatAppLog("log", "at utils/sqlite.js:420", `保留实际表 ${tableName} 及其数据`);
      return true;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:424", `删除所有名为 ${tableName} 的表的系统记录失败`, e);
      return false;
    }
  }
  function buildCreateTableSql(tableName, columns) {
    let sql = `CREATE TABLE IF NOT EXISTS ${tableName} (`;
    const columnDefs = columns.map((column) => {
      let def = `${column.name} ${column.type}`;
      if (column.isPrimaryKey) {
        def += " PRIMARY KEY";
        if (column.type === "INTEGER") {
          def += " AUTOINCREMENT";
        }
      }
      if (column.isNotNull) {
        def += " NOT NULL";
      }
      if (column.isUnique) {
        def += " UNIQUE";
      }
      if (column.defaultValue) {
        if (column.defaultValue.includes("datetime(") || column.defaultValue.includes("strftime(")) {
          def += ` DEFAULT ${column.defaultValue}`;
        } else {
          def += ` DEFAULT '${column.defaultValue}'`;
        }
      }
      return def;
    });
    sql += columnDefs.join(", ");
    const foreignKeys = columns.filter((column) => column.isForeignKey && column.referenceTableId && column.referenceColumnId);
    if (foreignKeys.length > 0) {
      for (const fk of foreignKeys) {
        sql += `, FOREIGN KEY (${fk.name}) REFERENCES table_${fk.referenceTableId}(column_${fk.referenceColumnId})`;
      }
    }
    sql += ")";
    return sql;
  }
  async function checkFileNameExists(fileName) {
    try {
      const result = await selectSql(`SELECT COUNT(*) as count FROM documents WHERE fileName = '${fileName.replace(/'/g, "''")}'`);
      return result && result.length > 0 && result[0].count > 0;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:496", "检查文件名是否存在失败", e);
      return false;
    }
  }
  async function createDocumentRecord(fileName) {
    try {
      const documentData = {
        fileName,
        category: "待完善",
        documentNumber: "待完善",
        publishDate: "",
        implementDate: "",
        publishingUnit: "待完善"
      };
      formatAppLog("log", "at utils/sqlite.js:518", `为文件名 ${fileName} 创建文档记录，使用默认值:`, documentData);
      return await insertData("documents", documentData);
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:523", "创建文档记录失败", e);
      return false;
    }
  }
  async function insertData(tableName, data) {
    try {
      const insertData2 = { ...data };
      if (tableName === "articles" && insertData2.fileName) {
        const fileNameExists = await checkFileNameExists(insertData2.fileName);
        if (!fileNameExists) {
          formatAppLog("log", "at utils/sqlite.js:543", `文件名 ${insertData2.fileName} 不存在于文档表中，自动创建文档记录`);
          const result2 = await createDocumentRecord(insertData2.fileName);
          if (!result2) {
            formatAppLog("error", "at utils/sqlite.js:546", `为文件名 ${insertData2.fileName} 创建文档记录失败`);
            throw new Error(`为文件名 ${insertData2.fileName} 创建文档记录失败`);
          }
          formatAppLog("log", "at utils/sqlite.js:549", `为文件名 ${insertData2.fileName} 创建文档记录成功，ID: ${result2}`);
        }
      }
      if (tableName === "documents" || tableName === "articles") {
        const tableInfo = await selectSql(`PRAGMA table_info(${tableName})`);
        const hasCreateTime = tableInfo.some((col) => col.name === "createTime");
        const hasUpdateTime = tableInfo.some((col) => col.name === "updateTime");
        if (hasCreateTime && !insertData2.createTime) {
          insertData2.createTime = `datetime('now', 'localtime')`;
        }
        if (hasUpdateTime && !insertData2.updateTime) {
          insertData2.updateTime = `datetime('now', 'localtime')`;
        }
      }
      const columns = Object.keys(insertData2);
      const values = Object.entries(insertData2).map(([key, value]) => {
        if (typeof value === "string" && (value.includes("datetime(") || value.includes("strftime("))) {
          return value;
        } else if (typeof value === "string") {
          return `'${value.replace(/'/g, "''")}'`;
        } else if (value === null || value === void 0) {
          return "NULL";
        } else {
          return value;
        }
      });
      const sql = `INSERT INTO ${tableName} (${columns.join(", ")}) VALUES (${values.join(", ")})`;
      formatAppLog("log", "at utils/sqlite.js:587", `插入数据SQL: ${sql}`);
      await executeSql(sql);
      const result = await selectSql("SELECT last_insert_rowid() as rowid");
      if (result && result.length > 0 && result[0].rowid) {
        formatAppLog("log", "at utils/sqlite.js:593", `数据插入成功，行ID: ${result[0].rowid}`);
        return result[0].rowid;
      }
      return true;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:599", "插入数据失败", e);
      return false;
    }
  }
  async function queryTableData(tableName) {
    try {
      const data = await selectSql(`SELECT rowid as rowid, * FROM ${tableName}`);
      if (data && data.length > 0) {
        const hasRowId = data[0].hasOwnProperty("rowid");
        formatAppLog("log", "at utils/sqlite.js:617", `查询表 ${tableName} 数据，是否包含rowid: ${hasRowId}`);
        if (!hasRowId) {
          formatAppLog("warn", "at utils/sqlite.js:621", `表 ${tableName} 的查询结果缺少rowid，尝试手动添加`);
          try {
            const rowIds = await selectSql(`SELECT ROWID FROM ${tableName}`);
            if (rowIds && rowIds.length === data.length) {
              for (let i = 0; i < data.length; i++) {
                data[i].rowid = rowIds[i].ROWID;
              }
              formatAppLog("log", "at utils/sqlite.js:632", `已手动添加rowid到表 ${tableName} 的数据中`);
            }
          } catch (rowIdError) {
            formatAppLog("error", "at utils/sqlite.js:635", `尝试获取表 ${tableName} 的ROWID失败`, rowIdError);
          }
        }
      }
      return data;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:642", "查询数据失败", e);
      return [];
    }
  }
  async function updateTableRow(tableName, rowId, data) {
    try {
      const updatedData = { ...data };
      if (tableName === "documents" || tableName === "articles") {
        updatedData.updateTime = `datetime('now', 'localtime')`;
      }
      const setClause = Object.entries(updatedData).map(([key, value]) => {
        if (typeof value === "string" && (value.includes("datetime(") || value.includes("strftime("))) {
          return `${key} = ${value}`;
        } else if (typeof value === "string") {
          return `${key} = '${value}'`;
        }
        return `${key} = ${value}`;
      }).join(", ");
      await executeSql(`UPDATE ${tableName} SET ${setClause} WHERE rowid = ${rowId}`);
      formatAppLog("log", "at utils/sqlite.js:680", `更新表 ${tableName} 中的数据，行ID: ${rowId}`);
      return true;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:683", "更新数据失败", e);
      return false;
    }
  }
  async function deleteTableRow(tableName, rowId) {
    try {
      await executeSql(`DELETE FROM ${tableName} WHERE rowid = ${rowId}`);
      return true;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:699", "删除数据失败", e);
      return false;
    }
  }
  async function addColumnToTable(tableId, tableName, column) {
    try {
      const alterTableSql = `ALTER TABLE ${tableName} ADD COLUMN ${column.name} ${column.type}`;
      await executeSql(alterTableSql);
      const columns = await getTableColumns(tableId);
      const maxOrder = columns.length > 0 ? Math.max(...columns.map((c) => c.order_index)) : -1;
      let sql = `
      INSERT INTO ${TABLE_COLUMNS$1} (
        table_id, name, type, is_primary_key, is_not_null, is_unique,
        is_foreign_key, reference_table_id, reference_column_id, order_index
      ) VALUES (
        ${tableId}, '${column.name}', '${column.type}', ${column.isPrimaryKey ? 1 : 0},
        ${column.isNotNull ? 1 : 0}, ${column.isUnique ? 1 : 0}, ${column.isForeignKey ? 1 : 0},
        ${column.referenceTableId || "NULL"}, ${column.referenceColumnId || "NULL"}, ${maxOrder + 1}
      )
    `;
      await executeSql(sql);
      const result = await selectSql(`SELECT id FROM ${TABLE_COLUMNS$1} WHERE table_id = ${tableId} AND name = '${column.name}'`);
      if (result && result.length > 0) {
        const columnId = result[0].id;
        if (column.isHidden !== void 0) {
          await executeSql(`UPDATE ${TABLE_COLUMNS$1} SET is_hidden = ${column.isHidden ? 1 : 0} WHERE id = ${columnId}`);
        }
        if (column.options) {
          await executeSql(`UPDATE ${TABLE_COLUMNS$1} SET options = '${JSON.stringify(column.options)}' WHERE id = ${columnId}`);
        }
        if (column.defaultValue) {
          await executeSql(`UPDATE ${TABLE_COLUMNS$1} SET default_value = ${JSON.stringify(column.defaultValue)} WHERE id = ${columnId}`);
        }
      }
      return true;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:758", "添加列失败", e);
      return false;
    }
  }
  async function deleteColumnFromTable(tableId, tableName, columnId, columnName) {
    var _a;
    try {
      const columns = await getTableColumns(tableId);
      if (columns.length <= 1) {
        throw new Error("表至少需要保留一列");
      }
      const isPrimaryKey = ((_a = columns.find((c) => c.id === columnId)) == null ? void 0 : _a.is_primary_key) === 1;
      if (isPrimaryKey) {
        throw new Error("不能删除主键列");
      }
      const tempTableName = `${tableName}_temp`;
      const remainingColumns = columns.filter((c) => c.id !== columnId);
      let createTempTableSql = `CREATE TABLE ${tempTableName} (`;
      const columnDefs = remainingColumns.map((column) => {
        let def = `${column.name} ${column.type}`;
        if (column.is_primary_key === 1) {
          def += " PRIMARY KEY";
          if (column.type === "INTEGER") {
            def += " AUTOINCREMENT";
          }
        }
        if (column.is_not_null === 1) {
          def += " NOT NULL";
        }
        if (column.is_unique === 1) {
          def += " UNIQUE";
        }
        if (column.default_value) {
          if (column.default_value.includes("datetime(") || column.default_value.includes("strftime(")) {
            def += ` DEFAULT ${column.default_value}`;
          } else {
            def += ` DEFAULT '${column.default_value}'`;
          }
        }
        return def;
      });
      createTempTableSql += columnDefs.join(", ") + ")";
      await executeSql(createTempTableSql);
      const columnNames = remainingColumns.map((c) => c.name).join(", ");
      await executeSql(`INSERT INTO ${tempTableName} SELECT ${columnNames} FROM ${tableName}`);
      await executeSql(`DROP TABLE ${tableName}`);
      await executeSql(`ALTER TABLE ${tempTableName} RENAME TO ${tableName}`);
      await executeSql(`DELETE FROM ${TABLE_COLUMNS$1} WHERE id = ${columnId}`);
      return true;
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:841", "删除列失败", e);
      throw e;
    }
  }
  async function updateColumnsOrder(tableId, columns) {
    try {
      await executeSql("BEGIN TRANSACTION");
      for (const column of columns) {
        await executeSql(`
        UPDATE ${TABLE_COLUMNS$1}
        SET order_index = ${column.order_index}
        WHERE id = ${column.id}
      `);
      }
      await executeSql("COMMIT");
      return true;
    } catch (e) {
      try {
        await executeSql("ROLLBACK");
      } catch (rollbackError) {
        formatAppLog("error", "at utils/sqlite.js:875", "回滚事务失败", rollbackError);
      }
      formatAppLog("error", "at utils/sqlite.js:878", "更新列顺序失败", e);
      return false;
    }
  }
  async function clearDatabase() {
    try {
      formatAppLog("log", "at utils/sqlite.js:890", "开始清空数据库");
      const tables = await selectSql(`SELECT name FROM sqlite_master WHERE type='table' AND name NOT IN ('sqlite_sequence')`);
      formatAppLog("log", "at utils/sqlite.js:894", "数据库中的表:", tables.map((t) => t.name));
      await executeSql("BEGIN TRANSACTION");
      try {
        for (const table of tables) {
          if (table.name.startsWith("sqlite_")) {
            continue;
          }
          await executeSql(`DELETE FROM ${table.name}`);
          formatAppLog("log", "at utils/sqlite.js:909", `已清空表 ${table.name} 中的数据`);
        }
        await executeSql(`DELETE FROM sqlite_sequence`);
        formatAppLog("log", "at utils/sqlite.js:914", "已重置所有表的自增ID");
        await executeSql("COMMIT");
        formatAppLog("log", "at utils/sqlite.js:918", "数据库清空成功（包括文档表和条文表中的数据）");
        await initSystemTables();
        await migrateSystemTables();
        formatAppLog("log", "at utils/sqlite.js:925", "系统表已重新初始化并迁移");
        return true;
      } catch (e) {
        await executeSql("ROLLBACK");
        formatAppLog("error", "at utils/sqlite.js:931", "清空数据库失败，已回滚事务", e);
        throw e;
      }
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:935", "清空数据库失败", e);
      return false;
    }
  }
  async function getForeignKeyData(tableId, columnId) {
    try {
      const tables = await selectSql(`SELECT * FROM ${TABLE_TABLES$1} WHERE id = ${tableId}`);
      if (!tables || tables.length === 0) {
        return [];
      }
      const columns = await selectSql(`SELECT * FROM ${TABLE_COLUMNS$1} WHERE id = ${columnId}`);
      if (!columns || columns.length === 0) {
        return [];
      }
      const refTableName = tables[0].name;
      const refColumnName = columns[0].name;
      const data = await selectSql(`SELECT ${refColumnName} FROM ${refTableName}`);
      return data.map((item) => item[refColumnName]);
    } catch (e) {
      formatAppLog("error", "at utils/sqlite.js:967", "获取外键引用数据失败", e);
      return [];
    }
  }
  function debounce(func, wait = 300) {
    let timeout;
    return function(...args) {
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        func.apply(this, args);
      }, wait);
    };
  }
  function formatFileSize(size) {
    if (size < 1024) {
      return size + " B";
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(2) + " KB";
    } else if (size < 1024 * 1024 * 1024) {
      return (size / (1024 * 1024)).toFixed(2) + " MB";
    } else {
      return (size / (1024 * 1024 * 1024)).toFixed(2) + " GB";
    }
  }
  function showLoading(title = "加载中...", mask = true) {
    uni.showLoading({
      title,
      mask
    });
  }
  function hideLoading() {
    uni.hideLoading();
    setTimeout(() => {
      uni.hideLoading();
    }, 100);
    setTimeout(() => {
      uni.hideLoading();
    }, 300);
  }
  function showToast(title, icon = "none", duration = 2e3) {
    uni.showToast({
      title,
      icon,
      duration
    });
  }
  const TABLE_TABLES = DB_CONFIG.systemTables.tables;
  const TABLE_COLUMNS = DB_CONFIG.systemTables.columns;
  async function initDocumentAndArticleTables(forceRecreate = false) {
    try {
      formatAppLog("log", "at pages/index/init-tables.js:18", "开始初始化所有预置表", forceRecreate ? "(强制重新创建)" : "");
      await registerTablesToSystem(forceRecreate);
      formatAppLog("log", "at pages/index/init-tables.js:23", "所有预置表初始化完成");
      return true;
    } catch (e) {
      formatAppLog("error", "at pages/index/init-tables.js:26", "初始化预置表失败", e);
      return false;
    }
  }
  async function createDocumentTable() {
    try {
      await executeSql(`
      CREATE TABLE IF NOT EXISTS documents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        fileName TEXT NOT NULL UNIQUE,
        category TEXT NOT NULL,
        documentNumber TEXT NOT NULL,
        publishDate TEXT,
        implementDate TEXT,
        publishingUnit TEXT,
        createTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
        updateTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
      )
    `);
      formatAppLog("log", "at pages/index/init-tables.js:51", "文档表创建成功");
      return true;
    } catch (e) {
      formatAppLog("error", "at pages/index/init-tables.js:54", "创建文档表失败", e);
      throw e;
    }
  }
  async function createArticleTable() {
    try {
      await executeSql(`
      CREATE TABLE IF NOT EXISTS articles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        fileName TEXT NOT NULL,
        articleType TEXT NOT NULL,
        articleContent TEXT NOT NULL,
        keywords TEXT,
        createTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
        updateTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
        FOREIGN KEY (fileName) REFERENCES documents(fileName) ON DELETE CASCADE
      )
    `);
      formatAppLog("log", "at pages/index/init-tables.js:78", "条文表创建成功");
      return true;
    } catch (e) {
      formatAppLog("error", "at pages/index/init-tables.js:81", "创建条文表失败", e);
      throw e;
    }
  }
  async function createProjectTable() {
    try {
      await executeSql(`
      CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        projectName TEXT NOT NULL,
        legalPerson TEXT,
        constructionLocation TEXT,
        constructionScale TEXT,
        createTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
        updateTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
      )
    `);
      formatAppLog("log", "at pages/index/init-tables.js:104", "项目表创建成功");
      return true;
    } catch (e) {
      formatAppLog("error", "at pages/index/init-tables.js:107", "创建项目表失败", e);
      throw e;
    }
  }
  async function createSubprojectTable() {
    try {
      await executeSql(`
      CREATE TABLE IF NOT EXISTS subprojects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        projectId INTEGER,
        projectName TEXT NOT NULL,
        subprojectName TEXT NOT NULL,
        constructionLocation TEXT,
        constructionUnit TEXT,
        agentUnit TEXT,
        surveyUnit TEXT,
        designUnit TEXT,
        supervisionUnit TEXT,
        constructorUnit TEXT,
        projectDescription TEXT,
        createTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
        updateTime TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
      )
    `);
      formatAppLog("log", "at pages/index/init-tables.js:137", "子项目表创建成功");
      return true;
    } catch (e) {
      formatAppLog("error", "at pages/index/init-tables.js:140", "创建子项目表失败", e);
      throw e;
    }
  }
  async function registerTablesToSystem(forceRecreate = false) {
    try {
      const documentsExists = await checkTableExistsInSystem("documents");
      const articlesExists = await checkTableExistsInSystem("articles");
      const projectsExists = await checkTableExistsInSystem("projects");
      const subprojectsExists = await checkTableExistsInSystem("subprojects");
      formatAppLog("log", "at pages/index/init-tables.js:157", "检查表是否存在:", {
        documentsExists,
        articlesExists,
        projectsExists,
        subprojectsExists
      });
      if (documentsExists && articlesExists && projectsExists && subprojectsExists && !forceRecreate) {
        formatAppLog("log", "at pages/index/init-tables.js:166", "所有预置表已存在，无需重新创建");
        return true;
      }
      if (!documentsExists || forceRecreate) {
        await dropAllTablesWithName("documents");
        formatAppLog("log", "at pages/index/init-tables.js:174", "已删除所有名为 documents 的表");
        await createDocumentTable();
        formatAppLog("log", "at pages/index/init-tables.js:178", "文档表已重新创建");
        await registerDocumentTable();
        formatAppLog("log", "at pages/index/init-tables.js:182", "文档表已注册到系统表");
      }
      if (!articlesExists || forceRecreate) {
        await dropAllTablesWithName("articles");
        formatAppLog("log", "at pages/index/init-tables.js:189", "已删除所有名为 articles 的表");
        await createArticleTable();
        formatAppLog("log", "at pages/index/init-tables.js:193", "条文表已重新创建");
        await registerArticleTable();
        formatAppLog("log", "at pages/index/init-tables.js:197", "条文表已注册到系统表");
      }
      if (!projectsExists || forceRecreate) {
        await dropAllTablesWithName("projects");
        formatAppLog("log", "at pages/index/init-tables.js:204", "已删除所有名为 projects 的表");
        await createProjectTable();
        formatAppLog("log", "at pages/index/init-tables.js:208", "项目表已重新创建");
        await registerProjectTable();
        formatAppLog("log", "at pages/index/init-tables.js:212", "项目表已注册到系统表");
      }
      if (!subprojectsExists || forceRecreate) {
        await dropAllTablesWithName("subprojects");
        formatAppLog("log", "at pages/index/init-tables.js:219", "已删除所有名为 subprojects 的表");
        await createSubprojectTable();
        formatAppLog("log", "at pages/index/init-tables.js:223", "子项目表已重新创建");
        await registerSubprojectTable();
        formatAppLog("log", "at pages/index/init-tables.js:227", "子项目表已注册到系统表");
      }
      return true;
    } catch (e) {
      formatAppLog("error", "at pages/index/init-tables.js:232", "注册表到系统表失败", e);
      throw e;
    }
  }
  async function checkTableExistsInSystem(tableName) {
    try {
      const result = await selectSql(`SELECT COUNT(*) as count FROM ${TABLE_TABLES} WHERE name = '${tableName}'`);
      return result[0].count > 0;
    } catch (e) {
      formatAppLog("error", "at pages/index/init-tables.js:245", "检查表是否存在失败", e);
      return false;
    }
  }
  async function registerDocumentTable() {
    try {
      await executeSql(`
      INSERT INTO ${TABLE_TABLES} (name, description, created_at)
      VALUES ('documents', '文档主表，存储文件的基本信息', ${Date.now()})
    `);
      const tables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = 'documents'`);
      if (!tables || tables.length === 0) {
        throw new Error("获取文档表ID失败");
      }
      const tableId = tables[0].id;
      const columns = [
        { name: "id", type: "INTEGER", isPrimaryKey: 1, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: true },
        { name: "fileName", type: "TEXT", isPrimaryKey: 0, isNotNull: 1, isUnique: 1, isForeignKey: 0, isHidden: false },
        {
          name: "category",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: false,
          options: ["法律", "法规", "规章", "规范性文件", "标准/规范", "规程", "其他"]
        },
        { name: "documentNumber", type: "TEXT", isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: false },
        { name: "publishDate", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        { name: "implementDate", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        { name: "publishingUnit", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        {
          name: "createTime",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: true,
          defaultValue: "datetime('now', 'localtime')"
        },
        {
          name: "updateTime",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: true,
          defaultValue: "datetime('now', 'localtime')"
        }
      ];
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        let sql = `
        INSERT INTO ${TABLE_COLUMNS} (
          table_id, name, type, is_primary_key, is_not_null, is_unique,
          is_foreign_key, reference_table_id, reference_column_id, order_index
        ) VALUES (
          ${tableId}, '${column.name}', '${column.type}', ${column.isPrimaryKey},
          ${column.isNotNull}, ${column.isUnique}, ${column.isForeignKey},
          NULL, NULL, ${i}
        )
      `;
        await executeSql(sql);
        const result = await selectSql(`SELECT id FROM ${TABLE_COLUMNS} WHERE table_id = ${tableId} AND name = '${column.name}'`);
        if (result && result.length > 0) {
          const columnId = result[0].id;
          if (column.isHidden !== void 0) {
            await executeSql(`UPDATE ${TABLE_COLUMNS} SET is_hidden = ${column.isHidden ? 1 : 0} WHERE id = ${columnId}`);
          }
          if (column.options) {
            const optionsJson = JSON.stringify(column.options);
            formatAppLog("log", "at pages/index/init-tables.js:344", `设置 ${column.name} 的选项:`, optionsJson);
            await executeSql(`UPDATE ${TABLE_COLUMNS} SET options = '${optionsJson}' WHERE id = ${columnId}`);
          }
          if (column.defaultValue) {
            await executeSql(`UPDATE ${TABLE_COLUMNS} SET default_value = ${JSON.stringify(column.defaultValue)} WHERE id = ${columnId}`);
          }
        }
      }
      formatAppLog("log", "at pages/index/init-tables.js:355", "文档表注册成功");
      return true;
    } catch (e) {
      formatAppLog("error", "at pages/index/init-tables.js:358", "注册文档表失败", e);
      throw e;
    }
  }
  async function registerArticleTable() {
    try {
      await executeSql(`
      INSERT INTO ${TABLE_TABLES} (name, description, created_at)
      VALUES ('articles', '条文表，存储文件中的条文内容', ${Date.now()})
    `);
      const tables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = 'articles'`);
      if (!tables || tables.length === 0) {
        throw new Error("获取条文表ID失败");
      }
      const tableId = tables[0].id;
      const documentTables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = 'documents'`);
      let documentTableId = null;
      let fileNameColumnId = null;
      if (documentTables && documentTables.length > 0) {
        documentTableId = documentTables[0].id;
        const fileNameColumns = await selectSql(`
        SELECT id FROM ${TABLE_COLUMNS}
        WHERE table_id = ${documentTableId} AND name = 'fileName'
      `);
        if (fileNameColumns && fileNameColumns.length > 0) {
          fileNameColumnId = fileNameColumns[0].id;
        }
      }
      const columns = [
        { name: "id", type: "INTEGER", isPrimaryKey: 1, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: true },
        {
          name: "fileName",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: documentTableId && fileNameColumnId ? 1 : 0,
          referenceTableId: documentTableId,
          referenceColumnId: fileNameColumnId,
          isHidden: false
        },
        {
          name: "articleType",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: false,
          options: ["主体行为", "设施设备", "物料", "方法措施", "作业环境", "应急管理", "资料管理"]
        },
        { name: "articleContent", type: "TEXT", isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: false },
        { name: "keywords", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        {
          name: "createTime",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: true,
          defaultValue: "datetime('now', 'localtime')"
        },
        {
          name: "updateTime",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: true,
          defaultValue: "datetime('now', 'localtime')"
        }
      ];
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        let sql = `
        INSERT INTO ${TABLE_COLUMNS} (
          table_id, name, type, is_primary_key, is_not_null, is_unique,
          is_foreign_key, reference_table_id, reference_column_id, order_index
        ) VALUES (
          ${tableId}, '${column.name}', '${column.type}', ${column.isPrimaryKey},
          ${column.isNotNull}, ${column.isUnique}, ${column.isForeignKey},
          ${column.referenceTableId || "NULL"}, ${column.referenceColumnId || "NULL"}, ${i}
        )
      `;
        await executeSql(sql);
        const result = await selectSql(`SELECT id FROM ${TABLE_COLUMNS} WHERE table_id = ${tableId} AND name = '${column.name}'`);
        if (result && result.length > 0) {
          const columnId = result[0].id;
          if (column.isHidden !== void 0) {
            await executeSql(`UPDATE ${TABLE_COLUMNS} SET is_hidden = ${column.isHidden ? 1 : 0} WHERE id = ${columnId}`);
          }
          if (column.options) {
            const optionsJson = JSON.stringify(column.options);
            formatAppLog("log", "at pages/index/init-tables.js:453", `设置 ${column.name} 的选项:`, optionsJson);
            await executeSql(`UPDATE ${TABLE_COLUMNS} SET options = '${optionsJson}' WHERE id = ${columnId}`);
          }
          if (column.defaultValue) {
            await executeSql(`UPDATE ${TABLE_COLUMNS} SET default_value = ${JSON.stringify(column.defaultValue)} WHERE id = ${columnId}`);
          }
        }
      }
      formatAppLog("log", "at pages/index/init-tables.js:464", "条文表注册成功");
      return true;
    } catch (e) {
      formatAppLog("error", "at pages/index/init-tables.js:467", "注册条文表失败", e);
      throw e;
    }
  }
  async function registerProjectTable() {
    try {
      await executeSql(`
      INSERT INTO ${TABLE_TABLES} (name, description, created_at)
      VALUES ('projects', '项目表，存储项目的基本信息', ${Date.now()})
    `);
      const tables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = 'projects'`);
      if (!tables || tables.length === 0) {
        throw new Error("获取项目表ID失败");
      }
      const tableId = tables[0].id;
      const columns = [
        { name: "id", type: "INTEGER", isPrimaryKey: 1, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: true },
        { name: "projectName", type: "TEXT", isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: false },
        { name: "legalPerson", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        { name: "constructionLocation", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        { name: "constructionScale", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        {
          name: "createTime",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: true,
          defaultValue: "datetime('now', 'localtime')"
        },
        {
          name: "updateTime",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: true,
          defaultValue: "datetime('now', 'localtime')"
        }
      ];
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        let sql = `
        INSERT INTO ${TABLE_COLUMNS} (
          table_id, name, type, is_primary_key, is_not_null, is_unique,
          is_foreign_key, reference_table_id, reference_column_id, order_index
        ) VALUES (
          ${tableId}, '${column.name}', '${column.type}', ${column.isPrimaryKey},
          ${column.isNotNull}, ${column.isUnique}, ${column.isForeignKey},
          NULL, NULL, ${i}
        )
      `;
        await executeSql(sql);
        const result = await selectSql(`SELECT id FROM ${TABLE_COLUMNS} WHERE table_id = ${tableId} AND name = '${column.name}'`);
        if (result && result.length > 0) {
          const columnId = result[0].id;
          if (column.isHidden !== void 0) {
            await executeSql(`UPDATE ${TABLE_COLUMNS} SET is_hidden = ${column.isHidden ? 1 : 0} WHERE id = ${columnId}`);
          }
          if (column.defaultValue) {
            await executeSql(`UPDATE ${TABLE_COLUMNS} SET default_value = ${JSON.stringify(column.defaultValue)} WHERE id = ${columnId}`);
          }
        }
      }
      formatAppLog("log", "at pages/index/init-tables.js:536", "项目表注册成功");
      return true;
    } catch (e) {
      formatAppLog("error", "at pages/index/init-tables.js:539", "注册项目表失败", e);
      throw e;
    }
  }
  async function registerSubprojectTable() {
    try {
      await executeSql(`
      INSERT INTO ${TABLE_TABLES} (name, description, created_at)
      VALUES ('subprojects', '子项目表，存储子项目的详细信息', ${Date.now()})
    `);
      const tables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = 'subprojects'`);
      if (!tables || tables.length === 0) {
        throw new Error("获取子项目表ID失败");
      }
      const tableId = tables[0].id;
      const projectTables = await selectSql(`SELECT id FROM ${TABLE_TABLES} WHERE name = 'projects'`);
      let projectTableId = null;
      let projectNameColumnId = null;
      let legalPersonColumnId = null;
      let constructionLocationColumnId = null;
      let constructionScaleColumnId = null;
      if (projectTables && projectTables.length > 0) {
        projectTableId = projectTables[0].id;
        const projectColumns = await selectSql(`
        SELECT id, name FROM ${TABLE_COLUMNS}
        WHERE table_id = ${projectTableId} AND name IN ('projectName', 'legalPerson', 'constructionLocation', 'constructionScale')
      `);
        if (projectColumns && projectColumns.length > 0) {
          for (const col of projectColumns) {
            switch (col.name) {
              case "projectName":
                projectNameColumnId = col.id;
                break;
              case "legalPerson":
                legalPersonColumnId = col.id;
                break;
              case "constructionLocation":
                constructionLocationColumnId = col.id;
                break;
              case "constructionScale":
                constructionScaleColumnId = col.id;
                break;
            }
          }
        }
      }
      const columns = [
        { name: "id", type: "INTEGER", isPrimaryKey: 1, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: true },
        {
          name: "projectId",
          type: "INTEGER",
          isPrimaryKey: 0,
          isNotNull: 0,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: true,
          description: "项目ID，用于逻辑关联"
        },
        {
          name: "projectName",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: false,
          description: "项目名称，从项目表选择"
        },
        { name: "subprojectName", type: "TEXT", isPrimaryKey: 0, isNotNull: 1, isUnique: 0, isForeignKey: 0, isHidden: false },
        {
          name: "constructionLocation",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 0,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: false,
          description: "建设地点，可从项目表自动填入"
        },
        {
          name: "constructionUnit",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 0,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: false,
          description: "建设单位，可从项目表自动填入"
        },
        { name: "agentUnit", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        { name: "surveyUnit", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        { name: "designUnit", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        { name: "supervisionUnit", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        { name: "constructorUnit", type: "TEXT", isPrimaryKey: 0, isNotNull: 0, isUnique: 0, isForeignKey: 0, isHidden: false },
        {
          name: "projectDescription",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 0,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: false,
          description: "项目描述，可从项目表自动填入"
        },
        {
          name: "createTime",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: true,
          defaultValue: "datetime('now', 'localtime')"
        },
        {
          name: "updateTime",
          type: "TEXT",
          isPrimaryKey: 0,
          isNotNull: 1,
          isUnique: 0,
          isForeignKey: 0,
          isHidden: true,
          defaultValue: "datetime('now', 'localtime')"
        }
      ];
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        let sql = `
        INSERT INTO ${TABLE_COLUMNS} (
          table_id, name, type, is_primary_key, is_not_null, is_unique,
          is_foreign_key, reference_table_id, reference_column_id, order_index
        ) VALUES (
          ${tableId}, '${column.name}', '${column.type}', ${column.isPrimaryKey},
          ${column.isNotNull}, ${column.isUnique}, ${column.isForeignKey},
          ${column.referenceTableId || "NULL"}, ${column.referenceColumnId || "NULL"}, ${i}
        )
      `;
        await executeSql(sql);
        const result = await selectSql(`SELECT id FROM ${TABLE_COLUMNS} WHERE table_id = ${tableId} AND name = '${column.name}'`);
        if (result && result.length > 0) {
          const columnId = result[0].id;
          if (column.isHidden !== void 0) {
            await executeSql(`UPDATE ${TABLE_COLUMNS} SET is_hidden = ${column.isHidden ? 1 : 0} WHERE id = ${columnId}`);
          }
          if (column.defaultValue) {
            await executeSql(`UPDATE ${TABLE_COLUMNS} SET default_value = ${JSON.stringify(column.defaultValue)} WHERE id = ${columnId}`);
          }
        }
      }
      formatAppLog("log", "at pages/index/init-tables.js:657", "子项目表注册成功");
      return true;
    } catch (e) {
      formatAppLog("error", "at pages/index/init-tables.js:660", "注册子项目表失败", e);
      throw e;
    }
  }
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const _sfc_main$a = {
    data() {
      return {
        tables: [],
        isLoading: false,
        appInfo: APP_INFO,
        helpInfo: HELP_INFO
      };
    },
    onLoad() {
      this.initDatabase();
    },
    onShow() {
      this.loadTables();
    },
    methods: {
      // 初始化数据库
      async initDatabase() {
        this.isLoading = true;
        try {
          formatAppLog("log", "at pages/index/index.vue:81", "开始初始化所有预置表");
          const success = await initDocumentAndArticleTables(true);
          formatAppLog("log", "at pages/index/index.vue:83", "初始化所有预置表结果:", success);
          await this.loadTables();
        } catch (e) {
          formatAppLog("error", "at pages/index/index.vue:88", "初始化数据库失败", e);
          showToast("初始化数据库失败，请重试");
        } finally {
          this.isLoading = false;
        }
      },
      // 加载表格列表
      async loadTables() {
        this.isLoading = true;
        showLoading("加载中...");
        try {
          const tables = await getAllTables();
          const tablesWithColumnCount = [];
          for (const table of tables) {
            const columns = await getTableColumns(table.id);
            tablesWithColumnCount.push({
              ...table,
              columnCount: columns.length
            });
          }
          this.tables = tablesWithColumnCount;
        } catch (e) {
          formatAppLog("error", "at pages/index/index.vue:116", "加载表格列表失败", e);
          showToast("加载表格列表失败");
        } finally {
          hideLoading();
          this.isLoading = false;
        }
      },
      // 创建表
      createTable() {
        uni.navigateTo({
          url: "/pages/table/create"
        });
      },
      // 打开表详情
      openTableDetail(table) {
        uni.navigateTo({
          url: `/pages/table/detail?id=${table.id}&name=${table.name}`
        });
      },
      // 显示帮助信息
      showHelp() {
        uni.showModal({
          title: this.helpInfo.title,
          content: this.helpInfo.content + "\n\n版本：v" + this.appInfo.version,
          showCancel: false,
          confirmText: "我知道了"
        });
      },
      // 确认清空数据库
      confirmClearDatabase() {
        uni.showModal({
          title: "清空数据库",
          content: "确定要清空数据库吗？此操作将删除所有表中的数据，且无法恢复！",
          confirmText: "确定清空",
          confirmColor: "#DD524D",
          cancelText: "取消",
          success: (res) => {
            if (res.confirm) {
              this.clearDatabase();
            }
          }
        });
      },
      // 清空数据库
      async clearDatabase() {
        showLoading("正在清空数据库...");
        try {
          const success = await clearDatabase();
          if (success) {
            showToast("数据库已清空");
            await this.loadTables();
            formatAppLog("log", "at pages/index/index.vue:179", "重新初始化文档表和条文表");
            const initResult = await initDocumentAndArticleTables(true);
            formatAppLog("log", "at pages/index/index.vue:181", "初始化结果:", initResult);
            await this.loadTables();
          } else {
            showToast("清空数据库失败");
          }
        } catch (e) {
          formatAppLog("error", "at pages/index/index.vue:189", "清空数据库失败:", e);
          showToast("清空数据库失败");
        } finally {
          hideLoading();
        }
      }
    }
  };
  function _sfc_render$9(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode(
          "text",
          { class: "header-title" },
          vue.toDisplayString($data.appInfo.name),
          1
          /* TEXT */
        ),
        vue.createElementVNode(
          "text",
          { class: "header-version" },
          "v" + vue.toDisplayString($data.appInfo.version),
          1
          /* TEXT */
        )
      ]),
      vue.createElementVNode("view", { class: "table-list" }, [
        vue.createElementVNode("view", { class: "table-list-header" }, [
          vue.createElementVNode("text", { class: "table-list-title" }, "数据表列表"),
          vue.createElementVNode("view", { class: "header-buttons" }, [
            vue.createElementVNode("view", {
              class: "clear-button",
              onClick: _cache[0] || (_cache[0] = (...args) => $options.confirmClearDatabase && $options.confirmClearDatabase(...args))
            }, [
              vue.createElementVNode("text", { class: "clear-icon" }, "🗑️")
            ]),
            vue.createElementVNode("view", {
              class: "help-button",
              onClick: _cache[1] || (_cache[1] = (...args) => $options.showHelp && $options.showHelp(...args))
            }, [
              vue.createElementVNode("text", { class: "help-icon" }, "?")
            ])
          ])
        ]),
        $data.tables.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "empty-tip"
        }, [
          vue.createElementVNode("text", null, "暂无数据表，点击右下角按钮创建")
        ])) : (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "table-items"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.tables, (table, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                key: table.id,
                class: "table-item",
                onClick: ($event) => $options.openTableDetail(table)
              }, [
                vue.createElementVNode("view", { class: "table-item-header" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "table-name" },
                    vue.toDisplayString(table.name),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "table-column-count" },
                    "列数: " + vue.toDisplayString(table.columnCount),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode(
                  "text",
                  { class: "table-description" },
                  vue.toDisplayString(table.description || "无描述"),
                  1
                  /* TEXT */
                )
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ]))
      ]),
      vue.createElementVNode("view", {
        class: "fab-button",
        onClick: _cache[2] || (_cache[2] = (...args) => $options.createTable && $options.createTable(...args))
      }, [
        vue.createElementVNode("text", { class: "fab-icon" }, "+")
      ])
    ]);
  }
  const PagesIndexIndex = /* @__PURE__ */ _export_sfc(_sfc_main$a, [["render", _sfc_render$9], ["__file", "G:/my/safety_management/pages/index/index.vue"]]);
  const _sfc_main$9 = {
    data() {
      return {
        tableName: "",
        tableDescription: "",
        columns: [],
        tableNameError: ""
      };
    },
    methods: {
      // 返回上一页
      goBack() {
        uni.navigateBack();
      },
      // 添加列
      addColumn() {
        uni.navigateTo({
          url: "/pages/column/create",
          events: {
            // 监听列创建页面返回的数据
            columnCreated: (column) => {
              if (this.columns.some((c) => c.name === column.name)) {
                uni.showToast({
                  title: `列名 '${column.name}' 已存在`,
                  icon: "none"
                });
                return;
              }
              if (column.isPrimaryKey && this.columns.some((c) => c.isPrimaryKey)) {
                uni.showToast({
                  title: "一个表只能有一个主键",
                  icon: "none"
                });
                return;
              }
              this.columns.push(column);
            }
          }
        });
      },
      // 删除列
      deleteColumn(index) {
        this.columns.splice(index, 1);
      },
      // 获取列类型文本
      getColumnTypeText(type) {
        switch (type) {
          case "TEXT":
            return "文本 (TEXT)";
          case "INTEGER":
            return "整数 (INTEGER)";
          case "REAL":
            return "小数 (REAL)";
          case "BLOB":
            return "二进制 (BLOB)";
          default:
            return type;
        }
      },
      // 验证表名
      validateTableName() {
        if (!this.tableName) {
          this.tableNameError = "表名不能为空";
          return false;
        }
        if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(this.tableName)) {
          this.tableNameError = "表名只能包含字母、数字和下划线，且必须以字母开头";
          return false;
        }
        this.tableNameError = "";
        return true;
      },
      // 保存表
      async saveTable() {
        if (!this.validateTableName()) {
          return;
        }
        if (this.columns.length === 0) {
          uni.showToast({
            title: "请至少添加一列",
            icon: "none"
          });
          return;
        }
        if (!this.columns.some((column) => column.isPrimaryKey)) {
          uni.showToast({
            title: "请至少设置一个主键列",
            icon: "none"
          });
          return;
        }
        try {
          const tableId = await createTable(
            this.tableName,
            this.tableDescription,
            this.columns
          );
          uni.showToast({
            title: "表创建成功",
            icon: "success"
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } catch (e) {
          formatAppLog("error", "at pages/table/create.vue:214", "创建表失败", e);
          uni.showToast({
            title: "创建表失败: " + (e.message || e),
            icon: "none"
          });
        }
      }
    }
  };
  function _sfc_render$8(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "header-back" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "创建数据表"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createCommentVNode(" 表名 "),
        vue.createElementVNode("view", { class: "form-item" }, [
          vue.createElementVNode("text", { class: "form-label" }, "表名"),
          vue.createElementVNode("view", { class: "input-container" }, [
            vue.createElementVNode("input", {
              class: "form-input",
              type: "text",
              value: $data.tableName,
              onInput: _cache[1] || (_cache[1] = ($event) => $data.tableName = $event.detail.value),
              placeholder: "请输入表名（英文字母和下划线）"
            }, null, 40, ["value"])
          ]),
          $data.tableNameError ? (vue.openBlock(), vue.createElementBlock(
            "text",
            {
              key: 0,
              class: "form-error"
            },
            vue.toDisplayString($data.tableNameError),
            1
            /* TEXT */
          )) : vue.createCommentVNode("v-if", true)
        ]),
        vue.createCommentVNode(" 表描述 "),
        vue.createElementVNode("view", { class: "form-item" }, [
          vue.createElementVNode("text", { class: "form-label" }, "表描述"),
          vue.createElementVNode("view", { class: "input-container" }, [
            vue.createElementVNode("textarea", {
              class: "form-textarea",
              value: $data.tableDescription,
              onInput: _cache[2] || (_cache[2] = ($event) => $data.tableDescription = $event.detail.value),
              placeholder: "请输入表描述（可选）"
            }, null, 40, ["value"])
          ])
        ]),
        vue.createCommentVNode(" 列定义 "),
        vue.createElementVNode("view", { class: "form-item" }, [
          vue.createElementVNode("view", { class: "form-header" }, [
            vue.createElementVNode("text", { class: "form-label" }, "列定义"),
            vue.createElementVNode("view", {
              class: "form-action",
              onClick: _cache[3] || (_cache[3] = (...args) => $options.addColumn && $options.addColumn(...args))
            }, [
              vue.createElementVNode("text", { class: "form-action-text" }, "添加列")
            ])
          ]),
          $data.columns.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "empty-tip"
          }, [
            vue.createElementVNode("text", null, "请添加至少一列")
          ])) : (vue.openBlock(), vue.createElementBlock("view", {
            key: 1,
            class: "column-list"
          }, [
            (vue.openBlock(true), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($data.columns, (column, index) => {
                return vue.openBlock(), vue.createElementBlock("view", {
                  key: index,
                  class: "column-item"
                }, [
                  vue.createElementVNode("view", { class: "column-item-header" }, [
                    vue.createElementVNode(
                      "text",
                      { class: "column-name" },
                      vue.toDisplayString(column.name),
                      1
                      /* TEXT */
                    ),
                    vue.createElementVNode("view", { class: "column-actions" }, [
                      vue.createElementVNode("view", {
                        class: "column-action",
                        onClick: ($event) => $options.deleteColumn(index)
                      }, [
                        vue.createElementVNode("text", { class: "column-action-text" }, "删除")
                      ], 8, ["onClick"])
                    ])
                  ]),
                  vue.createElementVNode("view", { class: "column-details" }, [
                    vue.createElementVNode(
                      "text",
                      { class: "column-type" },
                      vue.toDisplayString($options.getColumnTypeText(column.type)),
                      1
                      /* TEXT */
                    ),
                    vue.createElementVNode("view", { class: "column-attributes" }, [
                      column.isPrimaryKey ? (vue.openBlock(), vue.createElementBlock("text", {
                        key: 0,
                        class: "column-attribute"
                      }, "主键")) : vue.createCommentVNode("v-if", true),
                      column.isNotNull ? (vue.openBlock(), vue.createElementBlock("text", {
                        key: 1,
                        class: "column-attribute"
                      }, "不允许为空")) : vue.createCommentVNode("v-if", true),
                      column.isUnique ? (vue.openBlock(), vue.createElementBlock("text", {
                        key: 2,
                        class: "column-attribute"
                      }, "唯一")) : vue.createCommentVNode("v-if", true)
                    ]),
                    column.isForeignKey ? (vue.openBlock(), vue.createElementBlock(
                      "text",
                      {
                        key: 0,
                        class: "column-foreign-key"
                      },
                      " 外键关联: 表ID=" + vue.toDisplayString(column.referenceTableId) + ", 列ID=" + vue.toDisplayString(column.referenceColumnId),
                      1
                      /* TEXT */
                    )) : vue.createCommentVNode("v-if", true)
                  ])
                ]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ]))
        ]),
        vue.createCommentVNode(" 保存按钮 "),
        vue.createElementVNode("button", {
          class: "save-button",
          onClick: _cache[4] || (_cache[4] = (...args) => $options.saveTable && $options.saveTable(...args))
        }, "保存表")
      ])
    ]);
  }
  const PagesTableCreate = /* @__PURE__ */ _export_sfc(_sfc_main$9, [["render", _sfc_render$8], ["__file", "G:/my/safety_management/pages/table/create.vue"]]);
  const _sfc_main$8 = {
    data() {
      return {
        columnName: "",
        columnType: "TEXT",
        isPrimaryKey: false,
        isNotNull: false,
        isUnique: false,
        isForeignKey: false,
        tables: [],
        referenceTableIndex: -1,
        referenceColumns: [],
        referenceColumnIndex: -1,
        columnNameError: "",
        isEditMode: false
        // 是否为编辑表结构模式
      };
    },
    onLoad(options) {
      this.isEditMode = options.editMode === "true";
      this.loadTables();
    },
    methods: {
      // 返回上一页
      goBack() {
        uni.navigateBack();
      },
      // 加载所有表
      async loadTables() {
        try {
          const tables = await getAllTables();
          this.tables = tables;
          if (tables.length === 0) {
            this.isForeignKey = false;
          }
        } catch (e) {
          formatAppLog("error", "at pages/column/create.vue:170", "加载表失败", e);
          uni.showToast({
            title: "加载表失败",
            icon: "none"
          });
        }
      },
      // 切换外键选项
      toggleForeignKey() {
        if (this.tables.length === 0) {
          uni.showToast({
            title: "没有可用的表进行关联",
            icon: "none"
          });
          return;
        }
        this.isForeignKey = !this.isForeignKey;
        if (this.isForeignKey && this.tables.length > 0 && this.referenceTableIndex === -1) {
          this.referenceTableIndex = 0;
          this.loadReferenceColumns(this.tables[0].id);
        }
      },
      // 关联表变更
      onReferenceTableChange(e) {
        const index = e.detail.value;
        this.referenceTableIndex = index;
        this.referenceColumnIndex = -1;
        this.loadReferenceColumns(this.tables[index].id);
      },
      // 关联列变更
      onReferenceColumnChange(e) {
        this.referenceColumnIndex = e.detail.value;
      },
      // 加载关联表的列
      async loadReferenceColumns(tableId) {
        try {
          const columns = await getTableColumns(tableId);
          this.referenceColumns = columns;
        } catch (e) {
          formatAppLog("error", "at pages/column/create.vue:216", "加载列失败", e);
          uni.showToast({
            title: "加载列失败",
            icon: "none"
          });
        }
      },
      // 验证列名
      validateColumnName() {
        if (!this.columnName) {
          this.columnNameError = "列名不能为空";
          return false;
        }
        if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(this.columnName)) {
          this.columnNameError = "列名只能包含字母、数字和下划线，且必须以字母开头";
          return false;
        }
        this.columnNameError = "";
        return true;
      },
      // 保存列
      saveColumn() {
        if (!this.validateColumnName()) {
          return;
        }
        if (this.isForeignKey) {
          if (this.referenceTableIndex === -1) {
            uni.showToast({
              title: "请选择关联表",
              icon: "none"
            });
            return;
          }
          if (this.referenceColumnIndex === -1) {
            uni.showToast({
              title: "请选择关联列",
              icon: "none"
            });
            return;
          }
        }
        const column = {
          name: this.columnName,
          type: this.columnType,
          isPrimaryKey: this.isPrimaryKey,
          isNotNull: this.isNotNull,
          isUnique: this.isUnique,
          isForeignKey: this.isForeignKey,
          referenceTableId: this.isForeignKey ? this.tables[this.referenceTableIndex].id : null,
          referenceColumnId: this.isForeignKey ? this.referenceColumns[this.referenceColumnIndex].id : null
        };
        const eventChannel = this.getOpenerEventChannel();
        eventChannel.emit("columnCreated", column);
        uni.navigateBack();
      }
    }
  };
  function _sfc_render$7(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "header-back" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "创建列"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createCommentVNode(" 列名 "),
        vue.createElementVNode("view", { class: "form-item" }, [
          vue.createElementVNode("text", { class: "form-label" }, "列名"),
          vue.createElementVNode("view", { class: "input-container" }, [
            vue.createElementVNode("input", {
              class: "form-input",
              type: "text",
              value: $data.columnName,
              onInput: _cache[1] || (_cache[1] = ($event) => $data.columnName = $event.detail.value),
              placeholder: "请输入列名（英文字母和下划线）"
            }, null, 40, ["value"])
          ]),
          $data.columnNameError ? (vue.openBlock(), vue.createElementBlock(
            "text",
            {
              key: 0,
              class: "form-error"
            },
            vue.toDisplayString($data.columnNameError),
            1
            /* TEXT */
          )) : vue.createCommentVNode("v-if", true)
        ]),
        vue.createCommentVNode(" 数据类型 "),
        vue.createElementVNode("view", { class: "form-item" }, [
          vue.createElementVNode("text", { class: "form-label" }, "数据类型"),
          vue.createElementVNode("view", { class: "radio-group" }, [
            vue.createElementVNode(
              "view",
              {
                class: vue.normalizeClass(["radio-item", { "radio-item-selected": $data.columnType === "TEXT" }]),
                onClick: _cache[2] || (_cache[2] = ($event) => $data.columnType = "TEXT")
              },
              [
                vue.createElementVNode("text", { class: "radio-text" }, "文本 (TEXT)")
              ],
              2
              /* CLASS */
            ),
            vue.createElementVNode(
              "view",
              {
                class: vue.normalizeClass(["radio-item", { "radio-item-selected": $data.columnType === "INTEGER" }]),
                onClick: _cache[3] || (_cache[3] = ($event) => $data.columnType = "INTEGER")
              },
              [
                vue.createElementVNode("text", { class: "radio-text" }, "整数 (INTEGER)")
              ],
              2
              /* CLASS */
            ),
            vue.createElementVNode(
              "view",
              {
                class: vue.normalizeClass(["radio-item", { "radio-item-selected": $data.columnType === "REAL" }]),
                onClick: _cache[4] || (_cache[4] = ($event) => $data.columnType = "REAL")
              },
              [
                vue.createElementVNode("text", { class: "radio-text" }, "小数 (REAL)")
              ],
              2
              /* CLASS */
            ),
            vue.createElementVNode(
              "view",
              {
                class: vue.normalizeClass(["radio-item", { "radio-item-selected": $data.columnType === "BLOB" }]),
                onClick: _cache[5] || (_cache[5] = ($event) => $data.columnType = "BLOB")
              },
              [
                vue.createElementVNode("text", { class: "radio-text" }, "二进制 (BLOB)")
              ],
              2
              /* CLASS */
            )
          ])
        ]),
        vue.createCommentVNode(" 列属性 "),
        vue.createElementVNode("view", { class: "form-item" }, [
          vue.createElementVNode("text", { class: "form-label" }, "列属性"),
          vue.createElementVNode("view", { class: "checkbox-group" }, [
            vue.createElementVNode(
              "view",
              {
                class: vue.normalizeClass(["checkbox-item", { "checkbox-disabled": $data.isEditMode }]),
                onClick: _cache[6] || (_cache[6] = ($event) => !$data.isEditMode && ($data.isPrimaryKey = !$data.isPrimaryKey))
              },
              [
                vue.createElementVNode(
                  "view",
                  {
                    class: vue.normalizeClass(["checkbox", { "checkbox-selected": $data.isPrimaryKey, "checkbox-disabled": $data.isEditMode }])
                  },
                  null,
                  2
                  /* CLASS */
                ),
                vue.createElementVNode(
                  "text",
                  {
                    class: vue.normalizeClass(["checkbox-text", { "text-disabled": $data.isEditMode }])
                  },
                  "主键",
                  2
                  /* CLASS */
                ),
                $data.isEditMode ? (vue.openBlock(), vue.createElementBlock("text", {
                  key: 0,
                  class: "checkbox-tip"
                }, "(编辑模式下不可用)")) : vue.createCommentVNode("v-if", true)
              ],
              2
              /* CLASS */
            ),
            vue.createElementVNode("view", {
              class: "checkbox-item",
              onClick: _cache[7] || (_cache[7] = ($event) => $data.isNotNull = !$data.isNotNull)
            }, [
              vue.createElementVNode(
                "view",
                {
                  class: vue.normalizeClass(["checkbox", { "checkbox-selected": $data.isNotNull }])
                },
                null,
                2
                /* CLASS */
              ),
              vue.createElementVNode("text", { class: "checkbox-text" }, "不允许为空")
            ]),
            vue.createElementVNode("view", {
              class: "checkbox-item",
              onClick: _cache[8] || (_cache[8] = ($event) => $data.isUnique = !$data.isUnique)
            }, [
              vue.createElementVNode(
                "view",
                {
                  class: vue.normalizeClass(["checkbox", { "checkbox-selected": $data.isUnique }])
                },
                null,
                2
                /* CLASS */
              ),
              vue.createElementVNode("text", { class: "checkbox-text" }, "唯一值")
            ]),
            vue.createElementVNode("view", {
              class: "checkbox-item",
              onClick: _cache[9] || (_cache[9] = (...args) => $options.toggleForeignKey && $options.toggleForeignKey(...args))
            }, [
              vue.createElementVNode(
                "view",
                {
                  class: vue.normalizeClass(["checkbox", { "checkbox-selected": $data.isForeignKey }])
                },
                null,
                2
                /* CLASS */
              ),
              vue.createElementVNode("text", { class: "checkbox-text" }, "外键关联")
            ])
          ])
        ]),
        vue.createCommentVNode(" 外键关联选项 "),
        $data.isForeignKey ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "form-item foreign-key-options"
        }, [
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "关联表"),
            vue.createElementVNode("picker", {
              value: $data.referenceTableIndex,
              range: $data.tables.map((t) => t.name),
              onChange: _cache[10] || (_cache[10] = (...args) => $options.onReferenceTableChange && $options.onReferenceTableChange(...args))
            }, [
              vue.createElementVNode("view", { class: "picker-view" }, [
                $data.referenceTableIndex >= 0 ? (vue.openBlock(), vue.createElementBlock(
                  "text",
                  { key: 0 },
                  vue.toDisplayString($data.tables[$data.referenceTableIndex].name),
                  1
                  /* TEXT */
                )) : (vue.openBlock(), vue.createElementBlock("text", {
                  key: 1,
                  class: "picker-placeholder"
                }, "请选择关联表"))
              ])
            ], 40, ["value", "range"])
          ]),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "关联列"),
            vue.createElementVNode("picker", {
              value: $data.referenceColumnIndex,
              range: $data.referenceColumns.map((c) => c.name),
              disabled: $data.referenceColumns.length === 0,
              onChange: _cache[11] || (_cache[11] = (...args) => $options.onReferenceColumnChange && $options.onReferenceColumnChange(...args))
            }, [
              vue.createElementVNode("view", { class: "picker-view" }, [
                $data.referenceColumnIndex >= 0 ? (vue.openBlock(), vue.createElementBlock(
                  "text",
                  { key: 0 },
                  vue.toDisplayString($data.referenceColumns[$data.referenceColumnIndex].name),
                  1
                  /* TEXT */
                )) : (vue.openBlock(), vue.createElementBlock(
                  "text",
                  {
                    key: 1,
                    class: "picker-placeholder"
                  },
                  vue.toDisplayString($data.referenceColumns.length === 0 ? "没有可用的列" : "请选择关联列"),
                  1
                  /* TEXT */
                ))
              ])
            ], 40, ["value", "range", "disabled"])
          ])
        ])) : vue.createCommentVNode("v-if", true),
        vue.createCommentVNode(" 保存按钮 "),
        vue.createElementVNode("button", {
          class: "save-button",
          onClick: _cache[12] || (_cache[12] = (...args) => $options.saveColumn && $options.saveColumn(...args))
        }, "保存列")
      ])
    ]);
  }
  const PagesColumnCreate = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["render", _sfc_render$7], ["__file", "G:/my/safety_management/pages/column/create.vue"]]);
  const _sfc_main$7 = {
    data() {
      formatAppLog("log", "at pages/table/detail.vue:257", "初始化表格详情页，UI_CONFIG:", UI_CONFIG);
      return {
        tableId: 0,
        tableName: "",
        columns: [],
        tableData: [],
        activeTab: "data",
        // 默认显示数据标签页，方便调试
        isLoading: false,
        // 搜索和筛选
        searchText: "",
        searchDebounceTimer: null,
        activeFilters: [],
        // 批量操作
        batchMode: false,
        selectedRows: [],
        // 导出选项
        exportFormat: "csv",
        // 长按相关
        longPressTimer: null,
        longPressThreshold: UI_CONFIG.longPressThreshold || 800,
        // 长按触发时间，单位毫秒，默认800ms
        currentTouchRow: null,
        isTouchMoved: false,
        // 调试信息
        debugInfo: {
          lastLoadTime: null,
          rowIdStatus: "unknown",
          dataRowCount: 0
        }
      };
    },
    computed: {
      // 过滤后的数据
      filteredData() {
        if (!this.tableData.length)
          return [];
        let result = [...this.tableData];
        if (this.searchText) {
          const searchLower = this.searchText.toLowerCase();
          result = result.filter((row) => {
            for (const column of this.columns) {
              const value = row[column.name];
              if (value !== void 0 && value !== null) {
                const strValue = String(value).toLowerCase();
                if (strValue.includes(searchLower)) {
                  return true;
                }
              }
            }
            return false;
          });
        }
        if (this.activeFilters.length) {
          for (const filter of this.activeFilters) {
            result = result.filter((row) => {
              const value = row[filter.column];
              if (value === void 0 || value === null) {
                return false;
              }
              const filterValue = filter.value;
              switch (filter.operator) {
                case "=":
                  return String(value) === String(filterValue);
                case "!=":
                  return String(value) !== String(filterValue);
                case ">":
                  return Number(value) > Number(filterValue);
                case "<":
                  return Number(value) < Number(filterValue);
                case ">=":
                  return Number(value) >= Number(filterValue);
                case "<=":
                  return Number(value) <= Number(filterValue);
                case "包含":
                  return String(value).includes(String(filterValue));
                case "不包含":
                  return !String(value).includes(String(filterValue));
                case "开头是":
                  return String(value).startsWith(String(filterValue));
                case "结尾是":
                  return String(value).endsWith(String(filterValue));
                default:
                  return true;
              }
            });
          }
        }
        return result;
      },
      // 是否全选
      allRowsSelected() {
        return this.filteredData.length > 0 && this.selectedRows.length === this.filteredData.length;
      }
    },
    onLoad(options) {
      this.tableId = parseInt(options.id) || 0;
      this.tableName = options.name || "";
      this.loadTableData();
    },
    onShow() {
      this.loadTableData();
    },
    methods: {
      // 返回上一页
      goBack() {
        uni.navigateBack();
      },
      // 显示操作菜单
      showActions() {
        uni.showActionSheet({
          itemList: ["编辑表结构", "删除表"],
          itemColor: "#000000",
          success: (res) => {
            if (res.tapIndex === 0) {
              this.editTableStructure();
            } else if (res.tapIndex === 1) {
              this.confirmDeleteTable();
            }
          }
        });
      },
      // 编辑表结构
      editTableStructure() {
        uni.navigateTo({
          url: `/pages/table/edit?id=${this.tableId}&name=${this.tableName}`
        });
      },
      // 确认删除表
      confirmDeleteTable() {
        uni.showModal({
          title: "删除表",
          content: `确定要删除表 '${this.tableName}' 吗？此操作不可撤销，表中的所有数据都将丢失。`,
          confirmText: "删除",
          confirmColor: "#FF0000",
          success: (res) => {
            if (res.confirm) {
              this.deleteTable();
            }
          }
        });
      },
      // 删除表
      async deleteTable() {
        try {
          const success = await dropTable(this.tableId, this.tableName);
          if (success) {
            uni.showToast({
              title: "表已删除",
              icon: "success"
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            uni.showToast({
              title: "删除表失败",
              icon: "none"
            });
          }
        } catch (e) {
          formatAppLog("error", "at pages/table/detail.vue:442", "删除表失败", e);
          uni.showToast({
            title: "删除表失败: " + (e.message || e),
            icon: "none"
          });
        }
      },
      // 加载表格数据
      async loadTableData() {
        this.isLoading = true;
        try {
          await this.loadColumns();
          const data = await queryTableData(this.tableName);
          if (data && data.length > 0) {
            const hasRowId = data.every((row) => row.rowid !== void 0);
            formatAppLog("log", "at pages/table/detail.vue:464", `表 ${this.tableName} 数据加载完成，共 ${data.length} 行，是否都有rowid: ${hasRowId}`);
            this.debugInfo.lastLoadTime = (/* @__PURE__ */ new Date()).toLocaleTimeString();
            this.debugInfo.rowIdStatus = hasRowId ? "present" : "missing";
            this.debugInfo.dataRowCount = data.length;
            if (!hasRowId) {
              formatAppLog("warn", "at pages/table/detail.vue:473", `表 ${this.tableName} 的数据缺少rowid，尝试添加`);
              formatAppLog("log", "at pages/table/detail.vue:475", "第一行数据:", JSON.stringify(data[0]));
              for (let i = 0; i < data.length; i++) {
                if (data[i].id !== void 0) {
                  data[i].rowid = data[i].id;
                  formatAppLog("log", "at pages/table/detail.vue:482", `为第 ${i + 1} 行数据添加rowid = ${data[i].id}`);
                } else {
                  data[i].rowid = i + 1;
                  formatAppLog("log", "at pages/table/detail.vue:486", `为第 ${i + 1} 行数据添加临时rowid = ${i + 1}`);
                }
              }
              const hasRowIdNow = data.every((row) => row.rowid !== void 0);
              formatAppLog("log", "at pages/table/detail.vue:492", `手动添加rowid后，是否都有rowid: ${hasRowIdNow}`);
              this.debugInfo.rowIdStatus = hasRowIdNow ? "added" : "failed";
            }
          } else {
            formatAppLog("log", "at pages/table/detail.vue:496", `表 ${this.tableName} 没有数据`);
            this.debugInfo.dataRowCount = 0;
            this.debugInfo.rowIdStatus = "no_data";
          }
          this.tableData = data;
        } catch (e) {
          formatAppLog("error", "at pages/table/detail.vue:503", "加载表格数据失败", e);
          uni.showToast({
            title: "加载表格数据失败",
            icon: "none"
          });
        } finally {
          this.isLoading = false;
        }
      },
      // 加载列信息
      async loadColumns() {
        try {
          const columns = await getTableColumns(this.tableId);
          if (!columns || columns.length === 0) {
            formatAppLog("error", "at pages/table/detail.vue:519", "未找到列信息");
            uni.showToast({
              title: "未找到列信息，请确保表已正确创建",
              icon: "none",
              duration: 3e3
            });
            return;
          }
          const validColumns = columns.filter((column) => column && column.name);
          if (validColumns.length === 0) {
            formatAppLog("error", "at pages/table/detail.vue:532", "所有列都无效");
            uni.showToast({
              title: "表结构异常，请重新创建表",
              icon: "none",
              duration: 3e3
            });
            return;
          }
          formatAppLog("log", "at pages/table/detail.vue:541", "有效列数量:", validColumns.length);
          this.columns = validColumns.sort((a, b) => a.order_index - b.order_index);
        } catch (e) {
          formatAppLog("error", "at pages/table/detail.vue:546", "加载列信息失败", e);
          uni.showToast({
            title: "加载列信息失败: " + (e.message || e),
            icon: "none",
            duration: 3e3
          });
        }
      },
      // 获取列类型文本
      getColumnTypeText(type) {
        switch (type) {
          case "TEXT":
            return "文本 (TEXT)";
          case "INTEGER":
            return "整数 (INTEGER)";
          case "REAL":
            return "小数 (REAL)";
          case "BLOB":
            return "二进制 (BLOB)";
          default:
            return type;
        }
      },
      // 格式化单元格值
      formatCellValue(value, type) {
        if (value === void 0 || value === null) {
          return "";
        }
        switch (type) {
          case "INTEGER":
            return value.toString();
          case "REAL":
            return typeof value === "number" ? value.toFixed(4).replace(/\.?0+$/, "") : value;
          case "TEXT":
            if (typeof value === "string" && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(value)) {
              return value;
            }
            if (this.tableName === "articles" && this.columns.find((c) => {
              var _a;
              return c.name === "articleContent" && value === ((_a = this.tableData.find((r) => r[c.name] === value)) == null ? void 0 : _a[c.name]);
            })) {
              return value;
            }
            if (this.tableName === "documents") {
              return value;
            }
            if (typeof value === "string" && value.length > 50) {
              return value.substring(0, 50) + "...";
            }
            return value;
          default:
            return value.toString();
        }
      },
      // 判断列是否隐藏
      isHiddenColumn(column) {
        if (!column)
          return false;
        return column.is_hidden === 1;
      },
      // 获取列的CSS类
      getColumnClass(columnName) {
        if (this.tableName === "articles") {
          switch (columnName) {
            case "fileName":
              return "column-filename";
            case "articleType":
              return "column-articletype";
            case "articleContent":
              return "column-articlecontent";
            case "keywords":
              return "column-keywords";
            default:
              return "";
          }
        } else if (this.tableName === "documents") {
          switch (columnName) {
            case "fileName":
              return "column-filename";
            case "category":
              return "column-category";
            case "documentNumber":
              return "column-docnumber";
            case "publishingUnit":
              return "column-publishunit";
            default:
              return "";
          }
        } else if (this.tableName === "projects") {
          switch (columnName) {
            case "projectName":
              return "column-projectname";
            case "legalPerson":
              return "column-legalperson";
            case "constructionLocation":
              return "column-constructionlocation";
            case "constructionScale":
              return "column-constructionscale";
            default:
              return "";
          }
        } else if (this.tableName === "subprojects") {
          switch (columnName) {
            case "projectName":
              return "column-projectname";
            case "subprojectName":
              return "column-subprojectname";
            case "constructionLocation":
              return "column-constructionlocation";
            case "constructionUnit":
              return "column-constructionunit";
            case "agentUnit":
              return "column-agentunit";
            case "surveyUnit":
              return "column-surveyunit";
            case "designUnit":
              return "column-designunit";
            case "supervisionUnit":
              return "column-supervisionunit";
            case "constructorUnit":
              return "column-constructorunit";
            case "projectDescription":
              return "column-projectdescription";
            default:
              return "";
          }
        }
        return "";
      },
      // 获取列的样式
      getColumnStyle(columnName) {
        if (this.tableName === "articles") {
          switch (columnName) {
            case "fileName":
              return "min-width: 150rpx; max-width: 200rpx;";
            case "articleType":
              return "min-width: 120rpx; max-width: 150rpx;";
            case "articleContent":
              return "min-width: 400rpx; width: auto; flex: 1;";
            case "keywords":
              return "min-width: 150rpx; max-width: 200rpx;";
            default:
              return "";
          }
        } else if (this.tableName === "documents") {
          switch (columnName) {
            case "fileName":
              return "min-width: 200rpx; max-width: 300rpx;";
            case "category":
              return "min-width: 150rpx; max-width: 200rpx;";
            case "documentNumber":
              return "min-width: 200rpx; max-width: 250rpx;";
            case "publishingUnit":
              return "min-width: 200rpx; max-width: 300rpx;";
            default:
              return "";
          }
        } else if (this.tableName === "projects") {
          switch (columnName) {
            case "projectName":
              return "min-width: 300rpx; width: auto; flex: 1;";
            case "legalPerson":
              return "min-width: 200rpx; max-width: 300rpx;";
            case "constructionLocation":
              return "min-width: 200rpx; max-width: 300rpx;";
            case "constructionScale":
              return "min-width: 400rpx; width: auto; flex: 2;";
            default:
              return "min-width: 150rpx; max-width: 200rpx;";
          }
        } else if (this.tableName === "subprojects") {
          switch (columnName) {
            case "projectName":
              return "min-width: 200rpx; max-width: 300rpx;";
            case "subprojectName":
              return "min-width: 200rpx; max-width: 300rpx;";
            case "constructionLocation":
              return "min-width: 150rpx; max-width: 200rpx;";
            case "constructionUnit":
              return "min-width: 200rpx; max-width: 300rpx;";
            case "agentUnit":
              return "min-width: 150rpx; max-width: 200rpx;";
            case "surveyUnit":
              return "min-width: 120rpx; max-width: 150rpx;";
            case "designUnit":
              return "min-width: 120rpx; max-width: 150rpx;";
            case "supervisionUnit":
              return "min-width: 150rpx; max-width: 200rpx;";
            case "constructorUnit":
              return "min-width: 200rpx; max-width: 300rpx;";
            case "projectDescription":
              return "min-width: 300rpx; width: auto; flex: 1;";
            default:
              return "min-width: 120rpx; max-width: 150rpx;";
          }
        }
        return "";
      },
      // 判断是否应该换行显示文本
      shouldWrapText(columnName) {
        if (this.tableName === "articles" && columnName === "articleContent") {
          return true;
        }
        if (this.tableName === "projects" && columnName === "constructionScale") {
          return true;
        }
        if (this.tableName === "subprojects" && columnName === "projectDescription") {
          return true;
        }
        return false;
      },
      // 添加数据
      addData() {
        uni.navigateTo({
          url: `/pages/data/entry?tableId=${this.tableId}&tableName=${this.tableName}`
        });
      },
      // 显示行操作菜单
      showRowActions(row) {
        if (!row || !row.rowid) {
          uni.showToast({
            title: "无法操作此行，缺少行ID",
            icon: "none"
          });
          return;
        }
        uni.showActionSheet({
          itemList: ["编辑数据", "删除数据"],
          success: (res) => {
            if (res.tapIndex === 0) {
              this.editRow(row);
            } else if (res.tapIndex === 1) {
              this.showDeleteRowConfirm(row);
            }
          }
        });
      },
      // 编辑行数据
      editRow(row) {
        const rowDataStr = encodeURIComponent(JSON.stringify(row));
        uni.navigateTo({
          url: `/pages/data/edit?tableId=${this.tableId}&tableName=${this.tableName}&rowId=${row.rowid}&rowData=${rowDataStr}`
        });
      },
      // 显示删除行确认对话框
      showDeleteRowConfirm(row) {
        if (!row) {
          uni.showToast({
            title: "无法删除，行数据无效",
            icon: "none"
          });
          return;
        }
        let rowPreview = "";
        for (const column of this.columns) {
          if (!column || !column.name)
            continue;
          if (row[column.name] !== void 0 && row[column.name] !== null && row[column.name] !== "") {
            rowPreview += `${column.name}: ${row[column.name]}, `;
            if (rowPreview.length > 50) {
              rowPreview = rowPreview.substring(0, 50) + "...";
              break;
            }
          }
        }
        if (!rowPreview) {
          rowPreview = `行ID: ${row.rowid}`;
        }
        uni.showModal({
          title: "删除数据",
          content: `确定要删除此行数据吗？
${rowPreview}`,
          confirmText: "删除",
          confirmColor: "#FF0000",
          success: (res) => {
            if (res.confirm) {
              this.deleteRow(row.rowid);
            }
          }
        });
      },
      // 删除行
      async deleteRow(rowId) {
        try {
          const success = await deleteTableRow(this.tableName, rowId);
          if (success) {
            uni.showToast({
              title: "数据已删除",
              icon: "success"
            });
            this.loadTableData();
          } else {
            uni.showToast({
              title: "删除数据失败",
              icon: "none"
            });
          }
        } catch (e) {
          formatAppLog("error", "at pages/table/detail.vue:874", "删除数据失败", e);
          uni.showToast({
            title: "删除数据失败: " + (e.message || e),
            icon: "none"
          });
        }
      },
      // 显示列操作菜单
      showColumnActions(column, index) {
        const actions = [];
        if (index > 0) {
          actions.push("上移一位");
        }
        if (index < this.columns.length - 1) {
          actions.push("下移一位");
        }
        if (actions.length === 0) {
          uni.showToast({
            title: "无法移动此列",
            icon: "none"
          });
          return;
        }
        uni.showActionSheet({
          itemList: actions,
          success: (res) => {
            const action = actions[res.tapIndex];
            if (action === "上移一位") {
              this.moveColumnUp(column, index);
            } else if (action === "下移一位") {
              this.moveColumnDown(column, index);
            }
          }
        });
      },
      // 上移列
      async moveColumnUp(column, index) {
        if (index <= 0)
          return;
        const prevColumn = this.columns[index - 1];
        const updatedColumns = [...this.columns];
        const temp = updatedColumns[index].order_index;
        updatedColumns[index].order_index = updatedColumns[index - 1].order_index;
        updatedColumns[index - 1].order_index = temp;
        await this.updateColumnOrder([
          { id: column.id, order_index: updatedColumns[index].order_index },
          { id: prevColumn.id, order_index: updatedColumns[index - 1].order_index }
        ]);
      },
      // 下移列
      async moveColumnDown(column, index) {
        if (index >= this.columns.length - 1)
          return;
        const nextColumn = this.columns[index + 1];
        const updatedColumns = [...this.columns];
        const temp = updatedColumns[index].order_index;
        updatedColumns[index].order_index = updatedColumns[index + 1].order_index;
        updatedColumns[index + 1].order_index = temp;
        await this.updateColumnOrder([
          { id: column.id, order_index: updatedColumns[index].order_index },
          { id: nextColumn.id, order_index: updatedColumns[index + 1].order_index }
        ]);
      },
      // 更新列顺序
      async updateColumnOrder(columns) {
        try {
          const success = await updateColumnsOrder(this.tableId, columns);
          if (success) {
            uni.showToast({
              title: "列顺序已更新",
              icon: "success"
            });
            await this.loadColumns();
          } else {
            uni.showToast({
              title: "更新列顺序失败",
              icon: "none"
            });
          }
        } catch (e) {
          formatAppLog("error", "at pages/table/detail.vue:981", "更新列顺序失败", e);
          uni.showToast({
            title: "更新列顺序失败: " + (e.message || e),
            icon: "none"
          });
        }
      },
      // ===== 搜索和筛选功能 =====
      // 搜索输入处理（防抖）
      onSearchInput: debounce(function() {
        formatAppLog("log", "at pages/table/detail.vue:994", "搜索:", this.searchText);
      }, UI_CONFIG.searchDebounceTime),
      // 清除搜索
      clearSearch() {
        this.searchText = "";
      },
      // 判断单元格是否需要高亮显示（匹配搜索文本）
      isHighlighted(row, columnName) {
        if (!this.searchText)
          return false;
        const value = row[columnName];
        if (value === void 0 || value === null)
          return false;
        const strValue = String(value).toLowerCase();
        const searchLower = this.searchText.toLowerCase();
        return strValue.includes(searchLower);
      },
      // 显示筛选选项
      showFilterOptions() {
        if (!this.columns || this.columns.length === 0) {
          uni.showToast({
            title: "没有可筛选的列",
            icon: "none"
          });
          return;
        }
        const validColumns = this.columns.filter((col) => col && col.name);
        if (validColumns.length === 0) {
          uni.showToast({
            title: "没有可筛选的列",
            icon: "none"
          });
          return;
        }
        const columnOptions = validColumns.map((col) => col.name);
        uni.showActionSheet({
          itemList: columnOptions,
          success: (res) => {
            const selectedColumn = columnOptions[res.tapIndex];
            this.showFilterOperators(selectedColumn);
          }
        });
      },
      // 显示筛选操作符
      showFilterOperators(columnName) {
        const column = this.columns.find((col) => col && col.name === columnName);
        if (!column) {
          uni.showToast({
            title: "未找到列信息",
            icon: "none"
          });
          return;
        }
        let operators;
        if (column.type === "TEXT") {
          operators = FILTER_OPERATORS.TEXT;
        } else if (column.type === "INTEGER" || column.type === "REAL") {
          operators = FILTER_OPERATORS.NUMERIC;
        } else {
          operators = FILTER_OPERATORS.COMMON;
        }
        uni.showActionSheet({
          itemList: operators.map((op) => op.label),
          success: (res) => {
            const selectedOperator = operators[res.tapIndex].value;
            this.showFilterValueInput(columnName, selectedOperator);
          }
        });
      },
      // 显示筛选值输入
      showFilterValueInput(columnName, operator) {
        uni.showModal({
          title: "输入筛选值",
          content: `为 ${columnName} ${operator} ? 输入值:`,
          editable: true,
          placeholderText: "输入筛选值",
          success: (res) => {
            if (res.confirm && res.content) {
              this.addFilter(columnName, operator, res.content);
            }
          }
        });
      },
      // 添加筛选条件
      addFilter(column, operator, value) {
        this.activeFilters.push({
          column,
          operator,
          value
        });
      },
      // 移除筛选条件
      removeFilter(index) {
        this.activeFilters.splice(index, 1);
      },
      // 清除所有筛选条件
      clearAllFilters() {
        this.activeFilters = [];
      },
      // ===== 批量操作功能 =====
      // 显示批量操作选项
      showBatchOptions() {
        if (this.batchMode) {
          this.cancelBatchMode();
          return;
        }
        this.batchMode = true;
        this.selectedRows = [];
      },
      // 取消批量模式
      cancelBatchMode() {
        this.batchMode = false;
        this.selectedRows = [];
      },
      // 切换行选择
      toggleRowSelection(row) {
        const index = this.selectedRows.findIndex((r) => r.rowid === row.rowid);
        if (index >= 0) {
          this.selectedRows.splice(index, 1);
        } else {
          this.selectedRows.push(row);
        }
      },
      // 判断行是否被选中
      isRowSelected(row) {
        return this.selectedRows.some((r) => r.rowid === row.rowid);
      },
      // 全选/取消全选
      toggleSelectAll() {
        if (this.allRowsSelected) {
          this.selectedRows = [];
        } else {
          this.selectedRows = [...this.filteredData];
        }
      },
      // 删除选中的行
      deleteSelectedRows() {
        if (this.selectedRows.length === 0) {
          uni.showToast({
            title: "请先选择要删除的数据",
            icon: "none"
          });
          return;
        }
        uni.showModal({
          title: "批量删除",
          content: `确定要删除选中的 ${this.selectedRows.length} 条数据吗？此操作不可恢复。`,
          confirmText: "删除",
          confirmColor: "#FF0000",
          success: async (res) => {
            if (res.confirm) {
              await this.performBatchDelete();
            }
          }
        });
      },
      // 执行批量删除
      async performBatchDelete() {
        try {
          uni.showLoading({
            title: "正在删除..."
          });
          let successCount = 0;
          let failCount = 0;
          for (const row of this.selectedRows) {
            try {
              const success = await deleteTableRow(this.tableName, row.rowid);
              if (success) {
                successCount++;
              } else {
                failCount++;
              }
            } catch (e) {
              formatAppLog("error", "at pages/table/detail.vue:1210", "删除行失败", e);
              failCount++;
            }
          }
          uni.hideLoading();
          uni.showToast({
            title: `删除完成: ${successCount}成功, ${failCount}失败`,
            icon: "none",
            duration: 2e3
          });
          await this.loadTableData();
          this.cancelBatchMode();
        } catch (e) {
          uni.hideLoading();
          formatAppLog("error", "at pages/table/detail.vue:1231", "批量删除失败", e);
          uni.showToast({
            title: "批量删除失败: " + (e.message || e),
            icon: "none"
          });
        }
      },
      // ===== 自定义长按处理 =====
      // 处理触摸开始
      handleTouchStart(row, event) {
        if (this.longPressTimer) {
          clearTimeout(this.longPressTimer);
        }
        if (!row || !row.rowid) {
          formatAppLog("warn", "at pages/table/detail.vue:1250", "行数据缺少rowid，无法处理长按事件:", row);
          if (row) {
            const possibleIds = ["id", "ID", "Id", "rowId", "row_id"];
            for (const idField of possibleIds) {
              if (row[idField] !== void 0) {
                formatAppLog("log", "at pages/table/detail.vue:1257", `找到可能的ID字段 ${idField}:`, row[idField]);
                row.rowid = row[idField];
                formatAppLog("log", "at pages/table/detail.vue:1260", "已添加rowid:", row.rowid);
                break;
              }
            }
          }
        }
        this.currentTouchRow = row;
        this.isTouchMoved = false;
        this.longPressTimer = setTimeout(() => {
          if (!this.isTouchMoved && this.currentTouchRow) {
            if (!this.currentTouchRow.rowid) {
              formatAppLog("warn", "at pages/table/detail.vue:1276", "长按触发时行数据仍然缺少rowid");
              uni.showToast({
                title: "无法操作此行，缺少行ID",
                icon: "none"
              });
              return;
            }
            this.showRowActions(this.currentTouchRow);
            if (uni.vibrateShort) {
              uni.vibrateShort();
            }
          }
        }, this.longPressThreshold);
      },
      // 处理触摸结束
      handleTouchEnd() {
        if (this.longPressTimer) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }
        this.currentTouchRow = null;
      },
      // 处理触摸移动
      handleTouchMove() {
        this.isTouchMoved = true;
        if (this.longPressTimer) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }
      },
      // ===== 导入功能 =====
      // 显示导入选项
      showImportOptions() {
        const columnsStr = encodeURIComponent(JSON.stringify(this.columns));
        uni.navigateTo({
          url: `/pages/data/import?tableId=${this.tableId}&tableName=${this.tableName}&columns=${columnsStr}`
        });
      },
      // 跳转到智能输入页面
      goToSmartImport() {
        if (this.tableName !== "articles") {
          uni.showToast({
            title: "智能输入功能仅适用于条文表",
            icon: "none"
          });
          return;
        }
        uni.navigateTo({
          url: `/pages/data/smart-import?tableId=${this.tableId}&tableName=${this.tableName}`
        });
      },
      // ===== 导出功能 =====
      // 显示导出选项
      showExportOptions() {
        uni.showActionSheet({
          itemList: EXPORT_FORMATS.map((format) => `导出为${format.label}`),
          success: (res) => {
            const format = EXPORT_FORMATS[res.tapIndex].value;
            this.exportData(format);
          }
        });
      },
      // 导出数据
      async exportData(format) {
        try {
          if (this.filteredData.length === 0) {
            uni.showToast({
              title: "没有数据可导出",
              icon: "none"
            });
            return;
          }
          if (format !== "csv" && format !== "json") {
            uni.showToast({
              title: "仅支持CSV和JSON格式",
              icon: "none"
            });
            return;
          }
          uni.showLoading({
            title: "正在导出..."
          });
          let content = "";
          const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-");
          let fileName = `${this.tableName}_${timestamp}`;
          let mimeType = "";
          switch (format) {
            case "csv":
              content = this.generateCSV();
              fileName += ".csv";
              mimeType = "text/csv";
              break;
            case "json":
              content = this.generateJSON();
              fileName += ".json";
              mimeType = "application/json";
              break;
          }
          try {
            const platform = uni.getSystemInfoSync().platform;
            formatAppLog("log", "at pages/table/detail.vue:1439", "当前平台:", platform);
            if (platform === "android") {
              await new Promise((resolve, reject) => {
                plus.android.requestPermissions(
                  ["android.permission.WRITE_EXTERNAL_STORAGE"],
                  function(resultObj) {
                    formatAppLog("log", "at pages/table/detail.vue:1448", "权限请求结果:", resultObj);
                    if (resultObj.granted.length === 1) {
                      resolve();
                    } else {
                      reject(new Error("未授予存储权限"));
                    }
                  },
                  function(error) {
                    formatAppLog("error", "at pages/table/detail.vue:1456", "权限请求失败:", error);
                    reject(error);
                  }
                );
              });
              formatAppLog("log", "at pages/table/detail.vue:1462", "已获得存储权限，准备导出数据");
              const Environment = plus.android.importClass("android.os.Environment");
              const File = plus.android.importClass("java.io.File");
              const FileOutputStream = plus.android.importClass("java.io.FileOutputStream");
              const OutputStreamWriter = plus.android.importClass("java.io.OutputStreamWriter");
              const BufferedWriter = plus.android.importClass("java.io.BufferedWriter");
              const externalStorageDir = Environment.getExternalStorageDirectory();
              const filePath = externalStorageDir.getAbsolutePath() + "/Download/" + fileName;
              formatAppLog("log", "at pages/table/detail.vue:1474", "使用外部存储路径:", filePath);
              const fileDir = new File(filePath).getParentFile();
              if (!fileDir.exists()) {
                fileDir.mkdirs();
              }
              const fos = new FileOutputStream(filePath);
              const osw = new OutputStreamWriter(fos, "UTF-8");
              const bw = new BufferedWriter(osw);
              bw.write(content);
              bw.flush();
              bw.close();
              osw.close();
              fos.close();
              formatAppLog("log", "at pages/table/detail.vue:1493", "数据导出成功:", filePath);
              uni.hideLoading();
              uni.showModal({
                title: "导出成功",
                content: `已导出 ${this.filteredData.length} 条数据到：
${filePath}`,
                showCancel: false,
                confirmText: "确定"
              });
            } else if (platform === "ios") {
              formatAppLog("log", "at pages/table/detail.vue:1506", "iOS平台导出数据");
              const dirPath = plus.io.convertLocalFileSystemURL("_doc");
              const filePath = dirPath + "/" + fileName;
              formatAppLog("log", "at pages/table/detail.vue:1511", "iOS文件路径:", filePath);
              const fileEntry = await new Promise((resolve, reject) => {
                plus.io.requestFileSystem(plus.io.PRIVATE_DOC, (fs) => {
                  fs.root.getFile(fileName, { create: true, exclusive: false }, (fileEntry2) => {
                    resolve(fileEntry2);
                  }, (err) => {
                    reject(err);
                  });
                }, (err) => {
                  reject(err);
                });
              });
              await new Promise((resolve, reject) => {
                fileEntry.createWriter((writer) => {
                  writer.onwrite = () => {
                    resolve();
                  };
                  writer.onerror = (err) => {
                    reject(err);
                  };
                  writer.write(new Blob([content], { type: mimeType }));
                }, (err) => {
                  reject(err);
                });
              });
              formatAppLog("log", "at pages/table/detail.vue:1542", "iOS数据导出成功:", filePath);
              uni.hideLoading();
              uni.showModal({
                title: "导出成功",
                content: `已导出 ${this.filteredData.length} 条数据到文档目录。
您可以通过iTunes文件共享或其他方式访问该文件。`,
                showCancel: false,
                confirmText: "确定"
              });
            } else {
              throw new Error("不支持的平台: " + platform);
            }
          } catch (e) {
            formatAppLog("error", "at pages/table/detail.vue:1558", "APP导出错误:", e);
            throw e;
          }
        } catch (e) {
          uni.hideLoading();
          formatAppLog("error", "at pages/table/detail.vue:1610", "导出数据失败", e);
          uni.showToast({
            title: "导出数据失败: " + (e.message || e),
            icon: "none"
          });
        }
      },
      // 生成CSV格式
      generateCSV() {
        const headers = this.columns.map((col) => col.name);
        let csv = "\uFEFF";
        csv += headers.join(",") + "\n";
        for (const row of this.filteredData) {
          const rowData = headers.map((header) => {
            const value = row[header];
            if (value === void 0 || value === null) {
              return "";
            }
            const strValue = String(value);
            if (strValue.includes(",") || strValue.includes('"') || strValue.includes("\n")) {
              return '"' + strValue.replace(/"/g, '""') + '"';
            }
            return strValue;
          });
          csv += rowData.join(",") + "\n";
        }
        return csv;
      },
      // 生成JSON格式
      generateJSON() {
        const data = this.filteredData.map((row) => {
          const newRow = {};
          for (const column of this.columns) {
            newRow[column.name] = row[column.name];
          }
          return newRow;
        });
        return "\uFEFF" + JSON.stringify(data, null, 2);
      }
    }
  };
  function _sfc_render$6(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "header-back" }, "返回")
        ]),
        vue.createElementVNode(
          "text",
          { class: "header-title" },
          vue.toDisplayString($data.tableName),
          1
          /* TEXT */
        ),
        vue.createElementVNode("view", {
          class: "header-right",
          onClick: _cache[1] || (_cache[1] = (...args) => $options.showActions && $options.showActions(...args))
        }, [
          vue.createElementVNode("text", { class: "header-action" }, "操作")
        ])
      ]),
      vue.createElementVNode("view", { class: "tabs" }, [
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["tab-item", { "tab-item-active": $data.activeTab === "columns" }]),
            onClick: _cache[2] || (_cache[2] = ($event) => $data.activeTab = "columns")
          },
          [
            vue.createElementVNode("text", { class: "tab-text" }, "列定义")
          ],
          2
          /* CLASS */
        ),
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["tab-item", { "tab-item-active": $data.activeTab === "data" }]),
            onClick: _cache[3] || (_cache[3] = ($event) => $data.activeTab = "data")
          },
          [
            vue.createElementVNode("text", { class: "tab-text" }, "数据内容")
          ],
          2
          /* CLASS */
        )
      ]),
      vue.createCommentVNode(" 列定义 "),
      $data.activeTab === "columns" ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "tab-content"
      }, [
        $data.activeTab === "columns" && $data.columns.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "columns-tip"
        }, [
          vue.createElementVNode("text", { class: "columns-tip-text" }, "提示: 长按列可以调整列的顺序")
        ])) : vue.createCommentVNode("v-if", true),
        $data.isLoading ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "loading-tip"
        }, [
          vue.createElementVNode("text", null, "加载中...")
        ])) : $data.columns.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 2,
          class: "empty-tip"
        }, [
          vue.createElementVNode("text", null, "没有列定义")
        ])) : (vue.openBlock(), vue.createElementBlock("view", {
          key: 3,
          class: "column-list"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.columns, (column, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                key: column.id,
                class: "column-item",
                onLongpress: ($event) => $options.showColumnActions(column, index)
              }, [
                vue.createElementVNode("view", { class: "column-item-header" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "column-name" },
                    vue.toDisplayString(column.name),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "column-order" },
                    "顺序: " + vue.toDisplayString(column.order_index + 1),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "column-details" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "column-type" },
                    vue.toDisplayString($options.getColumnTypeText(column.type)),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode("view", { class: "column-attributes" }, [
                    column.is_primary_key === 1 ? (vue.openBlock(), vue.createElementBlock("text", {
                      key: 0,
                      class: "column-attribute"
                    }, "主键")) : vue.createCommentVNode("v-if", true),
                    column.is_not_null === 1 ? (vue.openBlock(), vue.createElementBlock("text", {
                      key: 1,
                      class: "column-attribute"
                    }, "不允许为空")) : vue.createCommentVNode("v-if", true),
                    column.is_unique === 1 ? (vue.openBlock(), vue.createElementBlock("text", {
                      key: 2,
                      class: "column-attribute"
                    }, "唯一")) : vue.createCommentVNode("v-if", true)
                  ]),
                  column.is_foreign_key === 1 ? (vue.openBlock(), vue.createElementBlock(
                    "text",
                    {
                      key: 0,
                      class: "column-foreign-key"
                    },
                    " 外键关联: 表ID=" + vue.toDisplayString(column.reference_table_id) + ", 列ID=" + vue.toDisplayString(column.reference_column_id),
                    1
                    /* TEXT */
                  )) : vue.createCommentVNode("v-if", true)
                ])
              ], 40, ["onLongpress"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ]))
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 数据内容 "),
      $data.activeTab === "data" ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 1,
        class: "tab-content"
      }, [
        vue.createElementVNode("view", { class: "data-tip" }, [
          vue.createElementVNode(
            "text",
            { class: "data-tip-text" },
            "提示: 长按数据行(约" + vue.toDisplayString($data.longPressThreshold / 1e3) + "秒)可编辑或删除该行数据",
            1
            /* TEXT */
          )
        ]),
        vue.createCommentVNode(" 搜索栏 - 单独一行 "),
        vue.createElementVNode("view", { class: "search-bar" }, [
          vue.createElementVNode("view", { class: "search-input-container" }, [
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "search-input",
                type: "text",
                "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => $data.searchText = $event),
                placeholder: "搜索数据...",
                onInput: _cache[5] || (_cache[5] = (...args) => $options.onSearchInput && $options.onSearchInput(...args))
              },
              null,
              544
              /* NEED_HYDRATION, NEED_PATCH */
            ), [
              [vue.vModelText, $data.searchText]
            ]),
            $data.searchText ? (vue.openBlock(), vue.createElementBlock("text", {
              key: 0,
              class: "search-clear",
              onClick: _cache[6] || (_cache[6] = (...args) => $options.clearSearch && $options.clearSearch(...args))
            }, "×")) : vue.createCommentVNode("v-if", true)
          ])
        ]),
        vue.createCommentVNode(" 操作按钮行 - 所有按钮在一行 "),
        vue.createElementVNode("view", { class: "action-bar" }, [
          vue.createElementVNode("view", {
            class: "action-button",
            onClick: _cache[7] || (_cache[7] = (...args) => $options.showFilterOptions && $options.showFilterOptions(...args))
          }, [
            vue.createElementVNode("text", { class: "action-button-text" }, "筛选")
          ]),
          vue.createElementVNode("view", {
            class: "action-button",
            onClick: _cache[8] || (_cache[8] = (...args) => $options.showBatchOptions && $options.showBatchOptions(...args))
          }, [
            vue.createElementVNode("text", { class: "action-button-text" }, "批量")
          ]),
          vue.createElementVNode("view", {
            class: "action-button",
            onClick: _cache[9] || (_cache[9] = (...args) => $options.showImportOptions && $options.showImportOptions(...args))
          }, [
            vue.createElementVNode("text", { class: "action-button-text" }, "导入")
          ]),
          vue.createElementVNode("view", {
            class: "action-button",
            onClick: _cache[10] || (_cache[10] = (...args) => $options.showExportOptions && $options.showExportOptions(...args))
          }, [
            vue.createElementVNode("text", { class: "action-button-text" }, "导出")
          ]),
          $data.tableName === "articles" ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "action-button smart-button",
            onClick: _cache[11] || (_cache[11] = (...args) => $options.goToSmartImport && $options.goToSmartImport(...args))
          }, [
            vue.createElementVNode("text", { class: "action-button-text" }, "智能输入")
          ])) : vue.createCommentVNode("v-if", true)
        ]),
        vue.createCommentVNode(" 筛选条件显示 "),
        $data.activeFilters.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "active-filters"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.activeFilters, (filter, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                key: index,
                class: "filter-tag"
              }, [
                vue.createElementVNode(
                  "text",
                  { class: "filter-tag-text" },
                  vue.toDisplayString(filter.column) + ": " + vue.toDisplayString(filter.operator) + " " + vue.toDisplayString(filter.value),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode("text", {
                  class: "filter-tag-remove",
                  onClick: ($event) => $options.removeFilter(index)
                }, "×", 8, ["onClick"])
              ]);
            }),
            128
            /* KEYED_FRAGMENT */
          )),
          vue.createElementVNode("text", {
            class: "clear-all-filters",
            onClick: _cache[12] || (_cache[12] = (...args) => $options.clearAllFilters && $options.clearAllFilters(...args))
          }, "清除全部")
        ])) : vue.createCommentVNode("v-if", true),
        $data.isLoading ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "loading-tip"
        }, [
          vue.createElementVNode("text", null, "加载中...")
        ])) : $options.filteredData.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 2,
          class: "empty-tip"
        }, [
          vue.createElementVNode(
            "text",
            null,
            vue.toDisplayString($data.searchText || $data.activeFilters.length > 0 ? "没有匹配的数据" : "暂无数据"),
            1
            /* TEXT */
          ),
          vue.createCommentVNode(" 数据行数显示 "),
          vue.createElementVNode("view", { class: "data-count-info-empty" }, [
            vue.createElementVNode(
              "text",
              { class: "data-count-text" },
              vue.toDisplayString($data.searchText || $data.activeFilters.length > 0 ? `筛选结果: 0/${$data.tableData.length} 条数据` : `共 0 条数据`),
              1
              /* TEXT */
            )
          ])
        ])) : (vue.openBlock(), vue.createElementBlock("view", {
          key: 3,
          class: "data-table"
        }, [
          vue.createCommentVNode(" 数据行数显示 "),
          vue.createElementVNode("view", { class: "data-count-info" }, [
            vue.createElementVNode(
              "text",
              { class: "data-count-text" },
              vue.toDisplayString($data.searchText || $data.activeFilters.length > 0 ? `筛选结果: ${$options.filteredData.length} 条数据` : `共 ${$data.tableData.length} 条数据`),
              1
              /* TEXT */
            )
          ]),
          vue.createCommentVNode(" 整体滚动视图 "),
          vue.createElementVNode("scroll-view", {
            class: "table-scroll",
            "scroll-x": "",
            "scroll-y": ""
          }, [
            vue.createCommentVNode(" 表头 (固定在顶部) "),
            vue.createElementVNode("view", { class: "table-header-container" }, [
              vue.createElementVNode("view", { class: "table-row" }, [
                vue.createCommentVNode(" 批量模式下的空白单元格，与复选框对齐 "),
                $data.batchMode ? (vue.openBlock(), vue.createElementBlock("view", {
                  key: 0,
                  class: "table-cell table-cell-checkbox table-header-cell"
                }, [
                  vue.createElementVNode("text")
                ])) : vue.createCommentVNode("v-if", true),
                (vue.openBlock(true), vue.createElementBlock(
                  vue.Fragment,
                  null,
                  vue.renderList($data.columns, (column, index) => {
                    return vue.openBlock(), vue.createElementBlock(
                      vue.Fragment,
                      {
                        key: column ? column.id : index
                      },
                      [
                        column && !$options.isHiddenColumn(column) ? (vue.openBlock(), vue.createElementBlock(
                          "view",
                          {
                            key: 0,
                            class: vue.normalizeClass(["table-cell table-header-cell", $options.getColumnClass(column.name)]),
                            style: vue.normalizeStyle($options.getColumnStyle(column.name))
                          },
                          [
                            vue.createElementVNode(
                              "text",
                              { class: "table-header-text" },
                              vue.toDisplayString(column.name),
                              1
                              /* TEXT */
                            )
                          ],
                          6
                          /* CLASS, STYLE */
                        )) : vue.createCommentVNode("v-if", true)
                      ],
                      64
                      /* STABLE_FRAGMENT */
                    );
                  }),
                  128
                  /* KEYED_FRAGMENT */
                ))
              ])
            ]),
            vue.createCommentVNode(" 表格选择头部 "),
            $data.batchMode ? (vue.openBlock(), vue.createElementBlock("view", {
              key: 0,
              class: "table-batch-header"
            }, [
              vue.createElementVNode("view", { class: "batch-select-all" }, [
                vue.createElementVNode(
                  "view",
                  {
                    class: vue.normalizeClass(["checkbox", { "checkbox-selected": $options.allRowsSelected }]),
                    onClick: _cache[13] || (_cache[13] = (...args) => $options.toggleSelectAll && $options.toggleSelectAll(...args))
                  },
                  null,
                  2
                  /* CLASS */
                ),
                vue.createElementVNode("text", { class: "batch-select-text" }, "全选")
              ]),
              vue.createElementVNode("view", { class: "batch-actions" }, [
                vue.createElementVNode(
                  "text",
                  {
                    class: "batch-action-text",
                    onClick: _cache[14] || (_cache[14] = (...args) => $options.deleteSelectedRows && $options.deleteSelectedRows(...args))
                  },
                  "删除所选 (" + vue.toDisplayString($data.selectedRows.length) + ")",
                  1
                  /* TEXT */
                ),
                vue.createElementVNode("text", {
                  class: "batch-action-cancel",
                  onClick: _cache[15] || (_cache[15] = (...args) => $options.cancelBatchMode && $options.cancelBatchMode(...args))
                }, "取消")
              ])
            ])) : vue.createCommentVNode("v-if", true),
            vue.createCommentVNode(" 表格数据行 "),
            (vue.openBlock(true), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($options.filteredData, (row, rowIndex) => {
                return vue.openBlock(), vue.createElementBlock("view", {
                  key: rowIndex,
                  class: vue.normalizeClass(["table-row", {
                    "table-row-even": rowIndex % 2 === 0,
                    "table-row-selected": $options.isRowSelected(row)
                  }]),
                  onTouchstart: ($event) => $data.batchMode ? null : $options.handleTouchStart(row, $event),
                  onTouchend: _cache[16] || (_cache[16] = ($event) => $data.batchMode ? null : $options.handleTouchEnd()),
                  onTouchmove: _cache[17] || (_cache[17] = ($event) => $data.batchMode ? null : $options.handleTouchMove()),
                  onClick: ($event) => $data.batchMode ? $options.toggleRowSelection(row) : null
                }, [
                  vue.createCommentVNode(" 批量选择复选框 "),
                  $data.batchMode ? (vue.openBlock(), vue.createElementBlock("view", {
                    key: 0,
                    class: "table-cell table-cell-checkbox"
                  }, [
                    vue.createElementVNode(
                      "view",
                      {
                        class: vue.normalizeClass(["checkbox", { "checkbox-selected": $options.isRowSelected(row) }])
                      },
                      null,
                      2
                      /* CLASS */
                    )
                  ])) : vue.createCommentVNode("v-if", true),
                  vue.createCommentVNode(" 数据单元格 "),
                  (vue.openBlock(true), vue.createElementBlock(
                    vue.Fragment,
                    null,
                    vue.renderList($data.columns, (column, colIndex) => {
                      return vue.openBlock(), vue.createElementBlock(
                        vue.Fragment,
                        {
                          key: column ? column.id : colIndex
                        },
                        [
                          column && !$options.isHiddenColumn(column) ? (vue.openBlock(), vue.createElementBlock(
                            "view",
                            {
                              key: 0,
                              class: vue.normalizeClass(["table-cell", $options.getColumnClass(column.name)]),
                              style: vue.normalizeStyle($options.getColumnStyle(column.name))
                            },
                            [
                              vue.createElementVNode(
                                "text",
                                {
                                  class: vue.normalizeClass(["table-cell-text", {
                                    "text-primary": column.is_primary_key === 1,
                                    "text-highlight": $options.isHighlighted(row, column.name),
                                    "text-wrap": $options.shouldWrapText(column.name)
                                  }])
                                },
                                vue.toDisplayString($options.formatCellValue(row[column.name], column.type)),
                                3
                                /* TEXT, CLASS */
                              )
                            ],
                            6
                            /* CLASS, STYLE */
                          )) : vue.createCommentVNode("v-if", true)
                        ],
                        64
                        /* STABLE_FRAGMENT */
                      );
                    }),
                    128
                    /* KEYED_FRAGMENT */
                  ))
                ], 42, ["onTouchstart", "onClick"]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ])
        ]))
      ])) : vue.createCommentVNode("v-if", true),
      $data.activeTab === "data" ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 2,
        class: "fab-button",
        onClick: _cache[18] || (_cache[18] = (...args) => $options.addData && $options.addData(...args))
      }, [
        vue.createElementVNode("text", { class: "fab-icon" }, "+")
      ])) : vue.createCommentVNode("v-if", true)
    ]);
  }
  const PagesTableDetail = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["render", _sfc_render$6], ["__file", "G:/my/safety_management/pages/table/detail.vue"]]);
  const _sfc_main$6 = {
    data() {
      return {
        tableId: 0,
        tableName: "",
        columns: []
      };
    },
    onLoad(options) {
      this.tableId = parseInt(options.id) || 0;
      this.tableName = options.name || "";
      this.loadColumns();
    },
    methods: {
      // 返回上一页
      goBack() {
        uni.navigateBack();
      },
      // 加载列信息
      async loadColumns() {
        try {
          const columns = await getTableColumns(this.tableId);
          this.columns = columns;
        } catch (e) {
          formatAppLog("error", "at pages/table/edit.vue:104", "加载列信息失败", e);
          uni.showToast({
            title: "加载列信息失败",
            icon: "none"
          });
        }
      },
      // 判断是否为主键
      isPrimaryKey(column) {
        return column.is_primary_key === 1;
      },
      // 获取列类型文本
      getColumnTypeText(type) {
        switch (type) {
          case "TEXT":
            return "文本 (TEXT)";
          case "INTEGER":
            return "整数 (INTEGER)";
          case "REAL":
            return "小数 (REAL)";
          case "BLOB":
            return "二进制 (BLOB)";
          default:
            return type;
        }
      },
      // 添加列
      addColumn() {
        uni.navigateTo({
          url: "/pages/column/create?editMode=true",
          events: {
            // 监听列创建页面返回的数据
            columnCreated: async (column) => {
              if (this.columns.some((c) => c.name === column.name)) {
                uni.showToast({
                  title: `列名 '${column.name}' 已存在`,
                  icon: "none"
                });
                return;
              }
              if (column.isPrimaryKey) {
                uni.showToast({
                  title: "不能添加主键列到已有表",
                  icon: "none"
                });
                return;
              }
              try {
                const success = await addColumnToTable(
                  this.tableId,
                  this.tableName,
                  column
                );
                if (success) {
                  uni.showToast({
                    title: "添加列成功",
                    icon: "success"
                  });
                  this.loadColumns();
                } else {
                  uni.showToast({
                    title: "添加列失败",
                    icon: "none"
                  });
                }
              } catch (e) {
                formatAppLog("error", "at pages/table/edit.vue:176", "添加列失败", e);
                uni.showToast({
                  title: "添加列失败: " + (e.message || e),
                  icon: "none"
                });
              }
            }
          }
        });
      },
      // 删除列
      deleteColumn(column) {
        uni.showModal({
          title: "删除列",
          content: `确定要删除列 '${column.name}' 吗？此操作不可撤销，该列的所有数据都将丢失。`,
          confirmText: "删除",
          confirmColor: "#FF0000",
          success: async (res) => {
            if (res.confirm) {
              try {
                await deleteColumnFromTable(
                  this.tableId,
                  this.tableName,
                  column.id,
                  column.name
                );
                uni.showToast({
                  title: "删除列成功",
                  icon: "success"
                });
                this.loadColumns();
              } catch (e) {
                formatAppLog("error", "at pages/table/edit.vue:214", "删除列失败", e);
                uni.showToast({
                  title: "删除列失败: " + (e.message || e),
                  icon: "none"
                });
              }
            }
          }
        });
      }
    }
  };
  function _sfc_render$5(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "header-back" }, "返回")
        ]),
        vue.createElementVNode(
          "text",
          { class: "header-title" },
          "编辑表结构: " + vue.toDisplayString($data.tableName),
          1
          /* TEXT */
        ),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createCommentVNode(" 列定义 "),
        vue.createElementVNode("view", { class: "form-item" }, [
          vue.createElementVNode("view", { class: "form-header" }, [
            vue.createElementVNode("text", { class: "form-label" }, "列定义"),
            vue.createElementVNode("view", {
              class: "form-action",
              onClick: _cache[1] || (_cache[1] = (...args) => $options.addColumn && $options.addColumn(...args))
            }, [
              vue.createElementVNode("text", { class: "form-action-text" }, "添加列")
            ])
          ]),
          $data.columns.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "empty-tip"
          }, [
            vue.createElementVNode("text", null, "加载中...")
          ])) : (vue.openBlock(), vue.createElementBlock("view", {
            key: 1,
            class: "column-list"
          }, [
            (vue.openBlock(true), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($data.columns, (column) => {
                return vue.openBlock(), vue.createElementBlock("view", {
                  key: column.id,
                  class: "column-item"
                }, [
                  vue.createElementVNode("view", { class: "column-item-header" }, [
                    vue.createElementVNode(
                      "text",
                      { class: "column-name" },
                      vue.toDisplayString(column.name),
                      1
                      /* TEXT */
                    ),
                    vue.createElementVNode("view", { class: "column-actions" }, [
                      !$options.isPrimaryKey(column) ? (vue.openBlock(), vue.createElementBlock("view", {
                        key: 0,
                        class: "column-action",
                        onClick: ($event) => $options.deleteColumn(column)
                      }, [
                        vue.createElementVNode("text", { class: "column-action-text" }, "删除")
                      ], 8, ["onClick"])) : vue.createCommentVNode("v-if", true)
                    ])
                  ]),
                  vue.createElementVNode("view", { class: "column-details" }, [
                    vue.createElementVNode(
                      "text",
                      { class: "column-type" },
                      vue.toDisplayString($options.getColumnTypeText(column.type)),
                      1
                      /* TEXT */
                    ),
                    vue.createElementVNode("view", { class: "column-attributes" }, [
                      column.is_primary_key === 1 ? (vue.openBlock(), vue.createElementBlock("text", {
                        key: 0,
                        class: "column-attribute"
                      }, "主键")) : vue.createCommentVNode("v-if", true),
                      column.is_not_null === 1 ? (vue.openBlock(), vue.createElementBlock("text", {
                        key: 1,
                        class: "column-attribute"
                      }, "不允许为空")) : vue.createCommentVNode("v-if", true),
                      column.is_unique === 1 ? (vue.openBlock(), vue.createElementBlock("text", {
                        key: 2,
                        class: "column-attribute"
                      }, "唯一")) : vue.createCommentVNode("v-if", true)
                    ]),
                    column.is_foreign_key === 1 ? (vue.openBlock(), vue.createElementBlock(
                      "text",
                      {
                        key: 0,
                        class: "column-foreign-key"
                      },
                      " 外键关联: 表ID=" + vue.toDisplayString(column.reference_table_id) + ", 列ID=" + vue.toDisplayString(column.reference_column_id),
                      1
                      /* TEXT */
                    )) : vue.createCommentVNode("v-if", true)
                  ])
                ]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ]))
        ]),
        vue.createCommentVNode(" 提示信息 "),
        vue.createElementVNode("view", { class: "tips" }, [
          vue.createElementVNode("text", { class: "tips-text" }, "提示："),
          vue.createElementVNode("text", { class: "tips-text" }, "1. 主键列不能删除"),
          vue.createElementVNode("text", { class: "tips-text" }, "2. 添加列后，原有数据的新列值将为空"),
          vue.createElementVNode("text", { class: "tips-text" }, "3. 删除列会永久删除该列的所有数据")
        ])
      ])
    ]);
  }
  const PagesTableEdit = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["render", _sfc_render$5], ["__file", "G:/my/safety_management/pages/table/edit.vue"]]);
  const _sfc_main$5 = {
    data() {
      return {
        tableId: 0,
        tableName: "",
        columns: [],
        formData: {},
        foreignKeyData: {},
        errors: {},
        // 项目选择相关（子项目表专用）
        projects: [],
        projectOptions: [],
        projectSelectionIndex: 0,
        selectedProject: null
      };
    },
    onLoad(options) {
      this.tableId = parseInt(options.tableId) || 0;
      this.tableName = options.tableName || "";
      this.loadColumns();
    },
    methods: {
      // 返回上一页
      goBack() {
        uni.navigateBack();
      },
      // 加载列信息
      async loadColumns() {
        try {
          const columns = await getTableColumns(this.tableId);
          if (!columns || columns.length === 0) {
            formatAppLog("error", "at pages/data/entry.vue:143", "未找到列信息");
            uni.showToast({
              title: "未找到列信息，请确保表已正确创建",
              icon: "none",
              duration: 3e3
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 3e3);
            return;
          }
          const validColumns = columns.filter((column) => column && column.name);
          if (validColumns.length === 0) {
            formatAppLog("error", "at pages/data/entry.vue:161", "所有列都无效");
            uni.showToast({
              title: "表结构异常，请重新创建表",
              icon: "none",
              duration: 3e3
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 3e3);
            return;
          }
          formatAppLog("log", "at pages/data/entry.vue:175", "有效列数量:", validColumns.length);
          const columnsWithOptions = validColumns.filter((col) => col.options);
          if (columnsWithOptions.length > 0) {
            formatAppLog("log", "at pages/data/entry.vue:180", "带选项的列:", columnsWithOptions.map((col) => col.name));
            columnsWithOptions.forEach((col) => {
              formatAppLog("log", "at pages/data/entry.vue:182", `${col.name} 的选项:`, col.options);
            });
          }
          this.columns = validColumns.sort((a, b) => a.order_index - b.order_index);
          this.initFormData();
          await this.loadForeignKeyData();
          if (this.tableName === "subprojects") {
            await this.loadProjectData();
          }
        } catch (e) {
          formatAppLog("error", "at pages/data/entry.vue:200", "加载列信息失败", e);
          uni.showToast({
            title: "加载列信息失败: " + (e.message || e),
            icon: "none",
            duration: 3e3
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 3e3);
        }
      },
      // 初始化表单数据
      initFormData() {
        const formData = {};
        for (const column of this.columns) {
          if (!column || !column.name || column.is_primary_key === 1 && column.type === "INTEGER") {
            continue;
          }
          formData[column.name] = "";
        }
        this.formData = formData;
        formatAppLog("log", "at pages/data/entry.vue:228", "表单数据初始化完成:", Object.keys(formData));
      },
      // 加载外键数据
      async loadForeignKeyData() {
        const foreignKeyData = {};
        for (const column of this.columns) {
          if (!column || !column.name) {
            continue;
          }
          if (column.is_foreign_key === 1 && column.reference_table_id && column.reference_column_id) {
            try {
              const data = await getForeignKeyData(column.reference_table_id, column.reference_column_id);
              if (data && data.length > 0) {
                foreignKeyData[column.id] = data;
                formatAppLog("log", "at pages/data/entry.vue:246", `已加载 ${column.name} 的外键数据:`, data.length);
              } else {
                formatAppLog("warn", "at pages/data/entry.vue:248", `未找到外键数据: ${column.name}`);
                foreignKeyData[column.id] = ["(无数据)"];
              }
            } catch (e) {
              formatAppLog("error", "at pages/data/entry.vue:253", `加载 ${column.name} 的外键数据失败:`, e);
              foreignKeyData[column.id] = ["(加载失败)"];
              uni.showToast({
                title: `加载 ${column.name} 的外键数据失败`,
                icon: "none",
                duration: 2e3
              });
            }
          }
        }
        this.foreignKeyData = foreignKeyData;
        formatAppLog("log", "at pages/data/entry.vue:268", "外键数据加载完成:", Object.keys(foreignKeyData).length);
      },
      // 外键选择变更
      onForeignKeyChange(column, e) {
        const index = e.detail.value;
        const data = this.foreignKeyData[column.id];
        if (data && index >= 0 && index < data.length) {
          this.formData[column.name] = {
            index,
            value: data[index]
          };
        }
      },
      // 加载项目数据（子项目表专用）
      async loadProjectData() {
        try {
          const projects = await queryTableData("projects");
          this.projects = projects || [];
          this.projectOptions = this.projects.map((p) => p.projectName || "未命名项目");
          if (this.projects.length === 0) {
            uni.showToast({
              title: "请先创建项目",
              icon: "none"
            });
          }
        } catch (e) {
          formatAppLog("error", "at pages/data/entry.vue:298", "加载项目数据失败", e);
          uni.showToast({
            title: "加载项目数据失败",
            icon: "none"
          });
        }
      },
      // 项目选择变更（子项目表专用）
      onProjectSelectionChange(column, e) {
        const index = e.detail.value;
        this.projectSelectionIndex = index;
        this.selectedProject = this.projects[index];
        if (this.selectedProject) {
          this.formData[column.name] = this.selectedProject.projectName;
          this.formData.projectId = this.selectedProject.id;
          if (this.selectedProject.constructionLocation) {
            this.formData.constructionLocation = this.selectedProject.constructionLocation;
          }
          if (this.selectedProject.legalPerson) {
            this.formData.constructionUnit = this.selectedProject.legalPerson;
          }
          if (this.selectedProject.constructionScale) {
            this.formData.projectDescription = this.selectedProject.constructionScale;
          }
        }
      },
      // 获取输入框占位符
      getPlaceholder(column) {
        if (!column)
          return "请输入";
        if (column.is_primary_key === 1 && column.type === "INTEGER") {
          return "自动生成";
        }
        return `请输入${column.name}`;
      },
      // 判断输入框是否禁用
      isDisabled(column) {
        if (!column)
          return false;
        return column.is_primary_key === 1 && column.type === "INTEGER";
      },
      // 判断字段是否隐藏
      isHidden(column) {
        if (!column)
          return true;
        if (column.is_hidden === 1)
          return true;
        const hiddenFields = ["id", "createTime", "updateTime"];
        formatAppLog(
          "log",
          "at pages/data/entry.vue:358",
          `检查列 ${column.name} 是否隐藏:`,
          hiddenFields.includes(column.name) ? "是" : "否",
          "is_hidden =",
          column.is_hidden
        );
        return hiddenFields.includes(column.name);
      },
      // 获取列的选项
      getColumnOptions(column) {
        try {
          if (column && column.options) {
            let options;
            if (typeof column.options === "string") {
              options = JSON.parse(column.options);
            } else if (Array.isArray(column.options)) {
              options = column.options;
            }
            return Array.isArray(options) ? options : [];
          }
        } catch (e) {
          formatAppLog("error", "at pages/data/entry.vue:382", "解析列选项失败", e, column);
        }
        return [];
      },
      // 获取类别索引
      getCategoryIndex(column) {
        if (!column)
          return 0;
        const options = this.getColumnOptions(column);
        const value = this.formData[column.name];
        return options.indexOf(value) > -1 ? options.indexOf(value) : 0;
      },
      // 类别选择变更
      onCategoryChange(column, e) {
        if (!column)
          return;
        const index = e.detail.value;
        const options = this.getColumnOptions(column);
        if (options && index >= 0 && index < options.length) {
          this.formData[column.name] = options[index];
        }
      },
      // 获取输入框类型
      getInputType(column) {
        if (!column)
          return "text";
        switch (column.type) {
          case "INTEGER":
            return "number";
          case "REAL":
            return "digit";
          default:
            return "text";
        }
      },
      // 验证表单数据
      validateForm() {
        const errors = {};
        let isValid = true;
        for (const column of this.columns) {
          if (!column || !column.name || column.is_primary_key === 1 && column.type === "INTEGER" || this.isHidden(column)) {
            continue;
          }
          const value = this.formData[column.name];
          if (column.is_not_null === 1) {
            if (!value || typeof value === "object" && !value.value) {
              errors[column.name] = `${column.name} 不能为空`;
              isValid = false;
              continue;
            }
          }
          if (value) {
            const actualValue = typeof value === "object" ? value.value : value;
            if (column.type === "INTEGER" && !/^-?\d+$/.test(actualValue)) {
              errors[column.name] = `${column.name} 必须是整数`;
              isValid = false;
            } else if (column.type === "REAL" && !/^-?\d+(\.\d+)?$/.test(actualValue)) {
              errors[column.name] = `${column.name} 必须是数字`;
              isValid = false;
            }
          }
        }
        this.errors = errors;
        formatAppLog("log", "at pages/data/entry.vue:455", "表单验证结果:", isValid, errors);
        return isValid;
      },
      // 保存数据
      async saveData() {
        if (!this.validateForm()) {
          return;
        }
        try {
          const data = {};
          for (const column of this.columns) {
            if (!column || !column.name || column.is_primary_key === 1 && column.type === "INTEGER") {
              continue;
            }
            if (this.isHidden(column) && !(column.name in this.formData)) {
              continue;
            }
            const value = this.formData[column.name];
            if (typeof value === "object" && value.value) {
              data[column.name] = value.value;
            } else if (value || value === 0 || value === "") {
              data[column.name] = value;
            } else if (column.is_not_null === 1) {
              data[column.name] = "";
            }
          }
          if (this.tableName === "subprojects" && this.formData.projectId) {
            data.projectId = this.formData.projectId;
          }
          formatAppLog("log", "at pages/data/entry.vue:501", "准备保存数据:", data);
          if (Object.keys(data).length === 0) {
            uni.showToast({
              title: "没有有效数据可保存",
              icon: "none"
            });
            return;
          }
          const result = await insertData(this.tableName, data);
          if (result) {
            const rowIdMsg = typeof result === "number" ? `，行ID: ${result}` : "";
            uni.showToast({
              title: `数据添加成功${rowIdMsg}`,
              icon: "success",
              duration: 2e3
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 2e3);
          } else {
            uni.showToast({
              title: "数据添加失败",
              icon: "none"
            });
          }
        } catch (e) {
          formatAppLog("error", "at pages/data/entry.vue:535", "保存数据失败", e);
          uni.showToast({
            title: "保存数据失败: " + (e.message || e),
            icon: "none",
            duration: 3e3
          });
        }
      }
    }
  };
  function _sfc_render$4(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "header-back" }, "返回")
        ]),
        vue.createElementVNode(
          "text",
          { class: "header-title" },
          "添加数据: " + vue.toDisplayString($data.tableName),
          1
          /* TEXT */
        ),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createElementVNode("view", { class: "form-container" }, [
        $data.columns.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "empty-tip"
        }, [
          vue.createElementVNode("text", null, "加载中...")
        ])) : (vue.openBlock(), vue.createElementBlock("view", { key: 1 }, [
          vue.createCommentVNode(" 数据输入表单 "),
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.columns, (column, index) => {
              var _a, _b;
              return vue.openBlock(), vue.createElementBlock(
                vue.Fragment,
                {
                  key: column ? column.id : index
                },
                [
                  column && !$options.isHidden(column) ? (vue.openBlock(), vue.createElementBlock("view", {
                    key: 0,
                    class: "form-item"
                  }, [
                    vue.createElementVNode(
                      "text",
                      { class: "form-label" },
                      vue.toDisplayString(column.name) + " (" + vue.toDisplayString(column.type) + ")",
                      1
                      /* TEXT */
                    ),
                    vue.createCommentVNode(" 项目选择器（子项目表专用） "),
                    $data.tableName === "subprojects" && column.name === "projectName" ? (vue.openBlock(), vue.createElementBlock("view", {
                      key: 0,
                      class: "input-container"
                    }, [
                      vue.createElementVNode("picker", {
                        value: $data.projectSelectionIndex,
                        range: $data.projectOptions,
                        onChange: (e) => $options.onProjectSelectionChange(column, e)
                      }, [
                        vue.createElementVNode("view", { class: "picker-view" }, [
                          $data.formData[column.name] ? (vue.openBlock(), vue.createElementBlock(
                            "text",
                            { key: 0 },
                            vue.toDisplayString($data.formData[column.name]),
                            1
                            /* TEXT */
                          )) : (vue.openBlock(), vue.createElementBlock("text", {
                            key: 1,
                            class: "picker-placeholder"
                          }, "请选择上级项目"))
                        ])
                      ], 40, ["value", "range", "onChange"])
                    ])) : column.is_foreign_key === 1 && $data.foreignKeyData[column.id] && $data.foreignKeyData[column.id].length > 0 ? (vue.openBlock(), vue.createElementBlock(
                      vue.Fragment,
                      { key: 1 },
                      [
                        vue.createCommentVNode(" 外键选择器 "),
                        vue.createElementVNode("view", { class: "input-container" }, [
                          vue.createElementVNode("picker", {
                            value: ((_a = $data.formData[column.name]) == null ? void 0 : _a.index) || 0,
                            range: $data.foreignKeyData[column.id],
                            onChange: (e) => $options.onForeignKeyChange(column, e)
                          }, [
                            vue.createElementVNode("view", { class: "picker-view" }, [
                              ((_b = $data.formData[column.name]) == null ? void 0 : _b.value) ? (vue.openBlock(), vue.createElementBlock(
                                "text",
                                { key: 0 },
                                vue.toDisplayString($data.formData[column.name].value),
                                1
                                /* TEXT */
                              )) : (vue.openBlock(), vue.createElementBlock(
                                "text",
                                {
                                  key: 1,
                                  class: "picker-placeholder"
                                },
                                "请选择" + vue.toDisplayString(column.name),
                                1
                                /* TEXT */
                              ))
                            ])
                          ], 40, ["value", "range", "onChange"])
                        ])
                      ],
                      2112
                      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
                    )) : (column.name === "category" || column.name === "articleType") && $options.getColumnOptions(column).length > 0 ? (vue.openBlock(), vue.createElementBlock(
                      vue.Fragment,
                      { key: 2 },
                      [
                        vue.createCommentVNode(" 类别下拉选择器 "),
                        vue.createElementVNode("view", { class: "input-container" }, [
                          vue.createElementVNode("picker", {
                            value: $options.getCategoryIndex(column),
                            range: $options.getColumnOptions(column),
                            onChange: (e) => $options.onCategoryChange(column, e)
                          }, [
                            vue.createElementVNode("view", { class: "picker-view" }, [
                              $data.formData[column.name] ? (vue.openBlock(), vue.createElementBlock(
                                "text",
                                { key: 0 },
                                vue.toDisplayString($data.formData[column.name]),
                                1
                                /* TEXT */
                              )) : (vue.openBlock(), vue.createElementBlock(
                                "text",
                                {
                                  key: 1,
                                  class: "picker-placeholder"
                                },
                                "请选择" + vue.toDisplayString(column.name),
                                1
                                /* TEXT */
                              ))
                            ])
                          ], 40, ["value", "range", "onChange"])
                        ])
                      ],
                      2112
                      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
                    )) : (vue.openBlock(), vue.createElementBlock(
                      vue.Fragment,
                      { key: 3 },
                      [
                        vue.createCommentVNode(" 普通输入框 "),
                        vue.createElementVNode("view", { class: "input-container" }, [
                          vue.createElementVNode("input", {
                            class: "form-input",
                            value: $data.formData[column.name],
                            onInput: ($event) => $data.formData[column.name] = $event.detail.value,
                            placeholder: $options.getPlaceholder(column),
                            disabled: $options.isDisabled(column),
                            type: $options.getInputType(column)
                          }, null, 40, ["value", "onInput", "placeholder", "disabled", "type"])
                        ])
                      ],
                      2112
                      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
                    )),
                    $data.errors[column.name] ? (vue.openBlock(), vue.createElementBlock(
                      "text",
                      {
                        key: 4,
                        class: "form-error"
                      },
                      vue.toDisplayString($data.errors[column.name]),
                      1
                      /* TEXT */
                    )) : vue.createCommentVNode("v-if", true)
                  ])) : vue.createCommentVNode("v-if", true)
                ],
                64
                /* STABLE_FRAGMENT */
              );
            }),
            128
            /* KEYED_FRAGMENT */
          )),
          vue.createCommentVNode(" 保存按钮 "),
          vue.createElementVNode("button", {
            class: "save-button",
            onClick: _cache[1] || (_cache[1] = (...args) => $options.saveData && $options.saveData(...args))
          }, "保存数据")
        ]))
      ])
    ]);
  }
  const PagesDataEntry = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["render", _sfc_render$4], ["__file", "G:/my/safety_management/pages/data/entry.vue"]]);
  const _sfc_main$4 = {
    data() {
      return {
        tableId: 0,
        tableName: "",
        rowId: 0,
        rowData: null,
        columns: [],
        formData: {},
        foreignKeyData: {},
        errors: {},
        isLoading: false,
        isSaving: false
      };
    },
    onLoad(options) {
      this.tableId = parseInt(options.tableId) || 0;
      this.tableName = options.tableName || "";
      this.rowId = parseInt(options.rowId) || 0;
      if (options.rowData) {
        try {
          this.rowData = JSON.parse(decodeURIComponent(options.rowData));
        } catch (e) {
          formatAppLog("error", "at pages/data/edit.vue:103", "解析行数据失败", e);
        }
      }
      this.loadColumns();
    },
    methods: {
      // 返回上一页
      goBack() {
        uni.navigateBack();
      },
      // 加载列信息
      async loadColumns() {
        this.isLoading = true;
        try {
          const columns = await getTableColumns(this.tableId);
          this.columns = columns.sort((a, b) => a.order_index - b.order_index);
          this.initFormData();
          await this.loadForeignKeyData();
        } catch (e) {
          formatAppLog("error", "at pages/data/edit.vue:131", "加载列信息失败", e);
          uni.showToast({
            title: "加载列信息失败",
            icon: "none"
          });
        } finally {
          this.isLoading = false;
        }
      },
      // 初始化表单数据
      initFormData() {
        const formData = {};
        for (const column of this.columns) {
          if (column.is_primary_key === 1 && column.type === "INTEGER" || this.isHidden(column)) {
            continue;
          }
          if (this.rowData && this.rowData[column.name] !== void 0) {
            formData[column.name] = this.rowData[column.name];
          } else {
            formData[column.name] = "";
          }
        }
        this.formData = formData;
      },
      // 加载外键数据
      async loadForeignKeyData() {
        const foreignKeyData = {};
        for (const column of this.columns) {
          if (this.isHidden(column)) {
            continue;
          }
          if (column.is_foreign_key === 1 && column.reference_table_id && column.reference_column_id) {
            try {
              const data = await getForeignKeyData(column.reference_table_id, column.reference_column_id);
              foreignKeyData[column.id] = data;
              if (this.rowData && this.rowData[column.name] !== void 0) {
                const value = this.rowData[column.name];
                const index = data.findIndex((item) => item === value);
                if (index >= 0) {
                  this.formData[column.name] = {
                    index,
                    value: data[index]
                  };
                }
              }
            } catch (e) {
              formatAppLog("error", "at pages/data/edit.vue:189", "加载外键数据失败", e);
            }
          }
        }
        this.foreignKeyData = foreignKeyData;
      },
      // 外键选择变更
      onForeignKeyChange(column, e) {
        const index = e.detail.value;
        const data = this.foreignKeyData[column.id];
        if (data && index >= 0 && index < data.length) {
          this.formData[column.name] = {
            index,
            value: data[index]
          };
        }
      },
      // 获取输入框占位符
      getPlaceholder(column) {
        if (column.is_primary_key === 1 && column.type === "INTEGER") {
          return "自动生成";
        }
        return `请输入${column.name}`;
      },
      // 判断输入框是否禁用
      isDisabled(column) {
        return column.is_primary_key === 1 && column.type === "INTEGER";
      },
      // 获取输入框类型
      getInputType(column) {
        switch (column.type) {
          case "INTEGER":
            return "number";
          case "REAL":
            return "digit";
          default:
            return "text";
        }
      },
      // 判断列是否隐藏
      isHidden(column) {
        if (!column)
          return true;
        if (column.is_hidden === 1)
          return true;
        const hiddenFields = ["id", "createTime", "updateTime"];
        formatAppLog(
          "log",
          "at pages/data/edit.vue:243",
          `检查列 ${column.name} 是否隐藏:`,
          hiddenFields.includes(column.name) ? "是" : "否",
          "is_hidden =",
          column.is_hidden
        );
        return hiddenFields.includes(column.name);
      },
      // 验证表单数据
      validateForm() {
        const errors = {};
        let isValid = true;
        for (const column of this.columns) {
          if (column.is_primary_key === 1 && column.type === "INTEGER" || this.isHidden(column)) {
            continue;
          }
          const value = this.formData[column.name];
          if (column.is_not_null === 1) {
            if (!value || typeof value === "object" && !value.value) {
              errors[column.name] = `${column.name} 不能为空`;
              isValid = false;
              continue;
            }
          }
          if (value) {
            const actualValue = typeof value === "object" ? value.value : value;
            if (column.type === "INTEGER" && !/^-?\d+$/.test(actualValue)) {
              errors[column.name] = `${column.name} 必须是整数`;
              isValid = false;
            } else if (column.type === "REAL" && !/^-?\d+(\.\d+)?$/.test(actualValue)) {
              errors[column.name] = `${column.name} 必须是数字`;
              isValid = false;
            }
          }
        }
        this.errors = errors;
        return isValid;
      },
      // 保存数据
      async saveData() {
        if (!this.validateForm()) {
          return;
        }
        this.isSaving = true;
        try {
          const data = {};
          for (const column of this.columns) {
            if (column.is_primary_key === 1 && column.type === "INTEGER" || this.isHidden(column)) {
              continue;
            }
            const value = this.formData[column.name];
            if (value !== void 0) {
              if (typeof value === "object" && value.value) {
                data[column.name] = value.value;
              } else {
                data[column.name] = value;
              }
            }
          }
          uni.showLoading({
            title: "保存中..."
          });
          const success = await updateTableRow(this.tableName, this.rowId, data);
          uni.hideLoading();
          if (success) {
            uni.showToast({
              title: "数据更新成功",
              icon: "success"
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            uni.showToast({
              title: "数据更新失败",
              icon: "none"
            });
          }
        } catch (e) {
          uni.hideLoading();
          formatAppLog("error", "at pages/data/edit.vue:353", "保存数据失败", e);
          uni.showToast({
            title: "保存数据失败: " + (e.message || e),
            icon: "none"
          });
        } finally {
          this.isSaving = false;
        }
      }
    }
  };
  function _sfc_render$3(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "header-back" }, "返回")
        ]),
        vue.createElementVNode(
          "text",
          { class: "header-title" },
          "编辑数据: " + vue.toDisplayString($data.tableName),
          1
          /* TEXT */
        ),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createElementVNode("view", { class: "form-container" }, [
        $data.isLoading ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "loading-tip"
        }, [
          vue.createElementVNode("text", null, "加载中...")
        ])) : $data.columns.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "empty-tip"
        }, [
          vue.createElementVNode("text", null, "没有列定义")
        ])) : (vue.openBlock(), vue.createElementBlock("view", { key: 2 }, [
          vue.createCommentVNode(" 数据编辑表单 "),
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.columns, (column, index) => {
              var _a, _b;
              return vue.openBlock(), vue.createElementBlock(
                vue.Fragment,
                {
                  key: column ? column.id : index
                },
                [
                  column && !$options.isHidden(column) ? (vue.openBlock(), vue.createElementBlock("view", {
                    key: 0,
                    class: "form-item"
                  }, [
                    vue.createElementVNode(
                      "text",
                      { class: "form-label" },
                      vue.toDisplayString(column.name) + " (" + vue.toDisplayString(column.type) + ")",
                      1
                      /* TEXT */
                    ),
                    vue.createCommentVNode(" 外键选择器 "),
                    column.is_foreign_key === 1 && $data.foreignKeyData[column.id] && $data.foreignKeyData[column.id].length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
                      key: 0,
                      class: "input-container"
                    }, [
                      vue.createElementVNode("picker", {
                        value: ((_a = $data.formData[column.name]) == null ? void 0 : _a.index) || 0,
                        range: $data.foreignKeyData[column.id],
                        onChange: (e) => $options.onForeignKeyChange(column, e)
                      }, [
                        vue.createElementVNode("view", { class: "picker-view" }, [
                          ((_b = $data.formData[column.name]) == null ? void 0 : _b.value) ? (vue.openBlock(), vue.createElementBlock(
                            "text",
                            { key: 0 },
                            vue.toDisplayString($data.formData[column.name].value),
                            1
                            /* TEXT */
                          )) : (vue.openBlock(), vue.createElementBlock(
                            "text",
                            {
                              key: 1,
                              class: "picker-placeholder"
                            },
                            "请选择" + vue.toDisplayString(column.name),
                            1
                            /* TEXT */
                          ))
                        ])
                      ], 40, ["value", "range", "onChange"])
                    ])) : (vue.openBlock(), vue.createElementBlock(
                      vue.Fragment,
                      { key: 1 },
                      [
                        vue.createCommentVNode(" 普通输入框 "),
                        vue.createElementVNode("view", { class: "input-container" }, [
                          vue.createElementVNode("input", {
                            class: "form-input",
                            value: $data.formData[column.name],
                            onInput: ($event) => $data.formData[column.name] = $event.detail.value,
                            placeholder: $options.getPlaceholder(column),
                            disabled: $options.isDisabled(column),
                            type: $options.getInputType(column)
                          }, null, 40, ["value", "onInput", "placeholder", "disabled", "type"])
                        ])
                      ],
                      2112
                      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
                    )),
                    $data.errors[column.name] ? (vue.openBlock(), vue.createElementBlock(
                      "text",
                      {
                        key: 2,
                        class: "form-error"
                      },
                      vue.toDisplayString($data.errors[column.name]),
                      1
                      /* TEXT */
                    )) : vue.createCommentVNode("v-if", true)
                  ])) : vue.createCommentVNode("v-if", true)
                ],
                64
                /* STABLE_FRAGMENT */
              );
            }),
            128
            /* KEYED_FRAGMENT */
          )),
          vue.createCommentVNode(" 保存按钮 "),
          vue.createElementVNode("button", {
            class: "save-button",
            onClick: _cache[1] || (_cache[1] = (...args) => $options.saveData && $options.saveData(...args)),
            disabled: $data.isSaving
          }, vue.toDisplayString($data.isSaving ? "保存中..." : "保存数据"), 9, ["disabled"])
        ]))
      ])
    ]);
  }
  const PagesDataEdit = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["render", _sfc_render$3], ["__file", "G:/my/safety_management/pages/data/edit.vue"]]);
  const _sfc_main$3 = {
    data() {
      return {
        tableId: 0,
        tableName: "",
        columns: [],
        // 导入选项
        formats: EXPORT_FORMATS,
        selectedFormat: "csv",
        importMethods: [
          { label: "文件", value: "file" },
          { label: "文本", value: "text" }
        ],
        selectedMethod: "file",
        // 文件导入
        selectedFile: "",
        fileContent: "",
        // 文本导入
        importText: "",
        // 预览数据
        previewData: [],
        previewColumns: [],
        // 状态
        isLoading: false,
        isImporting: false,
        importError: ""
      };
    },
    onLoad(options) {
      this.tableId = parseInt(options.tableId) || 0;
      this.tableName = options.tableName || "";
      if (options.columns) {
        try {
          this.columns = JSON.parse(decodeURIComponent(options.columns));
        } catch (e) {
          formatAppLog("error", "at pages/data/import.vue:185", "解析列信息失败", e);
        }
      }
    },
    watch: {
      // 监听导入文本变化
      importText() {
        if (this.importText) {
          this.parseImportData();
        } else {
          this.previewData = [];
          this.previewColumns = [];
          this.importError = "";
        }
      },
      // 监听格式变化
      selectedFormat() {
        this.previewData = [];
        this.previewColumns = [];
        this.importError = "";
        if (this.selectedMethod === "file" && this.fileContent) {
          this.parseImportData();
        } else if (this.selectedMethod === "text" && this.importText) {
          this.parseImportData();
        }
      }
    },
    methods: {
      // 返回上一页
      goBack() {
        uni.navigateBack();
      },
      // 选择文件
      selectFile() {
        if (uni.getSystemInfoSync().platform === "android") {
          this.selectFileFromDownload();
          return;
        }
        if (uni.getSystemInfoSync().platform === "ios") {
          uni.showModal({
            title: "提示",
            content: "iOS平台暂不支持直接选择文件，请使用文本输入方式导入数据。",
            showCancel: false,
            success: () => {
              this.selectedMethod = "text";
            }
          });
          return;
        }
      },
      // 从Download文件夹选择文件
      async selectFileFromDownload() {
        try {
          showLoading("正在查找文件...");
          await new Promise((resolve, reject) => {
            plus.android.requestPermissions(
              ["android.permission.READ_EXTERNAL_STORAGE"],
              function(resultObj) {
                formatAppLog("log", "at pages/data/import.vue:283", "权限请求结果:", resultObj);
                if (resultObj.granted.length === 1) {
                  resolve();
                } else {
                  hideLoading();
                  reject(new Error("未授予存储权限"));
                }
              },
              function(error) {
                formatAppLog("error", "at pages/data/import.vue:292", "权限请求失败:", error);
                hideLoading();
                reject(error);
              }
            );
          });
          formatAppLog("log", "at pages/data/import.vue:299", "已获得存储权限，准备选择文件");
          const Intent = plus.android.importClass("android.content.Intent");
          const Environment = plus.android.importClass("android.os.Environment");
          const Uri = plus.android.importClass("android.net.Uri");
          const File = plus.android.importClass("java.io.File");
          const FileInputStream = plus.android.importClass("java.io.FileInputStream");
          const BufferedReader = plus.android.importClass("java.io.BufferedReader");
          const InputStreamReader = plus.android.importClass("java.io.InputStreamReader");
          const Context = plus.android.importClass("android.content.Context");
          let downloadPath = "";
          try {
            const downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            if (downloadDir) {
              downloadPath = downloadDir.getAbsolutePath();
              formatAppLog("log", "at pages/data/import.vue:319", "方法1获取Download路径:", downloadPath);
            }
          } catch (e) {
            formatAppLog("error", "at pages/data/import.vue:322", "方法1获取Download路径失败:", e);
          }
          if (!downloadPath) {
            try {
              const externalDir = Environment.getExternalStorageDirectory();
              if (externalDir) {
                downloadPath = externalDir.getAbsolutePath() + "/Download";
                formatAppLog("log", "at pages/data/import.vue:332", "方法2获取Download路径:", downloadPath);
              }
            } catch (e) {
              formatAppLog("error", "at pages/data/import.vue:335", "方法2获取Download路径失败:", e);
            }
          }
          if (!downloadPath) {
            try {
              downloadPath = "/storage/emulated/0/Download";
              formatAppLog("log", "at pages/data/import.vue:344", "方法3使用固定Download路径:", downloadPath);
            } catch (e) {
              formatAppLog("error", "at pages/data/import.vue:346", "方法3使用固定Download路径失败:", e);
            }
          }
          if (!downloadPath) {
            throw new Error("无法获取Download文件夹路径");
          }
          formatAppLog("log", "at pages/data/import.vue:355", "最终使用的Download文件夹路径:", downloadPath);
          const downloadFolder = new File(downloadPath);
          if (!downloadFolder.exists()) {
            formatAppLog("error", "at pages/data/import.vue:362", "Download文件夹不存在:", downloadPath);
            uni.showModal({
              title: "提示",
              content: "Download文件夹不存在，请确保您的设备有Download文件夹",
              showCancel: false
            });
            return;
          }
          if (!downloadFolder.isDirectory()) {
            formatAppLog("error", "at pages/data/import.vue:373", "Download路径不是文件夹:", downloadPath);
            uni.showModal({
              title: "提示",
              content: "Download路径不是文件夹",
              showCancel: false
            });
            return;
          }
          if (!downloadFolder.canRead()) {
            formatAppLog("error", "at pages/data/import.vue:384", "没有Download文件夹的读取权限:", downloadPath);
            uni.showModal({
              title: "提示",
              content: "没有Download文件夹的读取权限，请在设置中授予应用存储权限",
              showCancel: false
            });
            return;
          }
          const files = downloadFolder.listFiles();
          formatAppLog("log", "at pages/data/import.vue:395", "Download文件夹中的文件数量:", files ? files.length : 0);
          if (!files || files.length === 0) {
            uni.showModal({
              title: "提示",
              content: "Download文件夹中没有文件",
              showCancel: false
            });
            return;
          }
          const validFiles = [];
          for (let i = 0; i < files.length; i++) {
            try {
              const file = files[i];
              if (!file) {
                formatAppLog("warn", "at pages/data/import.vue:412", `文件索引 ${i} 为null，跳过`);
                continue;
              }
              if (!file.canRead()) {
                formatAppLog("warn", "at pages/data/import.vue:418", `文件不可读，跳过:`, file);
                continue;
              }
              if (!file.isFile()) {
                formatAppLog("warn", "at pages/data/import.vue:424", `不是文件，跳过:`, file);
                continue;
              }
              const fileName = file.getName();
              if (!fileName) {
                formatAppLog("warn", "at pages/data/import.vue:430", `文件名为空，跳过:`, file);
                continue;
              }
              formatAppLog("log", "at pages/data/import.vue:434", `检查文件: ${fileName}`);
              if (fileName.toLowerCase().endsWith(".csv") || fileName.toLowerCase().endsWith(".json")) {
                const filePath = file.getAbsolutePath();
                formatAppLog("log", "at pages/data/import.vue:438", `找到有效文件: ${fileName}, 路径: ${filePath}`);
                validFiles.push({
                  name: fileName,
                  path: filePath
                });
              }
            } catch (e) {
              formatAppLog("error", "at pages/data/import.vue:445", `处理文件索引 ${i} 时出错:`, e);
            }
          }
          formatAppLog("log", "at pages/data/import.vue:449", `找到 ${validFiles.length} 个有效的CSV/JSON文件`);
          if (validFiles.length === 0) {
            uni.showModal({
              title: "提示",
              content: "Download文件夹中没有CSV或JSON文件",
              showCancel: false
            });
            return;
          }
          hideLoading();
          uni.showActionSheet({
            itemList: validFiles.map((file) => file.name),
            success: (res) => {
              const selectedFile = validFiles[res.tapIndex];
              this.selectedFile = selectedFile.name;
              this.readFileFromPath(selectedFile.path);
            }
          });
        } catch (e) {
          hideLoading();
          formatAppLog("error", "at pages/data/import.vue:478", "选择文件失败:", e);
          uni.showToast({
            title: "选择文件失败: " + (e.message || e),
            icon: "none"
          });
        }
      },
      // 从指定路径读取文件内容
      readFileFromPath(filePath) {
        this.isLoading = true;
        this.importError = "";
        formatAppLog("log", "at pages/data/import.vue:490", "开始读取文件:", filePath);
        try {
          const File = plus.android.importClass("java.io.File");
          const FileInputStream = plus.android.importClass("java.io.FileInputStream");
          const BufferedReader = plus.android.importClass("java.io.BufferedReader");
          const InputStreamReader = plus.android.importClass("java.io.InputStreamReader");
          const StringBuilder = plus.android.importClass("java.lang.StringBuilder");
          const file = new File(filePath);
          if (!file.exists()) {
            throw new Error("文件不存在: " + filePath);
          }
          if (!file.canRead()) {
            throw new Error("文件不可读: " + filePath);
          }
          const fileSize = file.length();
          formatAppLog("log", "at pages/data/import.vue:513", "文件大小:", fileSize, "字节");
          if (fileSize > 10 * 1024 * 1024) {
            throw new Error("文件太大，超过10MB");
          }
          const fileName = file.getName().toLowerCase();
          let encoding = "GBK";
          if (fileName.endsWith(".json")) {
            encoding = "UTF-8";
            formatAppLog("log", "at pages/data/import.vue:527", "检测到JSON文件，使用UTF-8编码");
          } else {
            formatAppLog("log", "at pages/data/import.vue:529", "使用GBK编码");
          }
          formatAppLog("log", "at pages/data/import.vue:532", "开始读取文件内容，使用", encoding, "编码");
          const fis = new FileInputStream(file);
          const isr = new InputStreamReader(fis, encoding);
          const br = new BufferedReader(isr);
          const sb = new StringBuilder();
          let line = null;
          let lineCount = 0;
          while ((line = br.readLine()) !== null) {
            sb.append(line).append("\n");
            lineCount++;
          }
          try {
            br.close();
            isr.close();
            fis.close();
          } catch (closeError) {
            formatAppLog("error", "at pages/data/import.vue:551", "关闭流失败:", closeError);
          }
          let content = sb.toString();
          formatAppLog("log", "at pages/data/import.vue:555", "使用", encoding, "编码成功读取了", lineCount, "行数据");
          if (fileName.endsWith(".json")) {
            const containsChinese = /[\u4e00-\u9fa5]/.test(content);
            formatAppLog("log", "at pages/data/import.vue:561", "JSON内容" + (containsChinese ? "包含" : "不包含") + "中文字符");
            if (!containsChinese) {
              formatAppLog("log", "at pages/data/import.vue:565", "JSON内容不包含中文，尝试使用GBK编码重新读取");
              try {
                const fis2 = new FileInputStream(file);
                const isr2 = new InputStreamReader(fis2, "GBK");
                const br2 = new BufferedReader(isr2);
                const sb2 = new StringBuilder();
                let line2 = null;
                let lineCount2 = 0;
                while ((line2 = br2.readLine()) !== null) {
                  sb2.append(line2).append("\n");
                  lineCount2++;
                }
                try {
                  br2.close();
                  isr2.close();
                  fis2.close();
                } catch (closeError) {
                  formatAppLog("error", "at pages/data/import.vue:586", "关闭流失败:", closeError);
                }
                const content2 = sb2.toString();
                const containsChinese2 = /[\u4e00-\u9fa5]/.test(content2);
                formatAppLog("log", "at pages/data/import.vue:593", "GBK编码的JSON内容" + (containsChinese2 ? "包含" : "不包含") + "中文字符");
                if (containsChinese2) {
                  content = content2;
                  formatAppLog("log", "at pages/data/import.vue:598", "使用GBK编码的JSON内容");
                }
              } catch (e) {
                formatAppLog("error", "at pages/data/import.vue:601", "使用GBK编码重新读取失败:", e);
              }
            }
          }
          this.fileContent = content;
          formatAppLog("log", "at pages/data/import.vue:608", "文件内容读取完成");
          this.parseImportData();
        } catch (e) {
          formatAppLog("error", "at pages/data/import.vue:611", "读取文件失败:", e);
          this.importError = "读取文件失败: " + (e.message || e);
          this.isLoading = false;
          uni.showModal({
            title: "读取文件失败",
            content: e.message || String(e),
            showCancel: false
          });
        }
      },
      // 读取文件内容
      readFile(filePath) {
        this.isLoading = true;
        this.importError = "";
        formatAppLog("log", "at pages/data/import.vue:628", "开始读取文件:", filePath);
        const isJsonFile = filePath.toLowerCase().endsWith(".json");
        const initialEncoding = isJsonFile ? "utf8" : "gbk";
        formatAppLog("log", "at pages/data/import.vue:634", "文件类型:", isJsonFile ? "JSON" : "CSV", "，初始编码:", initialEncoding);
        uni.getFileSystemManager().readFile({
          filePath,
          encoding: initialEncoding,
          success: (res) => {
            let content = res.data;
            formatAppLog("log", "at pages/data/import.vue:642", `使用 ${initialEncoding} 编码成功读取文件`);
            if (isJsonFile) {
              const containsChinese = /[\u4e00-\u9fa5]/.test(content);
              formatAppLog("log", "at pages/data/import.vue:648", "JSON内容" + (containsChinese ? "包含" : "不包含") + "中文字符");
              if (!containsChinese) {
                const alternativeEncoding = initialEncoding === "utf8" ? "gbk" : "utf8";
                formatAppLog("log", "at pages/data/import.vue:653", `JSON内容不包含中文，尝试使用 ${alternativeEncoding} 编码重新读取`);
                uni.getFileSystemManager().readFile({
                  filePath,
                  encoding: alternativeEncoding,
                  success: (res2) => {
                    const content2 = res2.data;
                    const containsChinese2 = /[\u4e00-\u9fa5]/.test(content2);
                    formatAppLog("log", "at pages/data/import.vue:663", `${alternativeEncoding} 编码的JSON内容` + (containsChinese2 ? "包含" : "不包含") + "中文字符");
                    if (containsChinese2) {
                      content = content2;
                      formatAppLog("log", "at pages/data/import.vue:668", `使用 ${alternativeEncoding} 编码的JSON内容`);
                    }
                    this.fileContent = content;
                    this.parseImportData();
                  },
                  fail: (err2) => {
                    formatAppLog("error", "at pages/data/import.vue:675", `使用 ${alternativeEncoding} 编码读取失败:`, err2);
                    this.fileContent = content;
                    this.parseImportData();
                  }
                });
              } else {
                this.fileContent = content;
                this.parseImportData();
              }
            } else {
              this.fileContent = content;
              this.parseImportData();
            }
          },
          fail: (err) => {
            formatAppLog("error", "at pages/data/import.vue:693", `使用 ${initialEncoding} 编码读取失败，尝试另一种编码:`, err);
            const alternativeEncoding = initialEncoding === "utf8" ? "gbk" : "utf8";
            uni.getFileSystemManager().readFile({
              filePath,
              encoding: alternativeEncoding,
              success: (res) => {
                this.fileContent = res.data;
                formatAppLog("log", "at pages/data/import.vue:703", `使用 ${alternativeEncoding} 编码成功读取文件`);
                this.parseImportData();
              },
              fail: (err2) => {
                formatAppLog("error", "at pages/data/import.vue:707", `使用 ${alternativeEncoding} 编码也读取失败:`, err2);
                this.importError = "读取文件失败: " + (err2.errMsg || err2);
                this.isLoading = false;
                uni.showModal({
                  title: "读取文件失败",
                  content: "无法读取文件，请确保文件格式正确",
                  showCancel: false
                });
              }
            });
          }
        });
      },
      // 解析导入数据
      parseImportData() {
        this.isLoading = true;
        this.importError = "";
        this.previewData = [];
        this.previewColumns = [];
        try {
          const content = this.selectedMethod === "file" ? this.fileContent : this.importText;
          if (!content) {
            this.isLoading = false;
            return;
          }
          if (this.selectedMethod === "file" && this.selectedFile) {
            const fileName = this.selectedFile.toLowerCase();
            if (fileName.endsWith(".csv")) {
              this.selectedFormat = "csv";
            } else if (fileName.endsWith(".json")) {
              this.selectedFormat = "json";
            }
          }
          switch (this.selectedFormat) {
            case "csv":
              this.parseCSV(content);
              break;
            case "json":
              this.parseJSON(content);
              break;
            default:
              if (content.trim().startsWith("{") || content.trim().startsWith("[")) {
                this.selectedFormat = "json";
                this.parseJSON(content);
              } else {
                this.selectedFormat = "csv";
                this.parseCSV(content);
              }
              break;
          }
        } catch (e) {
          formatAppLog("error", "at pages/data/import.vue:768", "解析数据失败", e);
          this.importError = "解析数据失败: " + (e.message || e);
        } finally {
          this.isLoading = false;
        }
      },
      // 解析CSV格式
      parseCSV(content) {
        formatAppLog("log", "at pages/data/import.vue:777", "开始解析CSV数据");
        if (content.charCodeAt(0) === 65279) {
          formatAppLog("log", "at pages/data/import.vue:781", "检测到BOM标记，移除BOM");
          content = content.slice(1);
        }
        const lines = content.split(/\r\n|\r|\n/).filter((line) => line.trim());
        formatAppLog("log", "at pages/data/import.vue:787", `CSV文件包含 ${lines.length} 行数据`);
        if (lines.length === 0) {
          this.importError = "CSV文件为空";
          return;
        }
        const containsChinese = /[\u4e00-\u9fa5]/.test(content);
        formatAppLog("log", "at pages/data/import.vue:796", "CSV内容" + (containsChinese ? "包含" : "不包含") + "中文字符");
        const headers = this.parseCSVLine(lines[0]);
        formatAppLog("log", "at pages/data/import.vue:800", "解析到的表头:", headers);
        const columnTypes = {};
        for (const column of this.columns) {
          columnTypes[column.name] = column.type;
        }
        formatAppLog("log", "at pages/data/import.vue:807", "表列类型信息:", columnTypes);
        const autoGeneratedFields = ["id", "createTime", "updateTime", "rowid"];
        formatAppLog("log", "at pages/data/import.vue:811", "自动生成字段:", autoGeneratedFields);
        const filteredHeaders = headers.filter((header) => !autoGeneratedFields.includes(header));
        formatAppLog("log", "at pages/data/import.vue:815", "过滤后的表头:", filteredHeaders);
        this.previewColumns = filteredHeaders;
        const data = [];
        for (let i = 1; i < lines.length; i++) {
          if (!lines[i].trim())
            continue;
          const values = this.parseCSVLine(lines[i]);
          const row = {};
          for (let j = 0; j < headers.length; j++) {
            const header = headers[j];
            if (autoGeneratedFields.includes(header)) {
              continue;
            }
            let value = j < values.length ? values[j] : "";
            if (columnTypes[header]) {
              const type = columnTypes[header].toLowerCase();
              if (type.includes("int") || type.includes("float") || type.includes("double") || type.includes("decimal") || type.includes("number")) {
                if (value === "") {
                  value = null;
                } else {
                  const num = Number(value);
                  if (!isNaN(num)) {
                    value = num;
                  }
                }
              } else if (type.includes("bool")) {
                if (value.toLowerCase() === "true" || value === "1") {
                  value = 1;
                } else if (value.toLowerCase() === "false" || value === "0") {
                  value = 0;
                } else if (value === "") {
                  value = null;
                }
              } else if (type.includes("date") || type.includes("time")) {
                if (value === "") {
                  value = null;
                }
              }
            }
            row[header] = value;
          }
          data.push(row);
          if (i <= 3) {
            formatAppLog("log", "at pages/data/import.vue:882", `第 ${i} 行数据:`, JSON.stringify(row));
          }
        }
        formatAppLog("log", "at pages/data/import.vue:886", `成功解析 ${data.length} 行数据`);
        this.previewData = data;
      },
      // 解析CSV行
      parseCSVLine(line) {
        formatAppLog("log", "at pages/data/import.vue:893", "解析CSV行:", line.substring(0, 30) + (line.length > 30 ? "..." : ""));
        const result = [];
        let inQuotes = false;
        let currentValue = "";
        for (let i = 0; i < line.length; i++) {
          const char = line[i];
          if (char === '"') {
            inQuotes = !inQuotes;
          } else if (char === "," && !inQuotes) {
            result.push(currentValue);
            currentValue = "";
          } else {
            currentValue += char;
          }
        }
        result.push(currentValue);
        for (let i = 0; i < result.length; i++) {
          let value = result[i];
          if (value.startsWith('"') && value.endsWith('"')) {
            value = value.substring(1, value.length - 1);
          }
          value = value.trim();
          result[i] = value;
        }
        return result;
      },
      // 解析JSON格式
      parseJSON(content) {
        try {
          formatAppLog("log", "at pages/data/import.vue:941", "开始解析JSON数据");
          if (content.charCodeAt(0) === 65279) {
            formatAppLog("log", "at pages/data/import.vue:945", "检测到BOM标记，移除BOM");
            content = content.slice(1);
          }
          const containsChinese = /[\u4e00-\u9fa5]/.test(content);
          formatAppLog("log", "at pages/data/import.vue:951", "JSON内容" + (containsChinese ? "包含" : "不包含") + "中文字符");
          let data;
          try {
            data = JSON.parse(content);
            formatAppLog("log", "at pages/data/import.vue:957", "JSON解析成功");
          } catch (parseError) {
            formatAppLog("error", "at pages/data/import.vue:959", "标准JSON解析失败，尝试修复:", parseError);
            try {
              const TextEncoder = plus.android.importClass("java.nio.charset.Charset");
              const charset = TextEncoder.forName("UTF-8");
              const bytes = [];
              for (let i = 0; i < content.length; i++) {
                bytes.push(content.charCodeAt(i) & 255);
              }
              const ByteBuffer = plus.android.importClass("java.nio.ByteBuffer");
              const buffer = ByteBuffer.wrap(bytes);
              const decodedContent = charset.decode(buffer).toString();
              formatAppLog("log", "at pages/data/import.vue:980", "尝试使用UTF-8重新解码内容");
              data = JSON.parse(decodedContent);
              formatAppLog("log", "at pages/data/import.vue:984", "使用重新解码的内容解析JSON成功");
            } catch (decodeError) {
              formatAppLog("error", "at pages/data/import.vue:986", "重新解码失败，尝试其他方法:", decodeError);
              try {
                let fixedContent = content.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
                  return String.fromCharCode(parseInt(hex, 16));
                }).replace(/\\"/g, '"').replace(/\\\\/g, "\\");
                data = JSON.parse(fixedContent);
                formatAppLog("log", "at pages/data/import.vue:1000", "使用手动修复的内容解析JSON成功");
              } catch (fixError) {
                formatAppLog("error", "at pages/data/import.vue:1002", "手动修复失败，抛出原始错误:", fixError);
                throw parseError;
              }
            }
          }
          if (!Array.isArray(data)) {
            if (typeof data === "object" && data !== null) {
              formatAppLog("log", "at pages/data/import.vue:1012", "JSON数据是对象，尝试转换为数组");
              data = [data];
            } else {
              this.importError = "JSON数据必须是数组格式";
              return;
            }
          }
          if (data.length === 0) {
            this.importError = "JSON数据为空";
            return;
          }
          formatAppLog("log", "at pages/data/import.vue:1025", `JSON数据包含 ${data.length} 条记录`);
          const columnTypes = {};
          for (const column of this.columns) {
            columnTypes[column.name] = column.type;
          }
          formatAppLog("log", "at pages/data/import.vue:1032", "表列类型信息:", columnTypes);
          const autoGeneratedFields = ["id", "createTime", "updateTime", "rowid"];
          formatAppLog("log", "at pages/data/import.vue:1036", "自动生成字段:", autoGeneratedFields);
          const allColumns = /* @__PURE__ */ new Set();
          for (const row of data) {
            Object.keys(row).forEach((key) => allColumns.add(key));
          }
          formatAppLog("log", "at pages/data/import.vue:1043", "所有可能的列:", Array.from(allColumns));
          const filteredColumns = Array.from(allColumns).filter((col) => !autoGeneratedFields.includes(col));
          formatAppLog("log", "at pages/data/import.vue:1047", "过滤后的列:", filteredColumns);
          for (let i = 0; i < data.length; i++) {
            const row = data[i];
            const filteredRow = {};
            for (const key of Object.keys(row)) {
              if (autoGeneratedFields.includes(key)) {
                continue;
              }
              let value = row[key];
              if (typeof value === "string") {
                if (/\\u[0-9a-fA-F]{4}/.test(value)) {
                  value = value.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
                    return String.fromCharCode(parseInt(hex, 16));
                  });
                  formatAppLog("log", "at pages/data/import.vue:1071", `修复字段 ${key} 的Unicode编码:`, value);
                }
                if (this.containsGarbledText(value)) {
                  formatAppLog("log", "at pages/data/import.vue:1076", `字段 ${key} 可能包含乱码:`, value);
                  try {
                    const TextEncoder = plus.android.importClass("java.nio.charset.Charset");
                    const charset = TextEncoder.forName("UTF-8");
                    const bytes = [];
                    for (let i2 = 0; i2 < value.length; i2++) {
                      bytes.push(value.charCodeAt(i2) & 255);
                    }
                    const ByteBuffer = plus.android.importClass("java.nio.ByteBuffer");
                    const buffer = ByteBuffer.wrap(bytes);
                    const decodedValue = charset.decode(buffer).toString();
                    if (decodedValue !== value) {
                      value = decodedValue;
                      formatAppLog("log", "at pages/data/import.vue:1100", `修复字段 ${key} 的乱码:`, value);
                    }
                  } catch (decodeError) {
                    formatAppLog("error", "at pages/data/import.vue:1103", `修复字段 ${key} 的乱码失败:`, decodeError);
                  }
                }
              }
              if (columnTypes[key]) {
                const type = columnTypes[key].toLowerCase();
                if (type.includes("int") || type.includes("float") || type.includes("double") || type.includes("decimal") || type.includes("number")) {
                  if (value === "" || value === null) {
                    value = null;
                  } else if (typeof value === "string") {
                    const num = Number(value);
                    if (!isNaN(num)) {
                      value = num;
                    }
                  }
                } else if (type.includes("bool")) {
                  if (typeof value === "string") {
                    if (value.toLowerCase() === "true" || value === "1") {
                      value = 1;
                    } else if (value.toLowerCase() === "false" || value === "0") {
                      value = 0;
                    } else if (value === "") {
                      value = null;
                    }
                  } else if (typeof value === "boolean") {
                    value = value ? 1 : 0;
                  }
                } else if (type.includes("date") || type.includes("time")) {
                  if (value === "" || value === null) {
                    value = null;
                  }
                }
              }
              filteredRow[key] = value;
            }
            data[i] = filteredRow;
            if (i < 3) {
              formatAppLog("log", "at pages/data/import.vue:1159", `第 ${i + 1} 条JSON数据:`, JSON.stringify(filteredRow));
            }
          }
          this.previewColumns = filteredColumns;
          this.previewData = data;
          formatAppLog("log", "at pages/data/import.vue:1165", `成功解析 ${data.length} 条JSON数据`);
        } catch (e) {
          formatAppLog("error", "at pages/data/import.vue:1167", "JSON解析错误:", e);
          this.importError = "JSON格式错误: " + e.message;
        }
      },
      // 根据当前格式获取占位符文本
      getPlaceholderByFormat() {
        switch (this.selectedFormat) {
          case "csv":
            return "请粘贴CSV格式数据，第一行应为列名...";
          case "json":
            return "请粘贴JSON格式数据，应为对象数组...";
          default:
            return "请粘贴CSV或JSON格式的数据...";
        }
      },
      // 获取格式示例
      getFormatExample() {
        const columnNames = this.columns.map((col) => col.name);
        switch (this.selectedFormat) {
          case "csv":
            return columnNames.join(",") + "\n示例值1," + "示例值2,".repeat(columnNames.length - 2) + "示例值3\n示例值4," + "示例值5,".repeat(columnNames.length - 2) + "示例值6";
          case "json":
            const obj1 = {};
            const obj2 = {};
            columnNames.forEach((col, index) => {
              obj1[col] = `示例值${index + 1}`;
              obj2[col] = `示例值${index + columnNames.length + 1}`;
            });
            return JSON.stringify([obj1, obj2], null, 2);
          default:
            return columnNames.join(",") + "\n示例值1," + "示例值2,".repeat(columnNames.length - 2) + "示例值3\n示例值4," + "示例值5,".repeat(columnNames.length - 2) + "示例值6";
        }
      },
      // 使用示例数据
      useExample() {
        this.importText = this.getFormatExample();
      },
      // 检测文本是否包含乱码
      containsGarbledText(text) {
        if (!text)
          return false;
        const garbledPatterns = [
          // 常见乱码字符组合
          /ï¿½/g,
          // UTF-8字符在ISO-8859-1中的表示
          /â€œ/g,
          // 引号在错误编码下的表示
          /â€/g,
          // 引号在错误编码下的表示
          /Ã¢â‚¬â„¢/g,
          // 常见乱码组合
          /Â/g,
          // 常见乱码字符
          // 检查中文是否正常
          // 如果文本中有中文字符，但没有常见的中文标点，可能是乱码
          /[\u4e00-\u9fa5]{3,}[，。？！；：""''（）【】《》]/
        ];
        for (const pattern of garbledPatterns.slice(0, 5)) {
          if (pattern.test(text)) {
            return true;
          }
        }
        const hasChinese = /[\u4e00-\u9fa5]{3,}/.test(text);
        const hasChinesePunctuation = /[，。？！；：""''（）【】《》]/.test(text);
        if (hasChinese && !hasChinesePunctuation && text.length > 20) {
          let chineseCount = 0;
          for (let i = 0; i < text.length; i++) {
            if (/[\u4e00-\u9fa5]/.test(text[i])) {
              chineseCount++;
            }
          }
          const chineseRatio = chineseCount / text.length;
          if (chineseRatio < 0.3) {
            return true;
          }
        }
        return false;
      },
      // 导入数据
      async importData() {
        if (this.previewData.length === 0) {
          uni.showToast({
            title: "没有数据可导入",
            icon: "none"
          });
          return;
        }
        uni.showModal({
          title: "确认导入",
          content: `确定要导入 ${this.previewData.length} 条数据到表 ${this.tableName} 吗？`,
          success: async (res) => {
            if (res.confirm) {
              await this.performImport();
            }
          }
        });
      },
      // 执行导入
      async performImport() {
        this.isImporting = true;
        try {
          const totalRecords = this.previewData.length;
          let successCount = 0;
          let failCount = 0;
          let lastError = null;
          showLoading(`导入中... 0/${totalRecords}`);
          formatAppLog("log", "at pages/data/import.vue:1306", "开始导入数据，共", totalRecords, "条记录");
          for (const row of this.previewData) {
            if (this.tableName === "articles") {
              if (!row.fileName) {
                row.fileName = "待完善";
                formatAppLog("log", "at pages/data/import.vue:1315", "为必填字段 fileName 添加默认值: 待完善");
              }
              if (!row.articleType) {
                row.articleType = "待完善";
                formatAppLog("log", "at pages/data/import.vue:1321", "为必填字段 articleType 添加默认值: 待完善");
              }
              if (!row.articleContent) {
                row.articleContent = "待完善";
                formatAppLog("log", "at pages/data/import.vue:1327", "为必填字段 articleContent 添加默认值: 待完善");
              }
              if (!row.keywords) {
                row.keywords = "待完善";
                formatAppLog("log", "at pages/data/import.vue:1333", "为必填字段 keywords 添加默认值: 待完善");
              }
            } else if (this.tableName === "documents") {
              if (!row.fileName) {
                row.fileName = "待完善";
                formatAppLog("log", "at pages/data/import.vue:1341", "为必填字段 fileName 添加默认值: 待完善");
              }
              if (!row.category) {
                row.category = "待完善";
                formatAppLog("log", "at pages/data/import.vue:1347", "为必填字段 category 添加默认值: 待完善");
              }
              if (!row.documentNumber) {
                row.documentNumber = "待完善";
                formatAppLog("log", "at pages/data/import.vue:1353", "为必填字段 documentNumber 添加默认值: 待完善");
              }
            }
          }
          const incompleteArticles = [];
          const incompleteDocuments = [];
          const autoCreatedDocuments = [];
          for (let i = 0; i < this.previewData.length; i++) {
            const row = this.previewData[i];
            try {
              const data = {};
              for (const key in row) {
                if (["id", "createTime", "updateTime", "rowid"].includes(key)) {
                  continue;
                }
                data[key] = row[key];
              }
              formatAppLog("log", "at pages/data/import.vue:1377", `准备插入第 ${i + 1} 行数据:`, JSON.stringify(data));
              let hasIncompleteFields = false;
              if (this.tableName === "articles") {
                if (data.fileName === "待完善" || data.articleType === "待完善" || data.articleContent === "待完善" || data.keywords === "待完善") {
                  hasIncompleteFields = true;
                  incompleteArticles.push(data.fileName || `记录${i + 1}`);
                }
              } else if (this.tableName === "documents") {
                if (data.fileName === "待完善" || data.category === "待完善" || data.documentNumber === "待完善") {
                  hasIncompleteFields = true;
                  incompleteDocuments.push(data.fileName || `记录${i + 1}`);
                }
              }
              if (this.tableName === "articles" && data.fileName) {
                const fileNameExists = await checkFileNameExists(data.fileName);
                if (!fileNameExists) {
                  autoCreatedDocuments.push(data.fileName);
                }
              }
              const result = await insertData(this.tableName, data);
              if (result) {
                formatAppLog("log", "at pages/data/import.vue:1408", `第 ${i + 1} 行数据插入成功，结果:`, result);
                successCount++;
                const progress = successCount + failCount;
                showLoading(`导入中... ${progress}/${totalRecords}`);
              } else {
                formatAppLog("error", "at pages/data/import.vue:1415", `第 ${i + 1} 行数据插入失败`);
                failCount++;
                const progress = successCount + failCount;
                showLoading(`导入中... ${progress}/${totalRecords}`);
              }
            } catch (e) {
              formatAppLog("error", "at pages/data/import.vue:1423", `插入第 ${i + 1} 行数据失败:`, e);
              formatAppLog("error", "at pages/data/import.vue:1424", `完整错误信息:`, JSON.stringify(e));
              lastError = e;
              failCount++;
              const progress = successCount + failCount;
              showLoading(`导入中... ${progress}/${totalRecords}`);
            }
          }
          hideLoading();
          const uniqueIncompleteArticles = [...new Set(incompleteArticles)];
          const uniqueIncompleteDocuments = [...new Set(incompleteDocuments)];
          const uniqueAutoCreatedDocuments = [...new Set(autoCreatedDocuments)];
          let resultContent = `成功导入 ${successCount} 条数据，失败 ${failCount} 条

`;
          if (this.tableName === "articles") {
            resultContent += `待完善条文: ${uniqueIncompleteArticles.length} 条
`;
            if (uniqueAutoCreatedDocuments.length > 0) {
              resultContent += `自动创建的文件记录: ${uniqueAutoCreatedDocuments.length} 条

`;
              if (uniqueAutoCreatedDocuments.length <= 5) {
                resultContent += `自动创建的文件名:
${uniqueAutoCreatedDocuments.join("\n")}
`;
              } else {
                resultContent += `自动创建的文件名(前5个):
${uniqueAutoCreatedDocuments.slice(0, 5).join("\n")}...
`;
              }
            }
          } else if (this.tableName === "documents") {
            resultContent += `待完善文件信息: ${uniqueIncompleteDocuments.length} 条
`;
          }
          uni.showModal({
            title: "导入完成",
            content: resultContent,
            confirmText: "复制结果",
            cancelText: "确定",
            success: (res) => {
              if (res.confirm) {
                uni.setClipboardData({
                  data: resultContent,
                  success: () => {
                    uni.showToast({
                      title: "已复制到剪贴板",
                      icon: "success"
                    });
                    setTimeout(() => {
                      uni.navigateBack();
                    }, 1e3);
                  }
                });
              } else {
                uni.navigateBack();
              }
            }
          });
        } catch (e) {
          hideLoading();
          formatAppLog("error", "at pages/data/import.vue:1493", "导入数据失败", e);
          formatAppLog("error", "at pages/data/import.vue:1494", "完整错误信息:", JSON.stringify(e));
          let errorMessage = e.message || String(e);
          let friendlyMessage = "导入数据失败";
          if (errorMessage.includes("has no column")) {
            const match = errorMessage.match(/has no column named (\w+)/);
            if (match && match.length >= 2) {
              const columnName = match[1];
              if (columnName === "category") {
                friendlyMessage = `导入失败: 条文表(articles)没有 "category" 列，请移除此列或使用正确的表`;
              } else {
                friendlyMessage = `导入失败: 表中不存在列 "${columnName}"，请检查导入数据的格式`;
              }
            } else {
              friendlyMessage = "导入失败: 表中不存在某些列，请检查导入数据的格式";
            }
          } else if (errorMessage.includes("NOT NULL constraint")) {
            const match = errorMessage.match(/NOT NULL constraint failed: (\w+)\.(\w+)/);
            if (match && match.length >= 3) {
              match[1];
              const columnName = match[2];
              if (columnName === "articleType") {
                friendlyMessage = `导入失败: 字段 "articleType" 不能为空，请确保导入的数据中包含此字段，值应为预定义的选项之一`;
              } else {
                friendlyMessage = `导入失败: 字段 "${columnName}" 不能为空，请确保导入的数据中包含此字段`;
              }
            } else {
              friendlyMessage = "导入失败: 某些必填字段缺少值，请确保导入的数据包含所有必填字段";
            }
          } else if (errorMessage.includes("FOREIGN KEY constraint")) {
            friendlyMessage = "导入失败: 外键约束错误，请确保引用的数据存在";
          } else if (errorMessage.includes("UNIQUE constraint")) {
            friendlyMessage = "导入失败: 唯一约束错误，可能是尝试插入重复的数据";
          } else if (errorMessage.includes("CHECK constraint")) {
            friendlyMessage = "导入失败: 检查约束错误，请确保数据符合表的约束条件";
          } else {
            friendlyMessage = "导入失败: " + errorMessage;
          }
          uni.showModal({
            title: "导入失败",
            content: friendlyMessage,
            showCancel: false
          });
        } finally {
          this.isImporting = false;
        }
      }
    }
  };
  function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "header-back" }, "返回")
        ]),
        vue.createElementVNode(
          "text",
          { class: "header-title" },
          "导入数据: " + vue.toDisplayString($data.tableName),
          1
          /* TEXT */
        ),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createElementVNode("view", { class: "import-container" }, [
        vue.createCommentVNode(" 导入选项 "),
        vue.createElementVNode("view", { class: "import-options" }, [
          vue.createElementVNode("view", { class: "option-group" }, [
            vue.createElementVNode("text", { class: "option-label" }, "导入格式:"),
            vue.createElementVNode("view", { class: "format-options" }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($data.formats, (format, index) => {
                  return vue.openBlock(), vue.createElementBlock("view", {
                    key: index,
                    class: vue.normalizeClass(["format-option", { "format-option-selected": $data.selectedFormat === format.value }]),
                    onClick: ($event) => $data.selectedFormat = format.value
                  }, [
                    vue.createElementVNode(
                      "text",
                      { class: "format-text" },
                      vue.toDisplayString(format.label),
                      1
                      /* TEXT */
                    )
                  ], 10, ["onClick"]);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ])
          ]),
          vue.createElementVNode("view", { class: "option-group" }, [
            vue.createElementVNode("text", { class: "option-label" }, "导入方式:"),
            vue.createElementVNode("view", { class: "import-methods" }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($data.importMethods, (method, index) => {
                  return vue.openBlock(), vue.createElementBlock("view", {
                    key: index,
                    class: vue.normalizeClass(["method-option", { "method-option-selected": $data.selectedMethod === method.value }]),
                    onClick: ($event) => $data.selectedMethod = method.value
                  }, [
                    vue.createElementVNode(
                      "text",
                      { class: "method-text" },
                      vue.toDisplayString(method.label),
                      1
                      /* TEXT */
                    )
                  ], 10, ["onClick"]);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ])
          ]),
          vue.createCommentVNode(" 文件选择 "),
          $data.selectedMethod === "file" ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "file-select"
          }, [
            vue.createElementVNode("button", {
              class: "file-button",
              onClick: _cache[1] || (_cache[1] = (...args) => $options.selectFile && $options.selectFile(...args))
            }, "选择文件"),
            $data.selectedFile ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "file-name"
              },
              "已选择: " + vue.toDisplayString($data.selectedFile),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ])) : $data.selectedMethod === "text" ? (vue.openBlock(), vue.createElementBlock(
            vue.Fragment,
            { key: 1 },
            [
              vue.createCommentVNode(" 文本输入 "),
              vue.createElementVNode("view", { class: "text-input-container" }, [
                vue.withDirectives(vue.createElementVNode("textarea", {
                  class: "text-input",
                  "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $data.importText = $event),
                  placeholder: $options.getPlaceholderByFormat(),
                  "auto-height": ""
                }, null, 8, ["placeholder"]), [
                  [vue.vModelText, $data.importText]
                ]),
                vue.createElementVNode("view", { class: "format-help" }, [
                  vue.createElementVNode("text", { class: "format-help-title" }, "格式示例:"),
                  vue.createElementVNode(
                    "text",
                    { class: "format-help-text" },
                    vue.toDisplayString($options.getFormatExample()),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode("text", {
                    class: "format-help-action",
                    onClick: _cache[3] || (_cache[3] = (...args) => $options.useExample && $options.useExample(...args))
                  }, "使用示例数据"),
                  vue.createElementVNode("text", { class: "format-help-note" }, "注意: id、createTime、updateTime 字段会自动生成，导入数据中可以省略这些字段"),
                  vue.createElementVNode("text", { class: "format-help-note" }, "注意: 对于条文表(articles)，必填字段包括 fileName、articleType、articleContent、keywords，如果缺少这些字段，将使用默认值"),
                  vue.createElementVNode("text", { class: "format-help-note" }, "注意: articleType 字段值应为预定义的选项之一: 主体行为, 设施设备, 物料, 方法措施, 作业环境, 应急管理, 资料管理")
                ])
              ])
            ],
            2112
            /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
          )) : vue.createCommentVNode("v-if", true)
        ]),
        vue.createCommentVNode(" 预览区域 "),
        vue.createElementVNode("view", { class: "preview-section" }, [
          vue.createElementVNode("view", { class: "preview-header" }, [
            vue.createElementVNode("text", { class: "preview-title" }, "数据预览"),
            $data.previewData.length > 0 ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "preview-count"
              },
              "共 " + vue.toDisplayString($data.previewData.length) + " 条记录",
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ]),
          $data.isLoading ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "loading-tip"
          }, [
            vue.createElementVNode("text", null, "解析中...")
          ])) : $data.previewData.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 1,
            class: "empty-tip"
          }, [
            vue.createElementVNode(
              "text",
              null,
              vue.toDisplayString($data.importError || "暂无预览数据"),
              1
              /* TEXT */
            )
          ])) : (vue.openBlock(), vue.createElementBlock("view", {
            key: 2,
            class: "preview-table"
          }, [
            vue.createCommentVNode(" 表头 "),
            vue.createElementVNode("scroll-view", {
              class: "preview-header-scroll",
              "scroll-x": ""
            }, [
              vue.createElementVNode("view", { class: "preview-row preview-header-row" }, [
                (vue.openBlock(true), vue.createElementBlock(
                  vue.Fragment,
                  null,
                  vue.renderList($data.previewColumns, (column, index) => {
                    return vue.openBlock(), vue.createElementBlock("view", {
                      key: index,
                      class: "preview-cell preview-header-cell"
                    }, [
                      vue.createElementVNode(
                        "text",
                        { class: "preview-header-text" },
                        vue.toDisplayString(column),
                        1
                        /* TEXT */
                      )
                    ]);
                  }),
                  128
                  /* KEYED_FRAGMENT */
                ))
              ])
            ]),
            vue.createCommentVNode(" 表内容 "),
            vue.createElementVNode("scroll-view", {
              class: "preview-body",
              "scroll-x": "",
              "scroll-y": ""
            }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($data.previewData.slice(0, 5), (row, rowIndex) => {
                  return vue.openBlock(), vue.createElementBlock(
                    "view",
                    {
                      key: rowIndex,
                      class: vue.normalizeClass(["preview-row", { "preview-row-even": rowIndex % 2 === 0 }])
                    },
                    [
                      (vue.openBlock(true), vue.createElementBlock(
                        vue.Fragment,
                        null,
                        vue.renderList($data.previewColumns, (column, colIndex) => {
                          return vue.openBlock(), vue.createElementBlock("view", {
                            key: colIndex,
                            class: "preview-cell"
                          }, [
                            vue.createElementVNode(
                              "text",
                              { class: "preview-cell-text" },
                              vue.toDisplayString(row[column] !== void 0 ? row[column] : ""),
                              1
                              /* TEXT */
                            )
                          ]);
                        }),
                        128
                        /* KEYED_FRAGMENT */
                      ))
                    ],
                    2
                    /* CLASS */
                  );
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ]),
            $data.previewData.length > 5 ? (vue.openBlock(), vue.createElementBlock("view", {
              key: 0,
              class: "preview-more"
            }, [
              vue.createElementVNode(
                "text",
                { class: "preview-more-text" },
                "显示前5条记录，共" + vue.toDisplayString($data.previewData.length) + "条",
                1
                /* TEXT */
              )
            ])) : vue.createCommentVNode("v-if", true)
          ]))
        ]),
        vue.createCommentVNode(" 导入按钮 "),
        vue.createElementVNode("view", { class: "import-actions" }, [
          vue.createElementVNode("button", {
            class: "import-button",
            disabled: $data.previewData.length === 0 || $data.isImporting,
            onClick: _cache[4] || (_cache[4] = (...args) => $options.importData && $options.importData(...args))
          }, vue.toDisplayString($data.isImporting ? "导入中..." : "导入数据"), 9, ["disabled"])
        ])
      ])
    ]);
  }
  const PagesDataImport = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render$2], ["__file", "G:/my/safety_management/pages/data/import.vue"]]);
  const _sfc_main$2 = {
    setup() {
      const currentStep = vue.ref(1);
      const selectedFile = vue.ref(null);
      const fileContent = vue.ref("");
      const processingStatus = vue.ref("准备处理文件...");
      const processingProgress = vue.ref(0);
      const processingError = vue.ref("");
      const processingComplete = vue.ref(false);
      const processedItems = vue.ref([]);
      const isImporting = vue.ref(false);
      const importProgress = vue.ref(0);
      const importComplete = vue.ref(false);
      const fileName = vue.ref("");
      const previewItems = vue.computed(() => {
        return processedItems.value.slice(0, 10);
      });
      const goBack = () => {
        uni.navigateBack();
      };
      const selectFile = () => {
        if (uni.getSystemInfoSync().platform === "android") {
          selectFileFromDownload();
        } else {
          uni.showModal({
            title: "提示",
            content: "当前平台暂不支持直接选择文件，请使用Android设备。",
            showCancel: false
          });
        }
      };
      const selectFileFromDownload = async () => {
        try {
          await requestStoragePermission();
          const Environment = plus.android.importClass("android.os.Environment");
          const File = plus.android.importClass("java.io.File");
          let downloadPath = "";
          try {
            const downloadDir2 = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            if (downloadDir2) {
              downloadPath = downloadDir2.getAbsolutePath();
              formatAppLog("log", "at pages/data/smart-import.vue:137", "使用公共目录获取Download路径:", downloadPath);
            }
          } catch (e) {
            formatAppLog("error", "at pages/data/smart-import.vue:140", "获取公共Download目录失败:", e);
          }
          if (!downloadPath) {
            try {
              const externalStorageDir = Environment.getExternalStorageDirectory();
              if (externalStorageDir) {
                downloadPath = externalStorageDir.getAbsolutePath() + "/Download";
                formatAppLog("log", "at pages/data/smart-import.vue:150", "使用外部存储根目录获取Download路径:", downloadPath);
              }
            } catch (e) {
              formatAppLog("error", "at pages/data/smart-import.vue:153", "获取外部存储根目录失败:", e);
            }
          }
          if (!downloadPath) {
            try {
              const context = plus.android.runtimeMainActivity();
              const fileDir = context.getExternalFilesDir(null);
              if (fileDir) {
                downloadPath = fileDir.getAbsolutePath();
                formatAppLog("log", "at pages/data/smart-import.vue:165", "使用应用私有目录:", downloadPath);
              }
            } catch (e) {
              formatAppLog("error", "at pages/data/smart-import.vue:168", "获取应用私有目录失败:", e);
            }
          }
          if (!downloadPath) {
            throw new Error("无法获取Download目录路径");
          }
          formatAppLog("log", "at pages/data/smart-import.vue:177", "最终使用的Download目录路径:", downloadPath);
          const downloadDir = new File(downloadPath);
          if (!downloadDir.exists()) {
            downloadDir.mkdirs();
            formatAppLog("log", "at pages/data/smart-import.vue:183", "创建Download目录:", downloadPath);
          }
          const mdFiles = listMdFiles(downloadPath);
          if (mdFiles.length === 0) {
            uni.showModal({
              title: "未找到MD文件",
              content: "Download文件夹中没有找到.md文件",
              showCancel: false
            });
            return;
          }
          uni.showActionSheet({
            itemList: mdFiles.map((file) => file.name),
            success: (res) => {
              const selectedIndex = res.tapIndex;
              selectedFile.value = mdFiles[selectedIndex];
              fileName.value = selectedFile.value.name.replace(".md", "");
              formatAppLog("log", "at pages/data/smart-import.vue:205", "已选择文件:", selectedFile.value);
            }
          });
        } catch (error) {
          formatAppLog("error", "at pages/data/smart-import.vue:209", "选择文件失败:", error);
          uni.showToast({
            title: "选择文件失败: " + error.message,
            icon: "none"
          });
        }
      };
      const listMdFiles = (dirPath) => {
        try {
          formatAppLog("log", "at pages/data/smart-import.vue:220", "开始列出目录中的MD文件:", dirPath);
          const File = plus.android.importClass("java.io.File");
          const dir = new File(dirPath);
          if (!dir.exists()) {
            formatAppLog("error", "at pages/data/smart-import.vue:227", "目录不存在:", dirPath);
            return [];
          }
          if (!dir.isDirectory()) {
            formatAppLog("error", "at pages/data/smart-import.vue:233", "路径不是目录:", dirPath);
            return [];
          }
          formatAppLog("log", "at pages/data/smart-import.vue:237", "目录存在且有效:", dirPath);
          const files = dir.listFiles();
          formatAppLog("log", "at pages/data/smart-import.vue:241", "目录中的文件数量:", files ? files.length : 0);
          const mdFiles = [];
          if (files) {
            for (let i = 0; i < files.length; i++) {
              try {
                const file = files[i];
                const fileName2 = file.getName();
                const isFile = file.isFile();
                const isDirectory = file.isDirectory();
                formatAppLog("log", "at pages/data/smart-import.vue:252", `文件 ${i + 1}/${files.length}: ${fileName2}, 是文件: ${isFile}, 是目录: ${isDirectory}`);
                if (isFile && fileName2.toLowerCase().endsWith(".md")) {
                  const filePath = file.getAbsolutePath();
                  const fileSize = file.length();
                  formatAppLog("log", "at pages/data/smart-import.vue:258", `找到MD文件: ${fileName2}, 路径: ${filePath}, 大小: ${fileSize} 字节`);
                  mdFiles.push({
                    name: fileName2,
                    path: filePath,
                    size: fileSize
                  });
                }
              } catch (fileError) {
                formatAppLog("error", "at pages/data/smart-import.vue:267", "处理文件时出错:", fileError);
              }
            }
          } else {
            formatAppLog("warn", "at pages/data/smart-import.vue:271", "目录中没有文件或无法访问:", dirPath);
          }
          formatAppLog("log", "at pages/data/smart-import.vue:274", `共找到 ${mdFiles.length} 个MD文件`);
          return mdFiles;
        } catch (error) {
          formatAppLog("error", "at pages/data/smart-import.vue:277", "列出MD文件失败:", error);
          return [];
        }
      };
      const requestStoragePermission = () => {
        return new Promise((resolve, reject) => {
          formatAppLog("log", "at pages/data/smart-import.vue:285", "开始请求存储权限");
          const permissions = [
            "android.permission.READ_EXTERNAL_STORAGE",
            "android.permission.WRITE_EXTERNAL_STORAGE"
          ];
          try {
            const Build = plus.android.importClass("android.os.Build");
            const sdkInt = Build.VERSION.SDK_INT;
            formatAppLog("log", "at pages/data/smart-import.vue:297", "Android SDK版本:", sdkInt);
            if (sdkInt >= 30) {
              formatAppLog("log", "at pages/data/smart-import.vue:301", "Android 11及以上版本，需要特殊处理");
              formatAppLog("warn", "at pages/data/smart-import.vue:305", "Android 11+设备将使用应用专属存储空间");
            }
          } catch (e) {
            formatAppLog("error", "at pages/data/smart-import.vue:308", "检查Android版本失败:", e);
          }
          formatAppLog("log", "at pages/data/smart-import.vue:311", "请求以下权限:", permissions.join(", "));
          plus.android.requestPermissions(
            permissions,
            function(resultObj) {
              formatAppLog("log", "at pages/data/smart-import.vue:316", "权限请求结果:", JSON.stringify(resultObj));
              if (resultObj.granted && resultObj.granted.length > 0) {
                formatAppLog("log", "at pages/data/smart-import.vue:319", "已授予权限:", resultObj.granted.join(", "));
                resolve();
              } else {
                formatAppLog("error", "at pages/data/smart-import.vue:322", "未授予所有请求的权限");
                if (resultObj.granted && resultObj.granted.length > 0) {
                  formatAppLog("warn", "at pages/data/smart-import.vue:326", "尝试使用部分权限继续操作");
                  resolve();
                } else {
                  reject(new Error("未授予任何存储权限，无法访问文件"));
                }
              }
            },
            function(error) {
              formatAppLog("error", "at pages/data/smart-import.vue:334", "请求权限时出错:", error);
              reject(error);
            }
          );
        });
      };
      const readFileContent = async () => {
        if (!selectedFile.value) {
          uni.showToast({
            title: "请先选择文件",
            icon: "none"
          });
          return;
        }
        try {
          processingStatus.value = "正在读取文件...";
          processingProgress.value = 5;
          formatAppLog("log", "at pages/data/smart-import.vue:354", "开始读取文件:", selectedFile.value.path);
          const File = plus.android.importClass("java.io.File");
          const FileInputStream = plus.android.importClass("java.io.FileInputStream");
          const BufferedReader = plus.android.importClass("java.io.BufferedReader");
          const InputStreamReader = plus.android.importClass("java.io.InputStreamReader");
          const StringBuilder = plus.android.importClass("java.lang.StringBuilder");
          const file = new File(selectedFile.value.path);
          if (!file.exists()) {
            throw new Error("文件不存在: " + selectedFile.value.path);
          }
          formatAppLog("log", "at pages/data/smart-import.vue:369", "文件存在，大小:", file.length(), "字节");
          let fis = null;
          let isr = null;
          let br = null;
          try {
            fis = new FileInputStream(file);
            formatAppLog("log", "at pages/data/smart-import.vue:378", "文件输入流创建成功");
            isr = new InputStreamReader(fis, "UTF-8");
            formatAppLog("log", "at pages/data/smart-import.vue:381", "输入流读取器创建成功");
            br = new BufferedReader(isr);
            formatAppLog("log", "at pages/data/smart-import.vue:384", "缓冲读取器创建成功");
            const sb = new StringBuilder();
            let line = null;
            let lineCount = 0;
            formatAppLog("log", "at pages/data/smart-import.vue:390", "开始逐行读取文件内容");
            while ((line = br.readLine()) !== null) {
              sb.append(line).append("\n");
              lineCount++;
              if (lineCount % 100 === 0) {
                processingProgress.value = 5 + lineCount / 1e3;
                processingStatus.value = `正在读取文件...已读取 ${lineCount} 行`;
              }
            }
            fileContent.value = sb.toString();
            formatAppLog("log", "at pages/data/smart-import.vue:403", "文件内容读取成功，总行数:", lineCount, "总字符数:", fileContent.value.length);
            currentStep.value = 2;
            processFileContent();
          } finally {
            formatAppLog("log", "at pages/data/smart-import.vue:410", "关闭所有流");
            if (br) {
              try {
                br.close();
              } catch (e) {
                formatAppLog("error", "at pages/data/smart-import.vue:412", "关闭BufferedReader失败:", e);
              }
            }
            if (isr) {
              try {
                isr.close();
              } catch (e) {
                formatAppLog("error", "at pages/data/smart-import.vue:415", "关闭InputStreamReader失败:", e);
              }
            }
            if (fis) {
              try {
                fis.close();
              } catch (e) {
                formatAppLog("error", "at pages/data/smart-import.vue:418", "关闭FileInputStream失败:", e);
              }
            }
          }
        } catch (error) {
          formatAppLog("error", "at pages/data/smart-import.vue:422", "读取文件失败:", error);
          processingError.value = "读取文件失败: " + error.message;
          uni.showModal({
            title: "读取文件失败",
            content: `无法读取文件: ${selectedFile.value.path}

错误信息: ${error.message}`,
            showCancel: false
          });
        }
      };
      const processFileContent = async () => {
        try {
          processingStatus.value = "正在分析文件内容...";
          processingProgress.value = 10;
          const content = fileContent.value;
          const sections = splitContentIntoSections(content);
          processingStatus.value = `文件已分割为 ${sections.length} 个部分，开始处理...`;
          processingProgress.value = 20;
          const results = [];
          for (let i = 0; i < sections.length; i++) {
            processingStatus.value = `正在处理第 ${i + 1}/${sections.length} 部分...`;
            processingProgress.value = 20 + i / sections.length * 60;
            const section = sections[i];
            if (section.trim()) {
              const sectionResults = await processSection(section);
              if (sectionResults && sectionResults.length > 0) {
                results.push(...sectionResults);
              }
            }
          }
          processedItems.value = results;
          processingStatus.value = `处理完成，共生成 ${results.length} 条数据`;
          processingProgress.value = 100;
          processingComplete.value = true;
        } catch (error) {
          formatAppLog("error", "at pages/data/smart-import.vue:467", "处理文件内容失败:", error);
          processingError.value = "处理文件内容失败: " + error.message;
        }
      };
      const splitContentIntoSections = (content) => {
        const maxSectionLength = 4e3;
        const lines = content.split("\n");
        const sections = [];
        let currentSection = "";
        for (const line of lines) {
          if (currentSection.length + line.length > maxSectionLength || line.startsWith("#")) {
            if (currentSection.trim()) {
              sections.push(currentSection.trim());
            }
            currentSection = line + "\n";
          } else {
            currentSection += line + "\n";
          }
        }
        if (currentSection.trim()) {
          sections.push(currentSection.trim());
        }
        return sections;
      };
      const processSection = async (section) => {
        try {
          const response = await uni.request({
            url: "https://api.deepseek.com/v1/chat/completions",
            method: "POST",
            data: {
              messages: [
                {
                  role: "system",
                  content: `你是一个专业的安全管理数据分析助手。请分析用户提供的安全管理文档内容，并按照以下要求提取信息：

1. 将文档完整内容逐条或逐项或逐句或逐段进行拆分处理。
2. 对每条内容提取相关信息，并转化成JSON格式。
3. 文件名为"${fileName.value}"。
4. articleType必须是以下选项之一："主体行为"、"设施设备"、"物料"、"方法措施"、"作业环境"、"应急管理"、"资料管理"。请仔细理解每条内容讲的是哪方面。如果一条内容涉及不止一个方面，那就拆分成多条内容。
5. articleContent为内容原文，不要修改，保留原始标点。
6. keywords为内容的关键词/标签，包括主语、标签、主要内容或摘要等。如果内容本身没有主语，需要从上下文或各级标题中获取。要确保我在搜索框中输入关键词或条文大意时，能够找到对应的原文内容。

输出格式示例：
[
  {
    "fileName": "工程质量安全手册",
    "articleType": "主体行为",
    "articleContent": "与参建各方签订的合同中应当明确安全责任，并加强履约管理。",
    "keywords": "建设单位 合同约定 安全责任"
  }
]

请站在安全管理专家、工程管理专家、语言学专家、资料管理专家等的角度分析内容。`
                },
                {
                  role: "user",
                  content: section
                }
              ],
              model: "deepseek-chat",
              temperature: 0.2
            },
            header: {
              "Content-Type": "application/json",
              "Authorization": "Bearer sk-38ff2a6b9f754b059fa42839d5a4b426"
            }
          });
          if (response.statusCode === 200 && response.data && response.data.choices && response.data.choices.length > 0) {
            const content = response.data.choices[0].message.content;
            const jsonMatch = content.match(/\[\s*\{[\s\S]*\}\s*\]/);
            if (jsonMatch) {
              const jsonStr = jsonMatch[0];
              const parsedData = JSON.parse(jsonStr);
              return parsedData;
            } else {
              formatAppLog("error", "at pages/data/smart-import.vue:557", "无法从API响应中提取JSON数据");
              return [];
            }
          } else {
            formatAppLog("error", "at pages/data/smart-import.vue:561", "API请求失败:", response);
            throw new Error("API请求失败: " + (response.statusCode || "Unknown error"));
          }
        } catch (error) {
          formatAppLog("error", "at pages/data/smart-import.vue:565", "处理部分内容失败:", error);
          return [];
        }
      };
      const previewResults = () => {
        if (processedItems.value.length === 0) {
          uni.showToast({
            title: "没有可预览的数据",
            icon: "none"
          });
          return;
        }
        currentStep.value = 3;
      };
      const cancelImport = () => {
        uni.showModal({
          title: "确认取消",
          content: "确定要取消导入吗？已处理的数据将丢失。",
          success: (res) => {
            if (res.confirm) {
              uni.navigateBack();
            }
          }
        });
      };
      const importData = async () => {
        if (processedItems.value.length === 0) {
          uni.showToast({
            title: "没有数据可导入",
            icon: "none"
          });
          return;
        }
        isImporting.value = true;
        try {
          uni.showLoading({
            title: "正在导入数据..."
          });
          const batchSize = 50;
          const totalItems = processedItems.value.length;
          let successCount = 0;
          for (let i = 0; i < totalItems; i += batchSize) {
            const batch = processedItems.value.slice(i, i + batchSize);
            for (const item of batch) {
              const result = await insertData("articles", item);
              if (result) {
                successCount++;
              }
            }
            importProgress.value = (i + batch.length) / totalItems * 100;
            uni.showLoading({
              title: `已导入 ${successCount}/${totalItems}...`
            });
          }
          uni.hideLoading();
          importComplete.value = true;
          uni.showModal({
            title: "导入完成",
            content: `成功导入 ${successCount} 条数据`,
            showCancel: false,
            success: () => {
              uni.navigateBack();
            }
          });
        } catch (error) {
          formatAppLog("error", "at pages/data/smart-import.vue:649", "导入数据失败:", error);
          uni.hideLoading();
          uni.showToast({
            title: "导入数据失败: " + error.message,
            icon: "none"
          });
        } finally {
          isImporting.value = false;
        }
      };
      return {
        currentStep,
        selectedFile,
        processingStatus,
        processingProgress,
        processingError,
        processingComplete,
        processedItems,
        previewItems,
        isImporting,
        importProgress,
        importComplete,
        formatFileSize,
        goBack,
        selectFile,
        readFileContent,
        previewResults,
        cancelImport,
        importData
      };
    }
  };
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", { class: "header-left" }, [
          vue.createElementVNode("text", {
            class: "header-back",
            onClick: _cache[0] || (_cache[0] = (...args) => $setup.goBack && $setup.goBack(...args))
          }, "返回")
        ]),
        vue.createElementVNode("view", { class: "header-title" }, "智能输入"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createElementVNode("view", { class: "content" }, [
        vue.createElementVNode("view", { class: "step-indicator" }, [
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["step", { active: $setup.currentStep >= 1 }])
            },
            "1. 选择文件",
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["step", { active: $setup.currentStep >= 2 }])
            },
            "2. 处理内容",
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["step", { active: $setup.currentStep >= 3 }])
            },
            "3. 导入数据",
            2
            /* CLASS */
          )
        ]),
        vue.createCommentVNode(" 步骤1：选择文件 "),
        $setup.currentStep === 1 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "step-content"
        }, [
          vue.createElementVNode("view", { class: "instruction" }, "请选择一个Markdown文件进行智能处理"),
          vue.createElementVNode("button", {
            class: "primary-button",
            onClick: _cache[1] || (_cache[1] = (...args) => $setup.selectFile && $setup.selectFile(...args))
          }, "选择MD文件"),
          $setup.selectedFile ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "file-info"
          }, [
            vue.createElementVNode(
              "text",
              { class: "file-name" },
              "已选择: " + vue.toDisplayString($setup.selectedFile.name),
              1
              /* TEXT */
            ),
            vue.createElementVNode(
              "text",
              { class: "file-size" },
              "大小: " + vue.toDisplayString($setup.formatFileSize($setup.selectedFile.size)),
              1
              /* TEXT */
            )
          ])) : vue.createCommentVNode("v-if", true),
          $setup.selectedFile ? (vue.openBlock(), vue.createElementBlock("button", {
            key: 1,
            class: "primary-button",
            onClick: _cache[2] || (_cache[2] = (...args) => $setup.readFileContent && $setup.readFileContent(...args))
          }, "开始处理")) : vue.createCommentVNode("v-if", true)
        ])) : vue.createCommentVNode("v-if", true),
        vue.createCommentVNode(" 步骤2：处理内容 "),
        $setup.currentStep === 2 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "step-content"
        }, [
          vue.createElementVNode("view", { class: "processing-status" }, [
            vue.createElementVNode(
              "text",
              { class: "status-text" },
              vue.toDisplayString($setup.processingStatus),
              1
              /* TEXT */
            ),
            vue.createElementVNode("progress", {
              percent: $setup.processingProgress,
              "stroke-width": "4"
            }, null, 8, ["percent"]),
            vue.createElementVNode(
              "text",
              { class: "progress-text" },
              vue.toDisplayString($setup.processingProgress.toFixed(0)) + "%",
              1
              /* TEXT */
            )
          ]),
          $setup.processingError ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "error-message"
          }, [
            vue.createElementVNode(
              "text",
              null,
              vue.toDisplayString($setup.processingError),
              1
              /* TEXT */
            )
          ])) : vue.createCommentVNode("v-if", true),
          $setup.processingComplete ? (vue.openBlock(), vue.createElementBlock("button", {
            key: 1,
            class: "primary-button",
            onClick: _cache[3] || (_cache[3] = (...args) => $setup.previewResults && $setup.previewResults(...args))
          }, "预览结果")) : vue.createCommentVNode("v-if", true)
        ])) : vue.createCommentVNode("v-if", true),
        vue.createCommentVNode(" 步骤3：预览和导入 "),
        $setup.currentStep === 3 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 2,
          class: "step-content"
        }, [
          vue.createElementVNode("view", { class: "preview-header" }, [
            vue.createElementVNode("text", { class: "preview-title" }, "处理结果预览"),
            vue.createElementVNode(
              "text",
              { class: "preview-count" },
              "共 " + vue.toDisplayString($setup.processedItems.length) + " 条数据",
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("scroll-view", {
            class: "preview-list",
            "scroll-y": ""
          }, [
            (vue.openBlock(true), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($setup.previewItems, (item, index) => {
                return vue.openBlock(), vue.createElementBlock("view", {
                  key: index,
                  class: "preview-item"
                }, [
                  vue.createElementVNode("view", { class: "preview-item-header" }, [
                    vue.createElementVNode(
                      "text",
                      { class: "item-number" },
                      vue.toDisplayString(index + 1),
                      1
                      /* TEXT */
                    ),
                    vue.createElementVNode(
                      "text",
                      { class: "item-type" },
                      vue.toDisplayString(item.articleType),
                      1
                      /* TEXT */
                    )
                  ]),
                  vue.createElementVNode("view", { class: "preview-item-content" }, [
                    vue.createElementVNode(
                      "text",
                      { class: "item-content" },
                      vue.toDisplayString(item.articleContent),
                      1
                      /* TEXT */
                    )
                  ]),
                  vue.createElementVNode("view", { class: "preview-item-footer" }, [
                    vue.createElementVNode(
                      "text",
                      { class: "item-keywords" },
                      "关键词: " + vue.toDisplayString(item.keywords),
                      1
                      /* TEXT */
                    )
                  ])
                ]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ]),
          vue.createElementVNode("view", { class: "import-actions" }, [
            vue.createElementVNode("button", {
              class: "secondary-button",
              onClick: _cache[4] || (_cache[4] = (...args) => $setup.cancelImport && $setup.cancelImport(...args))
            }, "取消"),
            vue.createElementVNode("button", {
              class: "primary-button",
              onClick: _cache[5] || (_cache[5] = (...args) => $setup.importData && $setup.importData(...args)),
              disabled: $setup.isImporting
            }, vue.toDisplayString($setup.isImporting ? "导入中..." : "导入数据"), 9, ["disabled"])
          ])
        ])) : vue.createCommentVNode("v-if", true)
      ])
    ]);
  }
  const PagesDataSmartImport = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$1], ["__file", "G:/my/safety_management/pages/data/smart-import.vue"]]);
  const _sfc_main$1 = {
    data() {
      return {
        isLoading: false,
        isSaving: false,
        projects: [],
        projectOptions: [],
        selectedProjectIndex: 0,
        selectedProject: null,
        formData: {
          subprojectName: "",
          constructionLocation: "",
          constructionUnit: "",
          agentUnit: "",
          surveyUnit: "",
          designUnit: "",
          supervisionUnit: "",
          constructorUnit: "",
          projectDescription: ""
        },
        errors: {}
      };
    },
    onLoad() {
      this.loadProjects();
    },
    methods: {
      // 返回上一页
      goBack() {
        uni.navigateBack();
      },
      // 加载项目列表
      async loadProjects() {
        this.isLoading = true;
        try {
          const projects = await queryTableData("projects");
          this.projects = projects || [];
          this.projectOptions = this.projects.map((p) => p.projectName || "未命名项目");
          if (this.projects.length === 0) {
            uni.showToast({
              title: "请先创建项目",
              icon: "none"
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 2e3);
          }
        } catch (e) {
          formatAppLog("error", "at pages/data/subproject-entry.vue:224", "加载项目列表失败", e);
          uni.showToast({
            title: "加载项目列表失败",
            icon: "none"
          });
        } finally {
          this.isLoading = false;
        }
      },
      // 项目选择变更
      onProjectChange(e) {
        const index = e.detail.value;
        this.selectedProjectIndex = index;
        this.selectedProject = this.projects[index];
        if (this.selectedProject) {
          this.formData.constructionLocation = this.selectedProject.constructionLocation || "";
          this.formData.constructionUnit = this.selectedProject.legalPerson || "";
          this.formData.projectDescription = this.selectedProject.constructionScale || "";
        }
      },
      // 验证表单
      validateForm() {
        const errors = {};
        let isValid = true;
        if (!this.selectedProject) {
          errors.projectName = "请选择上级项目";
          isValid = false;
        }
        if (!this.formData.subprojectName.trim()) {
          errors.subprojectName = "请输入子项目名称";
          isValid = false;
        }
        this.errors = errors;
        return isValid;
      },
      // 保存子项目
      async saveSubproject() {
        if (!this.validateForm()) {
          return;
        }
        this.isSaving = true;
        try {
          const data = {
            projectId: this.selectedProject.id,
            projectName: this.selectedProject.projectName,
            subprojectName: this.formData.subprojectName.trim(),
            constructionLocation: this.formData.constructionLocation.trim(),
            constructionUnit: this.formData.constructionUnit.trim(),
            agentUnit: this.formData.agentUnit.trim(),
            surveyUnit: this.formData.surveyUnit.trim(),
            designUnit: this.formData.designUnit.trim(),
            supervisionUnit: this.formData.supervisionUnit.trim(),
            constructorUnit: this.formData.constructorUnit.trim(),
            projectDescription: this.formData.projectDescription.trim()
          };
          const result = await insertData("subprojects", data);
          if (result) {
            uni.showToast({
              title: "子项目创建成功",
              icon: "success"
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            uni.showToast({
              title: "创建失败",
              icon: "none"
            });
          }
        } catch (e) {
          formatAppLog("error", "at pages/data/subproject-entry.vue:308", "保存子项目失败", e);
          uni.showToast({
            title: "保存失败: " + (e.message || e),
            icon: "none"
          });
        } finally {
          this.isSaving = false;
        }
      }
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "header-back" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "创建子项目"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createElementVNode("view", { class: "form-container" }, [
        $data.isLoading ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "empty-tip"
        }, [
          vue.createElementVNode("text", null, "加载中...")
        ])) : (vue.openBlock(), vue.createElementBlock("view", { key: 1 }, [
          vue.createCommentVNode(" 项目选择 "),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "选择上级项目 *"),
            vue.createElementVNode("view", { class: "input-container" }, [
              vue.createElementVNode("picker", {
                value: $data.selectedProjectIndex,
                range: $data.projectOptions,
                onChange: _cache[1] || (_cache[1] = (...args) => $options.onProjectChange && $options.onProjectChange(...args))
              }, [
                vue.createElementVNode("view", { class: "picker-view" }, [
                  $data.selectedProject ? (vue.openBlock(), vue.createElementBlock(
                    "text",
                    { key: 0 },
                    vue.toDisplayString($data.selectedProject.projectName),
                    1
                    /* TEXT */
                  )) : (vue.openBlock(), vue.createElementBlock("text", {
                    key: 1,
                    class: "picker-placeholder"
                  }, "请选择上级项目"))
                ])
              ], 40, ["value", "range"])
            ]),
            $data.errors.projectName ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "form-error"
              },
              vue.toDisplayString($data.errors.projectName),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ]),
          vue.createCommentVNode(" 子项目名称 "),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "子项目名称 *"),
            vue.createElementVNode("view", { class: "input-container" }, [
              vue.withDirectives(vue.createElementVNode(
                "input",
                {
                  class: "form-input",
                  "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $data.formData.subprojectName = $event),
                  placeholder: "请输入子项目名称",
                  type: "text"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vue.vModelText, $data.formData.subprojectName]
              ])
            ]),
            $data.errors.subprojectName ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "form-error"
              },
              vue.toDisplayString($data.errors.subprojectName),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ]),
          vue.createCommentVNode(" 建设地点（自动填入，可修改） "),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "建设地点"),
            vue.createElementVNode("view", { class: "input-container" }, [
              vue.withDirectives(vue.createElementVNode(
                "input",
                {
                  class: "form-input",
                  "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $data.formData.constructionLocation = $event),
                  placeholder: "建设地点（自动关联上级项目）",
                  type: "text"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vue.vModelText, $data.formData.constructionLocation]
              ])
            ]),
            vue.createElementVNode("text", { class: "form-hint" }, "自动关联上级项目的建设地点，可修改")
          ]),
          vue.createCommentVNode(" 建设单位（自动填入，可修改） "),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "建设单位"),
            vue.createElementVNode("view", { class: "input-container" }, [
              vue.withDirectives(vue.createElementVNode(
                "input",
                {
                  class: "form-input",
                  "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => $data.formData.constructionUnit = $event),
                  placeholder: "建设单位（自动关联上级项目）",
                  type: "text"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vue.vModelText, $data.formData.constructionUnit]
              ])
            ]),
            vue.createElementVNode("text", { class: "form-hint" }, "自动关联上级项目的企业法人，可修改")
          ]),
          vue.createCommentVNode(" 代建单位 "),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "代建单位"),
            vue.createElementVNode("view", { class: "input-container" }, [
              vue.withDirectives(vue.createElementVNode(
                "input",
                {
                  class: "form-input",
                  "onUpdate:modelValue": _cache[5] || (_cache[5] = ($event) => $data.formData.agentUnit = $event),
                  placeholder: "请输入代建单位",
                  type: "text"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vue.vModelText, $data.formData.agentUnit]
              ])
            ])
          ]),
          vue.createCommentVNode(" 勘察单位 "),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "勘察单位"),
            vue.createElementVNode("view", { class: "input-container" }, [
              vue.withDirectives(vue.createElementVNode(
                "input",
                {
                  class: "form-input",
                  "onUpdate:modelValue": _cache[6] || (_cache[6] = ($event) => $data.formData.surveyUnit = $event),
                  placeholder: "请输入勘察单位",
                  type: "text"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vue.vModelText, $data.formData.surveyUnit]
              ])
            ])
          ]),
          vue.createCommentVNode(" 设计单位 "),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "设计单位"),
            vue.createElementVNode("view", { class: "input-container" }, [
              vue.withDirectives(vue.createElementVNode(
                "input",
                {
                  class: "form-input",
                  "onUpdate:modelValue": _cache[7] || (_cache[7] = ($event) => $data.formData.designUnit = $event),
                  placeholder: "请输入设计单位",
                  type: "text"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vue.vModelText, $data.formData.designUnit]
              ])
            ])
          ]),
          vue.createCommentVNode(" 监理单位 "),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "监理单位"),
            vue.createElementVNode("view", { class: "input-container" }, [
              vue.withDirectives(vue.createElementVNode(
                "input",
                {
                  class: "form-input",
                  "onUpdate:modelValue": _cache[8] || (_cache[8] = ($event) => $data.formData.supervisionUnit = $event),
                  placeholder: "请输入监理单位",
                  type: "text"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vue.vModelText, $data.formData.supervisionUnit]
              ])
            ])
          ]),
          vue.createCommentVNode(" 施工单位 "),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "施工单位"),
            vue.createElementVNode("view", { class: "input-container" }, [
              vue.withDirectives(vue.createElementVNode(
                "input",
                {
                  class: "form-input",
                  "onUpdate:modelValue": _cache[9] || (_cache[9] = ($event) => $data.formData.constructorUnit = $event),
                  placeholder: "请输入施工单位",
                  type: "text"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vue.vModelText, $data.formData.constructorUnit]
              ])
            ])
          ]),
          vue.createCommentVNode(" 项目描述（自动填入，可修改） "),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "项目描述"),
            vue.createElementVNode("view", { class: "input-container" }, [
              vue.withDirectives(vue.createElementVNode(
                "textarea",
                {
                  class: "form-textarea",
                  "onUpdate:modelValue": _cache[10] || (_cache[10] = ($event) => $data.formData.projectDescription = $event),
                  placeholder: "项目描述（自动关联上级项目）",
                  "auto-height": ""
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vue.vModelText, $data.formData.projectDescription]
              ])
            ]),
            vue.createElementVNode("text", { class: "form-hint" }, "自动关联上级项目的建设规模及内容，可修改")
          ]),
          vue.createCommentVNode(" 操作按钮 "),
          vue.createElementVNode("view", { class: "button-group" }, [
            vue.createElementVNode("button", {
              class: "cancel-button",
              onClick: _cache[11] || (_cache[11] = (...args) => $options.goBack && $options.goBack(...args))
            }, "取消"),
            vue.createElementVNode("button", {
              class: "save-button",
              onClick: _cache[12] || (_cache[12] = (...args) => $options.saveSubproject && $options.saveSubproject(...args)),
              disabled: $data.isSaving
            }, vue.toDisplayString($data.isSaving ? "创建中..." : "创建子项目"), 9, ["disabled"])
          ])
        ]))
      ])
    ]);
  }
  const PagesDataSubprojectEntry = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__file", "G:/my/safety_management/pages/data/subproject-entry.vue"]]);
  __definePage("pages/index/index", PagesIndexIndex);
  __definePage("pages/table/create", PagesTableCreate);
  __definePage("pages/column/create", PagesColumnCreate);
  __definePage("pages/table/detail", PagesTableDetail);
  __definePage("pages/table/edit", PagesTableEdit);
  __definePage("pages/data/entry", PagesDataEntry);
  __definePage("pages/data/edit", PagesDataEdit);
  __definePage("pages/data/import", PagesDataImport);
  __definePage("pages/data/smart-import", PagesDataSmartImport);
  __definePage("pages/data/subproject-entry", PagesDataSubprojectEntry);
  const _sfc_main = {
    onLaunch: async function() {
      formatAppLog("log", "at App.vue:6", "App Launch");
      try {
        await openDatabase();
        await initSystemTables();
        await migrateSystemTables();
        formatAppLog("log", "at App.vue:15", "数据库初始化成功，表的创建将在用户界面中处理");
      } catch (e) {
        formatAppLog("error", "at App.vue:17", "数据库初始化失败", e);
      }
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:21", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:24", "App Hide");
    },
    onUnload: async function() {
      try {
        await closeDatabase();
      } catch (e) {
        formatAppLog("error", "at App.vue:31", "关闭数据库失败", e);
      }
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "G:/my/safety_management/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
