<template>
  <view class="container">
    <view class="header">
      <view class="header-content">
        <image src="/static/logo.jpg" mode="aspectFit" class="logo"></image>
        <text class="title">支出明细</text>
      </view>
    </view>

    <!-- 筛选区域 -->
    <view class="filter-section">
      <view class="filter-item">
        <text class="filter-label">日期区间</text>
        <view class="date-range">
          <picker mode="date" :value="startDate" @change="onStartDateChange">
            <view class="picker-item">{{ startDate || '开始日期' }}</view>
          </picker>
          <text class="date-separator">至</text>
          <picker mode="date" :value="endDate" @change="onEndDateChange">
            <view class="picker-item">{{ endDate || '结束日期' }}</view>
          </picker>
        </view>
      </view>

      <view class="filter-item">
        <text class="filter-label">三级分类</text>
        <picker :range="categoryOptions" range-key="name" @change="onCategoryChange">
          <view class="picker-item">{{ selectedCategory?.name || '全部' }}</view>
        </picker>
      </view>

      <view class="filter-item">
        <text class="filter-label">标的</text>
        <input
          type="text"
          v-model="targetFilter"
          placeholder="请输入标的"
          class="filter-input"
        />
      </view>

      <button class="filter-button" @tap="applyFilters">筛选</button>
    </view>

    <!-- 数据表格 -->
    <scroll-view scroll-y class="table-container">
      <view class="table">
        <view class="table-header">
          <view class="table-cell date-cell">时间</view>
          <view class="table-cell category-cell">分类</view>
          <view class="table-cell target-cell">标的</view>
          <view class="table-cell amount-cell">支出</view>
        </view>

        <view v-for="(item, index) in expenses" :key="index" class="table-row">
          <view class="table-cell date-cell">{{ formatDate(item.created_at) }}</view>
          <view class="table-cell category-cell">{{ item.category_name }}</view>
          <view class="table-cell target-cell">{{ item.target }}</view>
          <view class="table-cell amount-cell">{{ item.amount.toFixed(2) }}</view>
        </view>

        <view v-if="expenses.length === 0" class="empty-state">
          暂无数据
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useStore } from 'vuex'
import { dbService } from '../../utils/db'

export default {
  setup() {
    const store = useStore()
    const startDate = ref('')
    const endDate = ref('')
    const selectedCategory = ref(null)
    const targetFilter = ref('')
    const expenses = ref([])
    const categoryOptions = ref([])
    const isLoading = ref(false)

    // 获取所有三级分类选项
    const loadCategories = () => {
      const allCategories = store.state.categories
      const options = []

      const extractLevel3Categories = (categories) => {
        categories.forEach(category => {
          if (category.children) {
            category.children.forEach(level2 => {
              if (level2.children) {
                level2.children.forEach(level3 => {
                  options.push({
                    id: level3.category_id,
                    name: `${category.name} / ${level2.name} / ${level3.name}`
                  })
                })
              }
            })
          }
        })
      }

      extractLevel3Categories(allCategories)
      categoryOptions.value = [{ id: '', name: '全部' }, ...options]
    }

    // 加载支出数据
    const loadExpenses = async () => {
      try {
        // 构建查询条件
        let conditions = ['e.user_id = ?']
        let params = [store.state.user.user_id]

        if (startDate.value) {
          conditions.push('e.record_date >= ?')
          params.push(startDate.value)
        }
        if (endDate.value) {
          conditions.push('e.record_date <= ?')
          params.push(endDate.value)
        }
        if (selectedCategory.value?.id) {
          conditions.push('e.category_id = ?')
          params.push(selectedCategory.value.id)
        }
        if (targetFilter.value) {
          conditions.push('e.target LIKE ?')
          params.push(`%${targetFilter.value}%`)
        }

        // 构建SQL查询
        const sql = `
          SELECT
            e.*,
            c1.name as level1_name,
            c2.name as level2_name,
            c3.name as level3_name
          FROM expenses e
          LEFT JOIN categories c3 ON e.category_id = c3.category_id
          LEFT JOIN categories c2 ON c3.parent_id = c2.category_id
          LEFT JOIN categories c1 ON c2.parent_id = c1.category_id
          WHERE ${conditions.join(' AND ')}
          ORDER BY e.record_date DESC, e.created_at DESC
          LIMIT 40
        `

        // 执行查询
        const result = await dbService.selectSql(sql, params)

        // 处理查询结果
        expenses.value = result.map(item => ({
          ...item,
          created_at: item.record_date, // 使用record_date作为显示日期
          amount: parseFloat(item.amount),
          // 只显示三级分类名称
          category_name: item.level3_name || '未知分类'
        }))

      } catch (error) {
        console.error('获取数据失败:', error)
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        })
      }
    }

    // 日期格式化
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }

    // 事件处理函数
    const onStartDateChange = (e) => {
      startDate.value = e.detail.value
    }

    const onEndDateChange = (e) => {
      endDate.value = e.detail.value
    }

    const onCategoryChange = (e) => {
      selectedCategory.value = categoryOptions.value[e.detail.value]
    }

    const applyFilters = () => {
      loadExpenses()
    }



    // 页面加载时初始化
    onMounted(async () => {
      if (!store.state.user) {
        uni.reLaunch({
          url: '/pages/login/login'
        })
        return
      }

      await Promise.all([
        store.dispatch('fetchCategories'),
        loadCategories(),
        loadExpenses()
      ])
    })

    return {
      startDate,
      endDate,
      selectedCategory,
      targetFilter,
      expenses,
      categoryOptions,
      isLoading,
      formatDate,
      onStartDateChange,
      onEndDateChange,
      onCategoryChange,
      applyFilters
    }
  }
}
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
  box-sizing: border-box;
}

.header {
  background: #4a90e2;
  padding: 30px 16px;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-top: 15px;
  }

  .logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    margin-right: 12px;
  }

  .title {
    color: white;
    font-size: 20px;
    font-weight: 500;
  }
}

.filter-section {
  background: white;
  padding: 16px;
  margin: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-item {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 16px;
  }
}

.filter-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.date-range {
  display: flex;
  align-items: center;
}

.date-separator {
  margin: 0 8px;
  color: #666;
}

.picker-item {
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
}

.filter-input {
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
}

.filter-button {
  width: 100%;
  height: 40px;
  line-height: 40px;
  background: #4a90e2;
  color: white;
  border-radius: 4px;
  font-size: 16px;
  text-align: center;
}

.table-container {
  flex: 1;
  background: white;
  margin: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  width: auto;
  box-sizing: border-box;
}

.table {
  width: 100%;
  box-sizing: border-box;
}

.table-header {
  display: flex;
  background: #4a90e2;
  color: white;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 8px 8px 0 0;
  width: 100%;
  box-sizing: border-box;
}

.table-row {
  display: flex;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  width: 100%;
  box-sizing: border-box;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f8f9fa;
  }
}

.table-cell {
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 4px;
  box-sizing: border-box;
}

.date-cell {
  width: 85px;
  flex: none;
}

.category-cell {
  width: 100px;
  flex: none;
}

.target-cell {
  flex: 1;
  min-width: 0;
  padding-right: 16px;
  word-break: break-all;
  white-space: normal;
  overflow: visible;
}

.amount-cell {
  width: 80px;
  flex: none;
  text-align: right;
  padding-right: 16px;
  white-space: nowrap;
}

.empty-state {
  padding: 24px;
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style>