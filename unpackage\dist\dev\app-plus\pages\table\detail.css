
.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 0.625rem 0.9375rem;
		padding-top: var(--status-bar-height);
}
.header-left, .header-right {
		width: 3.75rem;
}
.header-back, .header-action {
		color: #FFFFFF;
		font-size: 0.875rem;
}
.header-title {
		color: #FFFFFF;
		font-size: 1.125rem;
		font-weight: bold;
		max-width: 12.5rem;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
}
.tabs {
		display: flex;
		background-color: #FFFFFF;
		border-bottom: 0.03125rem solid #EEEEEE;
}
.tab-item {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 2.5rem;
		position: relative;
}
.tab-item-active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 25%;
		width: 50%;
		height: 0.125rem;
		background-color: #007AFF;
}
.tab-text {
		font-size: 0.875rem;
		color: #333333;
}
.tab-item-active .tab-text {
		color: #007AFF;
		font-weight: bold;
}
.tab-content {
		flex: 1;
		padding: 0.9375rem;
		overflow-y: auto;
}
.empty-tip, .loading-tip {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 9.375rem;
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		color: #999999;
		font-size: 0.875rem;
}
.loading-tip {
		background-color: #F8F8F8;
}

	/* 调试信息样式 */
.debug-info {
		margin-top: 0.9375rem;
		padding: 0.625rem;
		background-color: #f8f8f8;
		border: 1px solid #ddd;
		border-radius: 0.3125rem;
		text-align: left;
		width: 80%;
}
.debug-info-title {
		font-weight: bold;
		color: #666;
		margin-bottom: 0.3125rem;
		display: block;
}
.debug-info-item {
		font-size: 0.75rem;
		color: #666;
		margin: 0.15625rem 0;
		display: block;
}

	/* 数据行数显示样式 */
.data-count-info {
		padding: 0.3125rem 0.625rem;
		background-color: #f8f8f8;
		border-bottom: 1px solid #eee;
		text-align: right;
}
.data-count-text {
		font-size: 0.75rem;
		color: #666;
}
.data-count-info-empty {
		margin-top: 0.625rem;
		padding: 0.3125rem;
		background-color: #f8f8f8;
		border-radius: 0.25rem;
		text-align: center;
}
.column-list {
		display: flex;
		flex-direction: column;
		gap: 0.625rem;
}
.column-item {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem;
}
.column-item-header {
		margin-bottom: 0.3125rem;
}
.column-name {
		font-size: 0.9375rem;
		font-weight: bold;
}
.column-order {
		font-size: 0.75rem;
		color: #666666;
}
.columns-tip {
		margin-bottom: 0.3125rem;
}
.columns-tip-text {
		font-size: 0.75rem;
		color: #666666;
		font-style: italic;
}
.column-details {
		display: flex;
		flex-direction: column;
		gap: 0.1875rem;
}
.column-type {
		font-size: 0.8125rem;
		color: #333333;
}
.column-attributes {
		display: flex;
		flex-wrap: wrap;
		gap: 0.3125rem;
}
.column-attribute {
		background-color: #F0F0F0;
		padding: 0.125rem 0.375rem;
		border-radius: 0.125rem;
		font-size: 0.6875rem;
		color: #666666;
}
.column-foreign-key {
		font-size: 0.6875rem;
		color: #666666;
		font-style: italic;
		margin-top: 0.1875rem;
}
.data-table {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		overflow: hidden;
		width: 100%;
}
.table-scroll {
		width: 100%;
		height: 31.25rem; /* 增加高度以显示更多内容 */
		background-color: #FFFFFF;
}
.table-header-container {
		width: 100%;
		border-bottom: 0.03125rem solid #EEEEEE;
}
.table-header-cell {
		background-color: #F8F8F8;
		font-weight: bold;
		position: -webkit-sticky;
		position: sticky;
		top: 0;
		z-index: 10;
}
.data-tip {
		margin-bottom: 0.3125rem;
}
.data-tip-text {
		font-size: 0.75rem;
		color: #666666;
		font-style: italic;
}
.table-row {
		display: flex;
		border-bottom: 0.03125rem solid #EEEEEE;
		transition: background-color 0.2s;
		width: 100%;
		min-width: -webkit-fit-content;
		min-width: fit-content;
}
.table-row:active {
		background-color: #E0F0FF;
}
.table-row-even {
		background-color: #F8F8F8;
}
.table-row:last-child {
		border-bottom: none;
}
.text-primary {
		color: #007AFF;
		font-weight: bold;
}
.text-highlight {
		background-color: #FFFF00;
		padding: 0 0.125rem;
		border-radius: 0.125rem;
}
.text-wrap {
		white-space: normal;
		word-break: break-word;
		overflow: visible;
}
.table-row-selected {
		background-color: #E0F0FF;
}

	/* 搜索和操作栏样式 */
.search-bar {
		display: flex;
		justify-content: space-between;
		margin-bottom: 0.625rem;
		width: 100%; /* 确保搜索栏占满整个宽度 */
		padding: 0 0.3125rem; /* 添加左右内边距，减少与屏幕边缘的距离 */
}
.search-input-container {
		position: relative;
		width: 100%; /* 搜索框占据整行宽度 */
}
.search-input {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem 1.875rem 0.625rem 0.625rem; /* 进一步增加上下内边距，使输入框更高 */
		font-size: 0.9375rem; /* 增加字体大小 */
		border: 0.03125rem solid #EEEEEE;
		width: 100%;
		box-sizing: border-box;
		min-width: 0; /* 确保在Flex容器中可以正确缩放 */
		height: 2.5rem; /* 固定高度使输入框更大 */
}
.search-clear {
		position: absolute;
		right: 0.625rem;
		top: 50%;
		transform: translateY(-50%);
		font-size: 1.25rem; /* 增大清除按钮 */
		color: #999999;
		width: 1.5625rem; /* 增大点击区域 */
		height: 1.5625rem; /* 增大点击区域 */
		text-align: center;
		line-height: 1.5625rem;
		z-index: 10; /* 确保在输入框上方 */
}
.action-buttons {
		display: flex;
		flex-shrink: 0; /* 防止按钮区域被压缩 */
}
.action-button {
		background-color: #F0F0F0;
		border-radius: 0.25rem;
		padding: 0.46875rem 0.3125rem; /* 减小左右内边距，使按钮更紧凑 */
		margin: 0 0.25rem 0 0; /* 减小右边距，移除底部边距 */
		min-width: 3.125rem; /* 减小最小宽度 */
		text-align: center; /* 文本居中 */
		flex: 1; /* 按钮平均分配空间 */
		max-width: 4.375rem; /* 限制最大宽度 */
		white-space: nowrap; /* 防止文本换行 */
}

	/* 智能输入按钮样式 */
.smart-button {
		background-color: #007AFF;
}
.smart-button .action-button-text {
		color: #FFFFFF;
}
.action-button:last-child {
		margin-right: 0;
}

	/* 隐藏水平滚动条 */
.action-bar::-webkit-scrollbar {
		display: none;
}
.action-button-text {
		font-size: 0.75rem;
		color: #333333;
		white-space: nowrap; /* 防止文本换行 */
		overflow: hidden; /* 隐藏溢出内容 */
		text-overflow: ellipsis; /* 文本溢出显示省略号 */
}

	/* 操作按钮行样式 */
.action-bar {
		display: flex;
		justify-content: space-between; /* 均匀分布按钮 */
		margin-bottom: 0.625rem;
		flex-wrap: nowrap; /* 防止按钮换行 */
		padding: 0 0.3125rem; /* 添加左右内边距，与搜索框对齐 */
		overflow-x: auto; /* 如果空间不足，允许水平滚动 */
}

	/* 筛选标签样式 */
.active-filters {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 0.625rem;
}
.filter-tag {
		background-color: #E0F0FF;
		border-radius: 0.25rem;
		padding: 0.1875rem 0.3125rem;
		margin-right: 0.3125rem;
		margin-bottom: 0.3125rem;
		display: flex;
		align-items: center;
}
.filter-tag-text {
		font-size: 0.75rem;
		color: #007AFF;
}
.filter-tag-remove {
		font-size: 0.875rem;
		color: #007AFF;
		margin-left: 0.3125rem;
		width: 0.9375rem;
		height: 0.9375rem;
		text-align: center;
		line-height: 0.9375rem;
}
.clear-all-filters {
		font-size: 0.75rem;
		color: #FF6B6B;
		margin-left: 0.3125rem;
		padding: 0.1875rem 0.3125rem;
}

	/* 批量操作样式 */
.table-batch-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0.625rem;
		background-color: #F0F0F0;
		border-bottom: 0.03125rem solid #EEEEEE;
		width: 100%;
}
.batch-select-all {
		display: flex;
		align-items: center;
}
.batch-select-text {
		font-size: 0.875rem;
		margin-left: 0.3125rem;
}
.batch-actions {
		display: flex;
		align-items: center;
}
.batch-action-text {
		font-size: 0.875rem;
		color: #FF6B6B;
		margin-right: 0.625rem;
}
.batch-action-cancel {
		font-size: 0.875rem;
		color: #999999;
}
.checkbox {
		width: 1.125rem;
		height: 1.125rem;
		border-radius: 0.125rem;
		border: 0.03125rem solid #CCCCCC;
		background-color: #FFFFFF;
}
.checkbox-selected {
		background-color: #007AFF;
		border-color: #007AFF;
		position: relative;
}
.checkbox-selected::after {
		content: '';
		position: absolute;
		width: 0.625rem;
		height: 0.3125rem;
		border-left: 0.09375rem solid #FFFFFF;
		border-bottom: 0.09375rem solid #FFFFFF;
		transform: rotate(-45deg);
		top: 0.25rem;
		left: 0.1875rem;
}
.table-cell-checkbox {
		width: 1.875rem;
		flex: none;
}
.table-cell {
		min-width: 6.25rem;
		padding: 0.625rem;
		border-right: 0.03125rem solid #EEEEEE;
}

	/* 特定表格列的样式 */
.column-filename {
		min-width: 4.6875rem;
		max-width: 6.25rem;
}
.column-articletype {
		min-width: 3.75rem;
		max-width: 4.6875rem;
}
.column-articlecontent {
		min-width: 12.5rem;
		width: auto;
		flex: 1;
}
.column-keywords {
		min-width: 4.6875rem;
		max-width: 6.25rem;
}
.column-category {
		min-width: 4.6875rem;
		max-width: 6.25rem;
}
.column-docnumber {
		min-width: 6.25rem;
		max-width: 7.8125rem;
}
.column-publishunit {
		min-width: 6.25rem;
		max-width: 9.375rem;
}

	/* 项目表列样式 */
.column-projectname {
		min-width: 9.375rem;
		width: auto;
		flex: 1;
}
.column-legalperson {
		min-width: 6.25rem;
		max-width: 9.375rem;
}
.column-constructionlocation {
		min-width: 6.25rem;
		max-width: 9.375rem;
}
.column-constructionscale {
		min-width: 12.5rem;
		width: auto;
		flex: 2;
}

	/* 子项目表列样式 */
.column-subprojectname {
		min-width: 4.6875rem;
		max-width: 6.25rem;
}
.column-constructionunit {
		min-width: 4.0625rem;
		max-width: 5.3125rem;
}
.column-agentunit {
		min-width: 3.125rem;
		max-width: 4.0625rem;
}
.column-surveyunit {
		min-width: 2.5rem;
		max-width: 3.4375rem;
}
.column-designunit {
		min-width: 2.5rem;
		max-width: 3.4375rem;
}
.column-supervisionunit {
		min-width: 3.125rem;
		max-width: 4.0625rem;
}
.column-constructorunit {
		min-width: 4.0625rem;
		max-width: 5.3125rem;
}
.column-projectdescription {
		min-width: 4.6875rem;
		width: auto;
		flex: 1;
}
.table-cell:last-child {
		border-right: none;
}
.table-header-text {
		font-size: 0.8125rem;
		color: #333333;
}
.table-cell-text {
		font-size: 0.8125rem;
		color: #666666;
		display: block;
		width: 100%;
}

	/* 移除了.table-body样式，因为我们现在使用单一滚动视图 */
.fab-button {
		position: fixed;
		right: 0.9375rem;
		bottom: 0.9375rem;
		width: 3.125rem;
		height: 3.125rem;
		background-color: #007AFF;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 0.125rem 0.3125rem rgba(0, 0, 0, 0.2);
}
.fab-icon {
		color: #FFFFFF;
		font-size: 1.875rem;
		font-weight: bold;
}
