if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global2 = uni.requireGlobal();
  ArrayBuffer = global2.ArrayBuffer;
  Int8Array = global2.Int8Array;
  Uint8Array = global2.Uint8Array;
  Uint8ClampedArray = global2.Uint8ClampedArray;
  Int16Array = global2.Int16Array;
  Uint16Array = global2.Uint16Array;
  Int32Array = global2.Int32Array;
  Uint32Array = global2.Uint32Array;
  Float32Array = global2.Float32Array;
  Float64Array = global2.Float64Array;
  BigInt64Array = global2.BigInt64Array;
  BigUint64Array = global2.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  function requireNativePlugin(name) {
    return weex.requireModule(name);
  }
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  function getDevtoolsGlobalHook() {
    return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__;
  }
  function getTarget() {
    return typeof navigator !== "undefined" && typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {};
  }
  const isProxyAvailable = typeof Proxy === "function";
  const HOOK_SETUP = "devtools-plugin:setup";
  const HOOK_PLUGIN_SETTINGS_SET = "plugin:settings:set";
  class ApiProxy {
    constructor(plugin, hook) {
      this.target = null;
      this.targetQueue = [];
      this.onQueue = [];
      this.plugin = plugin;
      this.hook = hook;
      const defaultSettings = {};
      if (plugin.settings) {
        for (const id in plugin.settings) {
          const item = plugin.settings[id];
          defaultSettings[id] = item.defaultValue;
        }
      }
      const localSettingsSaveId = `__vue-devtools-plugin-settings__${plugin.id}`;
      let currentSettings = { ...defaultSettings };
      try {
        const raw = localStorage.getItem(localSettingsSaveId);
        const data = JSON.parse(raw);
        Object.assign(currentSettings, data);
      } catch (e) {
      }
      this.fallbacks = {
        getSettings() {
          return currentSettings;
        },
        setSettings(value) {
          try {
            localStorage.setItem(localSettingsSaveId, JSON.stringify(value));
          } catch (e) {
          }
          currentSettings = value;
        }
      };
      hook.on(HOOK_PLUGIN_SETTINGS_SET, (pluginId, value) => {
        if (pluginId === this.plugin.id) {
          this.fallbacks.setSettings(value);
        }
      });
      this.proxiedOn = new Proxy({}, {
        get: (_target, prop) => {
          if (this.target) {
            return this.target.on[prop];
          } else {
            return (...args) => {
              this.onQueue.push({
                method: prop,
                args
              });
            };
          }
        }
      });
      this.proxiedTarget = new Proxy({}, {
        get: (_target, prop) => {
          if (this.target) {
            return this.target[prop];
          } else if (prop === "on") {
            return this.proxiedOn;
          } else if (Object.keys(this.fallbacks).includes(prop)) {
            return (...args) => {
              this.targetQueue.push({
                method: prop,
                args,
                resolve: () => {
                }
              });
              return this.fallbacks[prop](...args);
            };
          } else {
            return (...args) => {
              return new Promise((resolve) => {
                this.targetQueue.push({
                  method: prop,
                  args,
                  resolve
                });
              });
            };
          }
        }
      });
    }
    async setRealTarget(target) {
      this.target = target;
      for (const item of this.onQueue) {
        this.target.on[item.method](...item.args);
      }
      for (const item of this.targetQueue) {
        item.resolve(await this.target[item.method](...item.args));
      }
    }
  }
  function setupDevtoolsPlugin(pluginDescriptor, setupFn) {
    const target = getTarget();
    const hook = getDevtoolsGlobalHook();
    const enableProxy = isProxyAvailable && pluginDescriptor.enableEarlyProxy;
    if (hook && (target.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !enableProxy)) {
      hook.emit(HOOK_SETUP, pluginDescriptor, setupFn);
    } else {
      const proxy = enableProxy ? new ApiProxy(pluginDescriptor, hook) : null;
      const list = target.__VUE_DEVTOOLS_PLUGINS__ = target.__VUE_DEVTOOLS_PLUGINS__ || [];
      list.push({
        pluginDescriptor,
        setupFn,
        proxy
      });
      if (proxy)
        setupFn(proxy.proxiedTarget);
    }
  }
  /*!
   * vuex v4.1.0
   * (c) 2022 Evan You
   * @license MIT
   */
  var storeKey = "store";
  function useStore(key) {
    if (key === void 0)
      key = null;
    return vue.inject(key !== null ? key : storeKey);
  }
  function forEachValue(obj, fn) {
    Object.keys(obj).forEach(function(key) {
      return fn(obj[key], key);
    });
  }
  function isObject(obj) {
    return obj !== null && typeof obj === "object";
  }
  function isPromise(val) {
    return val && typeof val.then === "function";
  }
  function assert(condition, msg) {
    if (!condition) {
      throw new Error("[vuex] " + msg);
    }
  }
  function partial(fn, arg) {
    return function() {
      return fn(arg);
    };
  }
  function genericSubscribe(fn, subs, options) {
    if (subs.indexOf(fn) < 0) {
      options && options.prepend ? subs.unshift(fn) : subs.push(fn);
    }
    return function() {
      var i = subs.indexOf(fn);
      if (i > -1) {
        subs.splice(i, 1);
      }
    };
  }
  function resetStore(store2, hot) {
    store2._actions = /* @__PURE__ */ Object.create(null);
    store2._mutations = /* @__PURE__ */ Object.create(null);
    store2._wrappedGetters = /* @__PURE__ */ Object.create(null);
    store2._modulesNamespaceMap = /* @__PURE__ */ Object.create(null);
    var state = store2.state;
    installModule(store2, state, [], store2._modules.root, true);
    resetStoreState(store2, state, hot);
  }
  function resetStoreState(store2, state, hot) {
    var oldState = store2._state;
    var oldScope = store2._scope;
    store2.getters = {};
    store2._makeLocalGettersCache = /* @__PURE__ */ Object.create(null);
    var wrappedGetters = store2._wrappedGetters;
    var computedObj = {};
    var computedCache = {};
    var scope = vue.effectScope(true);
    scope.run(function() {
      forEachValue(wrappedGetters, function(fn, key) {
        computedObj[key] = partial(fn, store2);
        computedCache[key] = vue.computed(function() {
          return computedObj[key]();
        });
        Object.defineProperty(store2.getters, key, {
          get: function() {
            return computedCache[key].value;
          },
          enumerable: true
          // for local getters
        });
      });
    });
    store2._state = vue.reactive({
      data: state
    });
    store2._scope = scope;
    if (store2.strict) {
      enableStrictMode(store2);
    }
    if (oldState) {
      if (hot) {
        store2._withCommit(function() {
          oldState.data = null;
        });
      }
    }
    if (oldScope) {
      oldScope.stop();
    }
  }
  function installModule(store2, rootState, path, module, hot) {
    var isRoot = !path.length;
    var namespace = store2._modules.getNamespace(path);
    if (module.namespaced) {
      if (store2._modulesNamespaceMap[namespace] && true) {
        console.error("[vuex] duplicate namespace " + namespace + " for the namespaced module " + path.join("/"));
      }
      store2._modulesNamespaceMap[namespace] = module;
    }
    if (!isRoot && !hot) {
      var parentState = getNestedState(rootState, path.slice(0, -1));
      var moduleName = path[path.length - 1];
      store2._withCommit(function() {
        {
          if (moduleName in parentState) {
            console.warn(
              '[vuex] state field "' + moduleName + '" was overridden by a module with the same name at "' + path.join(".") + '"'
            );
          }
        }
        parentState[moduleName] = module.state;
      });
    }
    var local = module.context = makeLocalContext(store2, namespace, path);
    module.forEachMutation(function(mutation, key) {
      var namespacedType = namespace + key;
      registerMutation(store2, namespacedType, mutation, local);
    });
    module.forEachAction(function(action, key) {
      var type = action.root ? key : namespace + key;
      var handler = action.handler || action;
      registerAction(store2, type, handler, local);
    });
    module.forEachGetter(function(getter, key) {
      var namespacedType = namespace + key;
      registerGetter(store2, namespacedType, getter, local);
    });
    module.forEachChild(function(child, key) {
      installModule(store2, rootState, path.concat(key), child, hot);
    });
  }
  function makeLocalContext(store2, namespace, path) {
    var noNamespace = namespace === "";
    var local = {
      dispatch: noNamespace ? store2.dispatch : function(_type, _payload, _options) {
        var args = unifyObjectStyle(_type, _payload, _options);
        var payload = args.payload;
        var options = args.options;
        var type = args.type;
        if (!options || !options.root) {
          type = namespace + type;
          if (!store2._actions[type]) {
            console.error("[vuex] unknown local action type: " + args.type + ", global type: " + type);
            return;
          }
        }
        return store2.dispatch(type, payload);
      },
      commit: noNamespace ? store2.commit : function(_type, _payload, _options) {
        var args = unifyObjectStyle(_type, _payload, _options);
        var payload = args.payload;
        var options = args.options;
        var type = args.type;
        if (!options || !options.root) {
          type = namespace + type;
          if (!store2._mutations[type]) {
            console.error("[vuex] unknown local mutation type: " + args.type + ", global type: " + type);
            return;
          }
        }
        store2.commit(type, payload, options);
      }
    };
    Object.defineProperties(local, {
      getters: {
        get: noNamespace ? function() {
          return store2.getters;
        } : function() {
          return makeLocalGetters(store2, namespace);
        }
      },
      state: {
        get: function() {
          return getNestedState(store2.state, path);
        }
      }
    });
    return local;
  }
  function makeLocalGetters(store2, namespace) {
    if (!store2._makeLocalGettersCache[namespace]) {
      var gettersProxy = {};
      var splitPos = namespace.length;
      Object.keys(store2.getters).forEach(function(type) {
        if (type.slice(0, splitPos) !== namespace) {
          return;
        }
        var localType = type.slice(splitPos);
        Object.defineProperty(gettersProxy, localType, {
          get: function() {
            return store2.getters[type];
          },
          enumerable: true
        });
      });
      store2._makeLocalGettersCache[namespace] = gettersProxy;
    }
    return store2._makeLocalGettersCache[namespace];
  }
  function registerMutation(store2, type, handler, local) {
    var entry = store2._mutations[type] || (store2._mutations[type] = []);
    entry.push(function wrappedMutationHandler(payload) {
      handler.call(store2, local.state, payload);
    });
  }
  function registerAction(store2, type, handler, local) {
    var entry = store2._actions[type] || (store2._actions[type] = []);
    entry.push(function wrappedActionHandler(payload) {
      var res = handler.call(store2, {
        dispatch: local.dispatch,
        commit: local.commit,
        getters: local.getters,
        state: local.state,
        rootGetters: store2.getters,
        rootState: store2.state
      }, payload);
      if (!isPromise(res)) {
        res = Promise.resolve(res);
      }
      if (store2._devtoolHook) {
        return res.catch(function(err) {
          store2._devtoolHook.emit("vuex:error", err);
          throw err;
        });
      } else {
        return res;
      }
    });
  }
  function registerGetter(store2, type, rawGetter, local) {
    if (store2._wrappedGetters[type]) {
      {
        console.error("[vuex] duplicate getter key: " + type);
      }
      return;
    }
    store2._wrappedGetters[type] = function wrappedGetter(store22) {
      return rawGetter(
        local.state,
        // local state
        local.getters,
        // local getters
        store22.state,
        // root state
        store22.getters
        // root getters
      );
    };
  }
  function enableStrictMode(store2) {
    vue.watch(function() {
      return store2._state.data;
    }, function() {
      {
        assert(store2._committing, "do not mutate vuex store state outside mutation handlers.");
      }
    }, { deep: true, flush: "sync" });
  }
  function getNestedState(state, path) {
    return path.reduce(function(state2, key) {
      return state2[key];
    }, state);
  }
  function unifyObjectStyle(type, payload, options) {
    if (isObject(type) && type.type) {
      options = payload;
      payload = type;
      type = type.type;
    }
    {
      assert(typeof type === "string", "expects string as the type, but found " + typeof type + ".");
    }
    return { type, payload, options };
  }
  var LABEL_VUEX_BINDINGS = "vuex bindings";
  var MUTATIONS_LAYER_ID = "vuex:mutations";
  var ACTIONS_LAYER_ID = "vuex:actions";
  var INSPECTOR_ID = "vuex";
  var actionId = 0;
  function addDevtools(app, store2) {
    setupDevtoolsPlugin(
      {
        id: "org.vuejs.vuex",
        app,
        label: "Vuex",
        homepage: "https://next.vuex.vuejs.org/",
        logo: "https://vuejs.org/images/icons/favicon-96x96.png",
        packageName: "vuex",
        componentStateTypes: [LABEL_VUEX_BINDINGS]
      },
      function(api) {
        api.addTimelineLayer({
          id: MUTATIONS_LAYER_ID,
          label: "Vuex Mutations",
          color: COLOR_LIME_500
        });
        api.addTimelineLayer({
          id: ACTIONS_LAYER_ID,
          label: "Vuex Actions",
          color: COLOR_LIME_500
        });
        api.addInspector({
          id: INSPECTOR_ID,
          label: "Vuex",
          icon: "storage",
          treeFilterPlaceholder: "Filter stores..."
        });
        api.on.getInspectorTree(function(payload) {
          if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {
            if (payload.filter) {
              var nodes = [];
              flattenStoreForInspectorTree(nodes, store2._modules.root, payload.filter, "");
              payload.rootNodes = nodes;
            } else {
              payload.rootNodes = [
                formatStoreForInspectorTree(store2._modules.root, "")
              ];
            }
          }
        });
        api.on.getInspectorState(function(payload) {
          if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {
            var modulePath = payload.nodeId;
            makeLocalGetters(store2, modulePath);
            payload.state = formatStoreForInspectorState(
              getStoreModule(store2._modules, modulePath),
              modulePath === "root" ? store2.getters : store2._makeLocalGettersCache,
              modulePath
            );
          }
        });
        api.on.editInspectorState(function(payload) {
          if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {
            var modulePath = payload.nodeId;
            var path = payload.path;
            if (modulePath !== "root") {
              path = modulePath.split("/").filter(Boolean).concat(path);
            }
            store2._withCommit(function() {
              payload.set(store2._state.data, path, payload.state.value);
            });
          }
        });
        store2.subscribe(function(mutation, state) {
          var data = {};
          if (mutation.payload) {
            data.payload = mutation.payload;
          }
          data.state = state;
          api.notifyComponentUpdate();
          api.sendInspectorTree(INSPECTOR_ID);
          api.sendInspectorState(INSPECTOR_ID);
          api.addTimelineEvent({
            layerId: MUTATIONS_LAYER_ID,
            event: {
              time: Date.now(),
              title: mutation.type,
              data
            }
          });
        });
        store2.subscribeAction({
          before: function(action, state) {
            var data = {};
            if (action.payload) {
              data.payload = action.payload;
            }
            action._id = actionId++;
            action._time = Date.now();
            data.state = state;
            api.addTimelineEvent({
              layerId: ACTIONS_LAYER_ID,
              event: {
                time: action._time,
                title: action.type,
                groupId: action._id,
                subtitle: "start",
                data
              }
            });
          },
          after: function(action, state) {
            var data = {};
            var duration = Date.now() - action._time;
            data.duration = {
              _custom: {
                type: "duration",
                display: duration + "ms",
                tooltip: "Action duration",
                value: duration
              }
            };
            if (action.payload) {
              data.payload = action.payload;
            }
            data.state = state;
            api.addTimelineEvent({
              layerId: ACTIONS_LAYER_ID,
              event: {
                time: Date.now(),
                title: action.type,
                groupId: action._id,
                subtitle: "end",
                data
              }
            });
          }
        });
      }
    );
  }
  var COLOR_LIME_500 = 8702998;
  var COLOR_DARK = 6710886;
  var COLOR_WHITE = 16777215;
  var TAG_NAMESPACED = {
    label: "namespaced",
    textColor: COLOR_WHITE,
    backgroundColor: COLOR_DARK
  };
  function extractNameFromPath(path) {
    return path && path !== "root" ? path.split("/").slice(-2, -1)[0] : "Root";
  }
  function formatStoreForInspectorTree(module, path) {
    return {
      id: path || "root",
      // all modules end with a `/`, we want the last segment only
      // cart/ -> cart
      // nested/cart/ -> cart
      label: extractNameFromPath(path),
      tags: module.namespaced ? [TAG_NAMESPACED] : [],
      children: Object.keys(module._children).map(
        function(moduleName) {
          return formatStoreForInspectorTree(
            module._children[moduleName],
            path + moduleName + "/"
          );
        }
      )
    };
  }
  function flattenStoreForInspectorTree(result, module, filter, path) {
    if (path.includes(filter)) {
      result.push({
        id: path || "root",
        label: path.endsWith("/") ? path.slice(0, path.length - 1) : path || "Root",
        tags: module.namespaced ? [TAG_NAMESPACED] : []
      });
    }
    Object.keys(module._children).forEach(function(moduleName) {
      flattenStoreForInspectorTree(result, module._children[moduleName], filter, path + moduleName + "/");
    });
  }
  function formatStoreForInspectorState(module, getters, path) {
    getters = path === "root" ? getters : getters[path];
    var gettersKeys = Object.keys(getters);
    var storeState = {
      state: Object.keys(module.state).map(function(key) {
        return {
          key,
          editable: true,
          value: module.state[key]
        };
      })
    };
    if (gettersKeys.length) {
      var tree = transformPathsToObjectTree(getters);
      storeState.getters = Object.keys(tree).map(function(key) {
        return {
          key: key.endsWith("/") ? extractNameFromPath(key) : key,
          editable: false,
          value: canThrow(function() {
            return tree[key];
          })
        };
      });
    }
    return storeState;
  }
  function transformPathsToObjectTree(getters) {
    var result = {};
    Object.keys(getters).forEach(function(key) {
      var path = key.split("/");
      if (path.length > 1) {
        var target = result;
        var leafKey = path.pop();
        path.forEach(function(p) {
          if (!target[p]) {
            target[p] = {
              _custom: {
                value: {},
                display: p,
                tooltip: "Module",
                abstract: true
              }
            };
          }
          target = target[p]._custom.value;
        });
        target[leafKey] = canThrow(function() {
          return getters[key];
        });
      } else {
        result[key] = canThrow(function() {
          return getters[key];
        });
      }
    });
    return result;
  }
  function getStoreModule(moduleMap, path) {
    var names = path.split("/").filter(function(n) {
      return n;
    });
    return names.reduce(
      function(module, moduleName, i) {
        var child = module[moduleName];
        if (!child) {
          throw new Error('Missing module "' + moduleName + '" for path "' + path + '".');
        }
        return i === names.length - 1 ? child : child._children;
      },
      path === "root" ? moduleMap : moduleMap.root._children
    );
  }
  function canThrow(cb) {
    try {
      return cb();
    } catch (e) {
      return e;
    }
  }
  var Module = function Module2(rawModule, runtime) {
    this.runtime = runtime;
    this._children = /* @__PURE__ */ Object.create(null);
    this._rawModule = rawModule;
    var rawState = rawModule.state;
    this.state = (typeof rawState === "function" ? rawState() : rawState) || {};
  };
  var prototypeAccessors$1 = { namespaced: { configurable: true } };
  prototypeAccessors$1.namespaced.get = function() {
    return !!this._rawModule.namespaced;
  };
  Module.prototype.addChild = function addChild(key, module) {
    this._children[key] = module;
  };
  Module.prototype.removeChild = function removeChild(key) {
    delete this._children[key];
  };
  Module.prototype.getChild = function getChild(key) {
    return this._children[key];
  };
  Module.prototype.hasChild = function hasChild(key) {
    return key in this._children;
  };
  Module.prototype.update = function update(rawModule) {
    this._rawModule.namespaced = rawModule.namespaced;
    if (rawModule.actions) {
      this._rawModule.actions = rawModule.actions;
    }
    if (rawModule.mutations) {
      this._rawModule.mutations = rawModule.mutations;
    }
    if (rawModule.getters) {
      this._rawModule.getters = rawModule.getters;
    }
  };
  Module.prototype.forEachChild = function forEachChild(fn) {
    forEachValue(this._children, fn);
  };
  Module.prototype.forEachGetter = function forEachGetter(fn) {
    if (this._rawModule.getters) {
      forEachValue(this._rawModule.getters, fn);
    }
  };
  Module.prototype.forEachAction = function forEachAction(fn) {
    if (this._rawModule.actions) {
      forEachValue(this._rawModule.actions, fn);
    }
  };
  Module.prototype.forEachMutation = function forEachMutation(fn) {
    if (this._rawModule.mutations) {
      forEachValue(this._rawModule.mutations, fn);
    }
  };
  Object.defineProperties(Module.prototype, prototypeAccessors$1);
  var ModuleCollection = function ModuleCollection2(rawRootModule) {
    this.register([], rawRootModule, false);
  };
  ModuleCollection.prototype.get = function get(path) {
    return path.reduce(function(module, key) {
      return module.getChild(key);
    }, this.root);
  };
  ModuleCollection.prototype.getNamespace = function getNamespace(path) {
    var module = this.root;
    return path.reduce(function(namespace, key) {
      module = module.getChild(key);
      return namespace + (module.namespaced ? key + "/" : "");
    }, "");
  };
  ModuleCollection.prototype.update = function update$1(rawRootModule) {
    update2([], this.root, rawRootModule);
  };
  ModuleCollection.prototype.register = function register(path, rawModule, runtime) {
    var this$1$1 = this;
    if (runtime === void 0)
      runtime = true;
    {
      assertRawModule(path, rawModule);
    }
    var newModule = new Module(rawModule, runtime);
    if (path.length === 0) {
      this.root = newModule;
    } else {
      var parent = this.get(path.slice(0, -1));
      parent.addChild(path[path.length - 1], newModule);
    }
    if (rawModule.modules) {
      forEachValue(rawModule.modules, function(rawChildModule, key) {
        this$1$1.register(path.concat(key), rawChildModule, runtime);
      });
    }
  };
  ModuleCollection.prototype.unregister = function unregister(path) {
    var parent = this.get(path.slice(0, -1));
    var key = path[path.length - 1];
    var child = parent.getChild(key);
    if (!child) {
      {
        console.warn(
          "[vuex] trying to unregister module '" + key + "', which is not registered"
        );
      }
      return;
    }
    if (!child.runtime) {
      return;
    }
    parent.removeChild(key);
  };
  ModuleCollection.prototype.isRegistered = function isRegistered(path) {
    var parent = this.get(path.slice(0, -1));
    var key = path[path.length - 1];
    if (parent) {
      return parent.hasChild(key);
    }
    return false;
  };
  function update2(path, targetModule, newModule) {
    {
      assertRawModule(path, newModule);
    }
    targetModule.update(newModule);
    if (newModule.modules) {
      for (var key in newModule.modules) {
        if (!targetModule.getChild(key)) {
          {
            console.warn(
              "[vuex] trying to add a new module '" + key + "' on hot reloading, manual reload is needed"
            );
          }
          return;
        }
        update2(
          path.concat(key),
          targetModule.getChild(key),
          newModule.modules[key]
        );
      }
    }
  }
  var functionAssert = {
    assert: function(value) {
      return typeof value === "function";
    },
    expected: "function"
  };
  var objectAssert = {
    assert: function(value) {
      return typeof value === "function" || typeof value === "object" && typeof value.handler === "function";
    },
    expected: 'function or object with "handler" function'
  };
  var assertTypes = {
    getters: functionAssert,
    mutations: functionAssert,
    actions: objectAssert
  };
  function assertRawModule(path, rawModule) {
    Object.keys(assertTypes).forEach(function(key) {
      if (!rawModule[key]) {
        return;
      }
      var assertOptions = assertTypes[key];
      forEachValue(rawModule[key], function(value, type) {
        assert(
          assertOptions.assert(value),
          makeAssertionMessage(path, key, type, value, assertOptions.expected)
        );
      });
    });
  }
  function makeAssertionMessage(path, key, type, value, expected) {
    var buf = key + " should be " + expected + ' but "' + key + "." + type + '"';
    if (path.length > 0) {
      buf += ' in module "' + path.join(".") + '"';
    }
    buf += " is " + JSON.stringify(value) + ".";
    return buf;
  }
  function createStore(options) {
    return new Store(options);
  }
  var Store = function Store2(options) {
    var this$1$1 = this;
    if (options === void 0)
      options = {};
    {
      assert(typeof Promise !== "undefined", "vuex requires a Promise polyfill in this browser.");
      assert(this instanceof Store2, "store must be called with the new operator.");
    }
    var plugins = options.plugins;
    if (plugins === void 0)
      plugins = [];
    var strict = options.strict;
    if (strict === void 0)
      strict = false;
    var devtools = options.devtools;
    this._committing = false;
    this._actions = /* @__PURE__ */ Object.create(null);
    this._actionSubscribers = [];
    this._mutations = /* @__PURE__ */ Object.create(null);
    this._wrappedGetters = /* @__PURE__ */ Object.create(null);
    this._modules = new ModuleCollection(options);
    this._modulesNamespaceMap = /* @__PURE__ */ Object.create(null);
    this._subscribers = [];
    this._makeLocalGettersCache = /* @__PURE__ */ Object.create(null);
    this._scope = null;
    this._devtools = devtools;
    var store2 = this;
    var ref = this;
    var dispatch2 = ref.dispatch;
    var commit2 = ref.commit;
    this.dispatch = function boundDispatch(type, payload) {
      return dispatch2.call(store2, type, payload);
    };
    this.commit = function boundCommit(type, payload, options2) {
      return commit2.call(store2, type, payload, options2);
    };
    this.strict = strict;
    var state = this._modules.root.state;
    installModule(this, state, [], this._modules.root);
    resetStoreState(this, state);
    plugins.forEach(function(plugin) {
      return plugin(this$1$1);
    });
  };
  var prototypeAccessors = { state: { configurable: true } };
  Store.prototype.install = function install(app, injectKey) {
    app.provide(injectKey || storeKey, this);
    app.config.globalProperties.$store = this;
    var useDevtools = this._devtools !== void 0 ? this._devtools : true;
    if (useDevtools) {
      addDevtools(app, this);
    }
  };
  prototypeAccessors.state.get = function() {
    return this._state.data;
  };
  prototypeAccessors.state.set = function(v) {
    {
      assert(false, "use store.replaceState() to explicit replace store state.");
    }
  };
  Store.prototype.commit = function commit(_type, _payload, _options) {
    var this$1$1 = this;
    var ref = unifyObjectStyle(_type, _payload, _options);
    var type = ref.type;
    var payload = ref.payload;
    var options = ref.options;
    var mutation = { type, payload };
    var entry = this._mutations[type];
    if (!entry) {
      {
        console.error("[vuex] unknown mutation type: " + type);
      }
      return;
    }
    this._withCommit(function() {
      entry.forEach(function commitIterator(handler) {
        handler(payload);
      });
    });
    this._subscribers.slice().forEach(function(sub) {
      return sub(mutation, this$1$1.state);
    });
    if (options && options.silent) {
      console.warn(
        "[vuex] mutation type: " + type + ". Silent option has been removed. Use the filter functionality in the vue-devtools"
      );
    }
  };
  Store.prototype.dispatch = function dispatch(_type, _payload) {
    var this$1$1 = this;
    var ref = unifyObjectStyle(_type, _payload);
    var type = ref.type;
    var payload = ref.payload;
    var action = { type, payload };
    var entry = this._actions[type];
    if (!entry) {
      {
        console.error("[vuex] unknown action type: " + type);
      }
      return;
    }
    try {
      this._actionSubscribers.slice().filter(function(sub) {
        return sub.before;
      }).forEach(function(sub) {
        return sub.before(action, this$1$1.state);
      });
    } catch (e) {
      {
        console.warn("[vuex] error in before action subscribers: ");
        console.error(e);
      }
    }
    var result = entry.length > 1 ? Promise.all(entry.map(function(handler) {
      return handler(payload);
    })) : entry[0](payload);
    return new Promise(function(resolve, reject) {
      result.then(function(res) {
        try {
          this$1$1._actionSubscribers.filter(function(sub) {
            return sub.after;
          }).forEach(function(sub) {
            return sub.after(action, this$1$1.state);
          });
        } catch (e) {
          {
            console.warn("[vuex] error in after action subscribers: ");
            console.error(e);
          }
        }
        resolve(res);
      }, function(error) {
        try {
          this$1$1._actionSubscribers.filter(function(sub) {
            return sub.error;
          }).forEach(function(sub) {
            return sub.error(action, this$1$1.state, error);
          });
        } catch (e) {
          {
            console.warn("[vuex] error in error action subscribers: ");
            console.error(e);
          }
        }
        reject(error);
      });
    });
  };
  Store.prototype.subscribe = function subscribe(fn, options) {
    return genericSubscribe(fn, this._subscribers, options);
  };
  Store.prototype.subscribeAction = function subscribeAction(fn, options) {
    var subs = typeof fn === "function" ? { before: fn } : fn;
    return genericSubscribe(subs, this._actionSubscribers, options);
  };
  Store.prototype.watch = function watch$1(getter, cb, options) {
    var this$1$1 = this;
    {
      assert(typeof getter === "function", "store.watch only accepts a function.");
    }
    return vue.watch(function() {
      return getter(this$1$1.state, this$1$1.getters);
    }, cb, Object.assign({}, options));
  };
  Store.prototype.replaceState = function replaceState(state) {
    var this$1$1 = this;
    this._withCommit(function() {
      this$1$1._state.data = state;
    });
  };
  Store.prototype.registerModule = function registerModule(path, rawModule, options) {
    if (options === void 0)
      options = {};
    if (typeof path === "string") {
      path = [path];
    }
    {
      assert(Array.isArray(path), "module path must be a string or an Array.");
      assert(path.length > 0, "cannot register the root module by using registerModule.");
    }
    this._modules.register(path, rawModule);
    installModule(this, this.state, path, this._modules.get(path), options.preserveState);
    resetStoreState(this, this.state);
  };
  Store.prototype.unregisterModule = function unregisterModule(path) {
    var this$1$1 = this;
    if (typeof path === "string") {
      path = [path];
    }
    {
      assert(Array.isArray(path), "module path must be a string or an Array.");
    }
    this._modules.unregister(path);
    this._withCommit(function() {
      var parentState = getNestedState(this$1$1.state, path.slice(0, -1));
      delete parentState[path[path.length - 1]];
    });
    resetStore(this);
  };
  Store.prototype.hasModule = function hasModule(path) {
    if (typeof path === "string") {
      path = [path];
    }
    {
      assert(Array.isArray(path), "module path must be a string or an Array.");
    }
    return this._modules.isRegistered(path);
  };
  Store.prototype.hotUpdate = function hotUpdate(newOptions) {
    this._modules.update(newOptions);
    resetStore(this, true);
  };
  Store.prototype._withCommit = function _withCommit(fn) {
    var committing = this._committing;
    this._committing = true;
    fn();
    this._committing = committing;
  };
  Object.defineProperties(Store.prototype, prototypeAccessors);
  const DB_NAME = "myaccount.db";
  const DB_PATH = "_doc/myaccount.db";
  let isDatabaseOpen = false;
  const openDatabase = async () => {
    if (isDatabaseOpen) {
      formatAppLog("log", "at utils/db.js:10", "数据库已经打开");
      return;
    }
    return new Promise((resolve, reject) => {
      formatAppLog("log", "at utils/db.js:15", "尝试打开数据库");
      plus.sqlite.openDatabase({
        name: DB_NAME,
        path: DB_PATH,
        success: () => {
          formatAppLog("log", "at utils/db.js:20", "数据库打开成功");
          isDatabaseOpen = true;
          resolve();
        },
        fail: (e) => {
          if (e.code === -1402) {
            formatAppLog("log", "at utils/db.js:27", "数据库已经打开");
            isDatabaseOpen = true;
            resolve();
            return;
          }
          formatAppLog("error", "at utils/db.js:32", "数据库打开失败，错误详情:", e);
          reject(new Error("数据库打开失败: " + JSON.stringify(e)));
        }
      });
    });
  };
  const closeDatabase = async () => {
    if (!isDatabaseOpen) {
      formatAppLog("log", "at utils/db.js:42", "数据库已经关闭");
      return;
    }
    return new Promise((resolve, reject) => {
      formatAppLog("log", "at utils/db.js:47", "尝试关闭数据库");
      plus.sqlite.closeDatabase({
        name: DB_NAME,
        success: () => {
          formatAppLog("log", "at utils/db.js:51", "数据库关闭成功");
          isDatabaseOpen = false;
          resolve();
        },
        fail: (e) => {
          if (e.code === -1401) {
            formatAppLog("log", "at utils/db.js:58", "数据库已经关闭");
            isDatabaseOpen = false;
            resolve();
            return;
          }
          formatAppLog("error", "at utils/db.js:63", "数据库关闭失败，错误详情:", e);
          reject(new Error("数据库关闭失败: " + JSON.stringify(e)));
        }
      });
    });
  };
  const executeSqlOperation = async (operation, sql, params = [], dbName = DB_NAME) => {
    try {
      const operationType = operation === "execute" ? "SQL" : "查询";
      formatAppLog("log", "at utils/db.js:74", `准备执行${operationType}:`, { sql, params });
      if (!isDatabaseOpen && dbName === DB_NAME) {
        await openDatabase();
      }
      const values = Array.isArray(params) ? params : [params];
      formatAppLog("log", "at utils/db.js:84", `${operationType}详情:`, {
        sql,
        params: values,
        paramsType: values.map((p) => typeof p),
        paramsLength: values.length
      });
      let finalSql = sql;
      values.forEach((value) => {
        const placeholder = "?";
        const position = finalSql.indexOf(placeholder);
        if (position !== -1) {
          const quotedValue = typeof value === "string" ? `'${value}'` : value;
          finalSql = finalSql.substring(0, position) + quotedValue + finalSql.substring(position + 1);
        }
      });
      formatAppLog("log", "at utils/db.js:102", `最终执行的${operationType}:`, finalSql);
      return new Promise((resolve, reject) => {
        const method = operation === "execute" ? "executeSql" : "selectSql";
        plus.sqlite[method]({
          name: dbName,
          sql: finalSql,
          success: (res) => {
            formatAppLog("log", "at utils/db.js:110", `${operationType}执行成功:`, { sql: finalSql, result: res });
            resolve(res);
          },
          fail: (e) => {
            formatAppLog("error", "at utils/db.js:114", `${operationType}执行失败:`, {
              sql: finalSql,
              error: e,
              errorCode: e.code,
              errorMessage: e.message
            });
            reject(new Error(`${operationType}执行失败: ` + JSON.stringify(e)));
          }
        });
      });
    } catch (error) {
      formatAppLog("error", "at utils/db.js:125", `执行${operation === "execute" ? "SQL" : "查询SQL"}失败:`, error);
      throw error;
    }
  };
  const executeSql = async (sql, params = [], dbName = DB_NAME) => {
    return executeSqlOperation("execute", sql, params, dbName);
  };
  const selectSql = async (sql, params = [], dbName = DB_NAME) => {
    return executeSqlOperation("select", sql, params, dbName);
  };
  const initDatabase = async () => {
    try {
      formatAppLog("log", "at utils/db.js:143", "开始初始化数据库");
      await openDatabase();
      await executeSql(`
            CREATE TABLE IF NOT EXISTS users (
                user_id TEXT PRIMARY KEY,
                username TEXT NOT NULL CHECK(username != ''),
                password TEXT NOT NULL,
                email TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(username),
                UNIQUE(email)
            )
        `);
      await executeSql(`
            CREATE TABLE IF NOT EXISTS categories (
                category_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                parent_id TEXT,
                name TEXT NOT NULL,
                level INTEGER NOT NULL,
                sort_order INTEGER NOT NULL,
                total_amount REAL DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);
      await executeSql(`
            CREATE TABLE IF NOT EXISTS expenses (
                record_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                category_id TEXT NOT NULL,
                target TEXT,
                amount REAL NOT NULL,
                record_date DATE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);
      formatAppLog("log", "at utils/db.js:186", "数据库表初始化成功");
    } catch (error) {
      formatAppLog("error", "at utils/db.js:188", "数据库初始化失败:", error);
      throw error;
    }
  };
  const getAllTables = async () => {
    try {
      formatAppLog("log", "at utils/db.js:196", "开始获取所有表名");
      const results = await selectSql(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      );
      formatAppLog("log", "at utils/db.js:200", "获取到的表名:", results);
      return results.map((row) => row.name);
    } catch (error) {
      formatAppLog("error", "at utils/db.js:203", "获取表名失败:", error);
      throw error;
    }
  };
  const getAllFromTable = async (tableName) => {
    try {
      formatAppLog("log", "at utils/db.js:211", `开始获取表 ${tableName} 的所有数据`);
      const results = await selectSql(`SELECT * FROM ${tableName}`);
      formatAppLog("log", "at utils/db.js:213", `表 ${tableName} 数据获取成功:`, results.length, "条记录");
      return results;
    } catch (error) {
      formatAppLog("error", "at utils/db.js:216", `获取表 ${tableName} 数据失败:`, error);
      throw error;
    }
  };
  const getTableCreateStatement = async (tableName) => {
    try {
      formatAppLog("log", "at utils/db.js:224", `开始获取表 ${tableName} 的创建语句`);
      const results = await selectSql(
        "SELECT sql FROM sqlite_master WHERE type='table' AND name=?",
        [tableName]
      );
      if (results.length > 0) {
        formatAppLog("log", "at utils/db.js:230", `表 ${tableName} 创建语句:`, results[0].sql);
        return results[0].sql;
      }
      throw new Error(`表 ${tableName} 不存在`);
    } catch (error) {
      formatAppLog("error", "at utils/db.js:235", `获取表 ${tableName} 创建语句失败:`, error);
      throw error;
    }
  };
  const createBackupDatabase = async (filePath) => {
    try {
      formatAppLog("log", "at utils/db.js:243", "开始创建备份数据库:", filePath);
      try {
        await new Promise((resolve) => {
          plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
            entry.remove(() => {
              formatAppLog("log", "at utils/db.js:250", "已删除旧的备份数据库文件");
              resolve();
            }, (error) => {
              formatAppLog("log", "at utils/db.js:253", "删除旧文件失败，可能不存在:", error);
              resolve();
            });
          }, (error) => {
            formatAppLog("log", "at utils/db.js:257", "旧文件不存在，继续创建新文件");
            resolve();
          });
        });
        await new Promise((resolve) => setTimeout(resolve, 200));
      } catch (error) {
        formatAppLog("log", "at utils/db.js:265", "处理旧文件时出错，继续创建新文件:", error);
      }
      await new Promise((resolve, reject) => {
        plus.sqlite.openDatabase({
          name: "backup_db",
          path: filePath,
          success: function(e) {
            formatAppLog("log", "at utils/db.js:274", "备份数据库创建成功");
            resolve();
          },
          fail: function(e) {
            formatAppLog("error", "at utils/db.js:278", "备份数据库创建失败:", e);
            reject(new Error("创建备份数据库失败: " + JSON.stringify(e)));
          }
        });
      });
      const initSettings = [
        // 设置同步模式为NORMAL，在写入数据时更高效
        {
          sql: "PRAGMA synchronous=NORMAL",
          message: "已设置同步模式为NORMAL"
        },
        // 设置日志模式为DELETE，避免WAL模式的问题
        {
          sql: "PRAGMA journal_mode=DELETE",
          message: "已设置日志模式为DELETE"
        },
        // 设置页面缓存大小
        {
          sql: "PRAGMA page_size=4096",
          message: "已设置页面大小为4KB"
        },
        // 设置缓存大小
        {
          sql: "PRAGMA cache_size=2000",
          message: "已设置缓存大小"
        }
      ];
      for (const setting of initSettings) {
        try {
          await new Promise((resolve) => {
            plus.sqlite.executeSql({
              name: "backup_db",
              sql: setting.sql,
              success: function() {
                formatAppLog("log", "at utils/db.js:316", setting.message);
                resolve();
              },
              fail: function(e) {
                formatAppLog("log", "at utils/db.js:320", `设置 ${setting.sql} 失败，继续执行:`, e);
                resolve();
              }
            });
          });
        } catch (error) {
          formatAppLog("log", "at utils/db.js:326", `执行 ${setting.sql} 时出错，继续执行:`, error);
        }
      }
      return "backup_db";
    } catch (error) {
      formatAppLog("error", "at utils/db.js:332", "创建备份数据库失败:", error);
      throw error;
    }
  };
  const recreateTableInBackup = async (backupDbName, tableName, data) => {
    try {
      formatAppLog("log", "at utils/db.js:439", `开始在备份数据库中重建表 ${tableName}`);
      const createStatement = await getTableCreateStatement(tableName);
      formatAppLog("log", "at utils/db.js:443", `表 ${tableName} 的创建语句:`, createStatement);
      await executeSql(`DROP TABLE IF EXISTS ${tableName}`, [], backupDbName).then(() => formatAppLog("log", "at utils/db.js:447", `已删除旧的 ${tableName} 表`)).catch((e) => formatAppLog("log", "at utils/db.js:448", `删除表 ${tableName} 时出错:`, e));
      await executeSql(createStatement, [], backupDbName).then(() => formatAppLog("log", "at utils/db.js:452", `表 ${tableName} 创建成功`)).catch((e) => {
        formatAppLog("error", "at utils/db.js:454", `创建表 ${tableName} 失败:`, e);
        throw e;
      });
      if (data && data.length > 0) {
        formatAppLog("log", "at utils/db.js:460", `开始插入表 ${tableName} 的数据:`, data.length, "条记录");
        await executeSql("BEGIN TRANSACTION", [], backupDbName).then(() => formatAppLog("log", "at utils/db.js:464", `开始事务: 表 ${tableName}`)).catch((e) => {
          formatAppLog("error", "at utils/db.js:466", `开始事务失败:`, e);
          throw e;
        });
        try {
          const BATCH_SIZE = 100;
          let batchCount = 0;
          for (let i = 0; i < data.length; i += BATCH_SIZE) {
            const batch = data.slice(i, i + BATCH_SIZE);
            batchCount++;
            formatAppLog("log", "at utils/db.js:478", `开始处理第 ${batchCount} 批数据，共 ${batch.length} 条`);
            for (const row of batch) {
              const columns = Object.keys(row);
              const values = columns.map((col) => {
                const value = row[col];
                if (value === null || value === void 0) {
                  return "NULL";
                }
                if (typeof value === "string") {
                  return `'${value.replace(/'/g, "''")}'`;
                }
                if (value instanceof Date) {
                  return `'${value.toISOString()}'`;
                }
                return value.toString();
              });
              const insertSql = `INSERT INTO ${tableName} (${columns.join(",")}) VALUES (${values.join(",")})`;
              formatAppLog("log", "at utils/db.js:500", `执行SQL:`, insertSql);
              await new Promise((resolve, reject) => {
                plus.sqlite.executeSql({
                  name: backupDbName,
                  sql: insertSql,
                  success: function() {
                    resolve();
                  },
                  fail: function(e) {
                    formatAppLog("error", "at utils/db.js:510", `插入数据失败:`, {
                      error: e,
                      sql: insertSql
                    });
                    reject(e);
                  }
                });
              });
            }
            if (i + BATCH_SIZE < data.length) {
              await executeSql("COMMIT", [], backupDbName).then(() => formatAppLog("log", "at utils/db.js:524", `提交第 ${batchCount} 批数据事务`)).catch((e) => {
                formatAppLog("error", "at utils/db.js:526", `提交第 ${batchCount} 批数据事务失败:`, e);
                throw e;
              });
              await executeSql("BEGIN TRANSACTION", [], backupDbName).then(() => formatAppLog("log", "at utils/db.js:532", `开始第 ${batchCount + 1} 批数据事务`)).catch((e) => {
                formatAppLog("error", "at utils/db.js:534", `开始第 ${batchCount + 1} 批数据事务失败:`, e);
                throw e;
              });
              await new Promise((resolve) => setTimeout(resolve, 100));
            }
          }
          await executeSql("COMMIT", [], backupDbName).then(() => formatAppLog("log", "at utils/db.js:545", `提交最后一批数据事务: 表 ${tableName}`)).catch((e) => {
            formatAppLog("error", "at utils/db.js:547", `提交最后一批数据事务失败:`, e);
            throw e;
          });
          formatAppLog("log", "at utils/db.js:551", `表 ${tableName} 数据插入完成`);
        } catch (error) {
          formatAppLog("error", "at utils/db.js:554", `插入数据出错，准备回滚:`, error);
          await executeSql("ROLLBACK", [], backupDbName).then(() => formatAppLog("log", "at utils/db.js:556", `事务已回滚: 表 ${tableName}`)).catch((e) => formatAppLog("error", "at utils/db.js:557", `回滚事务失败:`, e));
          throw error;
        }
      }
    } catch (error) {
      formatAppLog("error", "at utils/db.js:562", `重建表 ${tableName} 失败:`, error);
      throw error;
    }
  };
  const closeBackupDatabase = async (backupDbName) => {
    try {
      formatAppLog("log", "at utils/db.js:570", "开始关闭备份数据库");
      const syncSteps = [
        // 步骤1: 提交所有活跃事务
        {
          sql: "COMMIT",
          message: "确保没有活跃事务",
          errorMessage: "没有活跃事务需要提交"
        },
        // 步骤2: 强制写入所有数据
        {
          sql: "PRAGMA locking_mode=EXCLUSIVE",
          message: "已设置独占锁定模式",
          errorMessage: "设置锁定模式失败"
        },
        // 步骤3: 设置同步模式为FULL
        {
          sql: "PRAGMA synchronous=FULL",
          message: "已设置同步模式为FULL",
          errorMessage: "设置同步模式失败"
        },
        // 步骤4: 切换日志模式为DELETE
        {
          sql: "PRAGMA journal_mode=DELETE",
          message: "已切换到DELETE日志模式",
          errorMessage: "切换日志模式失败"
        },
        // 步骤5: 执行检查点
        {
          sql: "PRAGMA wal_checkpoint(FULL)",
          message: "检查点完成",
          errorMessage: "检查点操作失败"
        }
      ];
      for (const step of syncSteps) {
        try {
          await executeSql(step.sql, [], backupDbName).catch((e) => {
            formatAppLog("log", "at utils/db.js:610", `${step.errorMessage}，继续执行:`, e);
          });
          formatAppLog("log", "at utils/db.js:612", step.message);
          await new Promise((resolve) => setTimeout(resolve, 100));
        } catch (error) {
          formatAppLog("log", "at utils/db.js:616", `执行 ${step.sql} 时出错，继续执行:`, error);
        }
      }
      try {
        await executeSql("PRAGMA optimize", [], backupDbName).catch((e) => {
          formatAppLog("log", "at utils/db.js:623", "数据库优化失败，继续执行:", e);
        });
        formatAppLog("log", "at utils/db.js:625", "数据库优化完成");
      } catch (error) {
        formatAppLog("log", "at utils/db.js:627", "数据库优化时出错，继续执行:", error);
      }
      await new Promise((resolve) => setTimeout(resolve, 500));
      let closeAttempts = 0;
      const maxAttempts = 3;
      while (closeAttempts < maxAttempts) {
        try {
          await new Promise((resolve, reject) => {
            plus.sqlite.closeDatabase({
              name: backupDbName,
              success: function() {
                formatAppLog("log", "at utils/db.js:643", `备份数据库关闭成功 (尝试 ${closeAttempts + 1}/${maxAttempts})`);
                resolve();
              },
              fail: function(e) {
                formatAppLog("error", "at utils/db.js:647", `备份数据库关闭失败 (尝试 ${closeAttempts + 1}/${maxAttempts}):`, e);
                if (closeAttempts === maxAttempts - 1) {
                  reject(new Error("关闭备份数据库失败: " + JSON.stringify(e)));
                } else {
                  resolve(false);
                }
              }
            });
          });
          break;
        } catch (error) {
          if (closeAttempts === maxAttempts - 1) {
            throw error;
          }
        }
        closeAttempts++;
        await new Promise((resolve) => setTimeout(resolve, 300));
      }
    } catch (error) {
      formatAppLog("error", "at utils/db.js:669", "关闭备份数据库失败:", error);
      throw error;
    }
  };
  const getDatabaseFilePath = async () => {
    try {
      formatAppLog("log", "at utils/db.js:677", "开始获取数据库文件路径");
      if (!isDatabaseOpen) {
        await openDatabase();
      }
      const fullPath = plus.io.convertLocalFileSystemURL(DB_PATH);
      formatAppLog("log", "at utils/db.js:686", "数据库文件的实际路径:", fullPath);
      return fullPath;
    } catch (error) {
      formatAppLog("error", "at utils/db.js:690", "获取数据库文件路径失败:", error);
      throw error;
    }
  };
  const isHarmonyOS = () => {
    try {
      const Build = plus.android.importClass("android.os.Build");
      const manufacturer = Build.MANUFACTURER;
      const model = Build.MODEL;
      const brand = Build.BRAND;
      formatAppLog("log", "at utils/db.js:703", "设备信息:", { manufacturer, model, brand });
      return manufacturer.toLowerCase().includes("huawei") || brand.toLowerCase().includes("huawei") || model.toLowerCase().includes("harmony") || model.toLowerCase().includes("hongmeng");
    } catch (error) {
      formatAppLog("error", "at utils/db.js:711", "检测设备系统时出错:", error);
      return false;
    }
  };
  const safeCloseDatabase = async () => {
    if (isDatabaseOpen) {
      await closeDatabase();
      await new Promise((resolve) => setTimeout(resolve, 1e3));
    }
  };
  const safeOpenDatabase = async () => {
    try {
      await openDatabase();
    } catch (e) {
      formatAppLog("error", "at utils/db.js:730", "重新打开数据库失败:", e);
    }
  };
  const safeCloseChannels = (sourceChannel, destChannel) => {
    try {
      if (sourceChannel)
        sourceChannel.close();
      if (destChannel)
        destChannel.close();
    } catch (e) {
      formatAppLog("error", "at utils/db.js:740", "关闭通道失败:", e);
    }
  };
  const backupDatabaseByFileCopy = async (targetPath) => {
    let sourceChannel = null;
    let destChannel = null;
    let sourceFis = null;
    let targetFos = null;
    try {
      formatAppLog("log", "at utils/db.js:752", "开始直接复制数据库文件到:", targetPath);
      const sourceFilePath = await getDatabaseFilePath();
      formatAppLog("log", "at utils/db.js:756", "原始数据库文件路径:", sourceFilePath);
      await safeCloseDatabase();
      const File = plus.android.importClass("java.io.File");
      const FileInputStream = plus.android.importClass("java.io.FileInputStream");
      const FileOutputStream = plus.android.importClass("java.io.FileOutputStream");
      const FileChannel = plus.android.importClass("java.nio.channels.FileChannel");
      const sourceFile = new File(sourceFilePath);
      if (!sourceFile.exists()) {
        throw new Error("源数据库文件不存在");
      }
      const targetFile = new File(targetPath);
      if (targetFile.exists()) {
        targetFile.delete();
      }
      const parentDir = targetFile.getParentFile();
      if (!parentDir.exists()) {
        parentDir.mkdirs();
      }
      sourceFis = new FileInputStream(sourceFile);
      targetFos = new FileOutputStream(targetFile);
      sourceChannel = sourceFis.getChannel();
      destChannel = targetFos.getChannel();
      destChannel.transferFrom(sourceChannel, 0, sourceChannel.size());
      formatAppLog("log", "at utils/db.js:797", "文件复制成功，大小:", targetFile.length());
      return targetFile.length();
    } catch (error) {
      formatAppLog("error", "at utils/db.js:801", "直接复制数据库文件失败:", error);
      throw error;
    } finally {
      safeCloseChannels(sourceChannel, destChannel);
      if (sourceFis)
        sourceFis.close();
      if (targetFos)
        targetFos.close();
      await safeOpenDatabase();
    }
  };
  const checkDatabaseOpen = () => {
    return isDatabaseOpen;
  };
  const isSupportVacuumInto = async () => {
    try {
      const versionResult = await selectSql("SELECT sqlite_version() as version");
      const sqliteVersion = versionResult[0].version;
      formatAppLog("log", "at utils/db.js:824", "SQLite版本:", sqliteVersion);
      const versionParts = sqliteVersion.split(".");
      const majorVersion = parseInt(versionParts[0]);
      const minorVersion = parseInt(versionParts[1]);
      return majorVersion > 3 || majorVersion === 3 && minorVersion >= 27;
    } catch (error) {
      formatAppLog("error", "at utils/db.js:834", "检查SQLite版本失败:", error);
      return false;
    }
  };
  const dbService = {
    openDatabase,
    closeDatabase,
    executeSql,
    selectSql,
    initDatabase,
    getAllTables,
    getAllFromTable,
    getTableCreateStatement,
    createBackupDatabase,
    recreateTableInBackup,
    closeBackupDatabase,
    getDatabaseFilePath,
    isDatabaseOpen: checkDatabaseOpen,
    backupDatabaseByFileCopy,
    isSupportVacuumInto,
    isHarmonyOS
  };
  const _imports_0$1 = "/static/logo.jpg";
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const _sfc_main$a = {
    setup() {
      const store2 = useStore();
      const isLoading = vue.ref(false);
      const currentInputs = vue.ref(/* @__PURE__ */ new Map());
      const tempTotals = vue.ref(/* @__PURE__ */ new Map());
      const isInitialized = vue.ref(false);
      const earliestDate = vue.ref(null);
      const showModal = vue.ref(false);
      const isProcessing = vue.ref(false);
      const aiInputText = vue.ref("");
      const categories = vue.computed(() => {
        formatAppLog("log", "at pages/index/index.vue:148", "Computing categories from store:", store2.state.categories);
        return store2.state.categories;
      });
      const handleLogout = async () => {
        try {
          isLoading.value = true;
          await store2.dispatch("logout");
          uni.navigateTo({
            url: "/pages/login/login"
          });
        } catch (error) {
          uni.showToast({
            title: "退出失败，请重试",
            icon: "none"
          });
        } finally {
          isLoading.value = false;
        }
      };
      const goToDetails = () => {
        uni.navigateTo({
          url: "/pages/details/details"
        });
      };
      const goToSettings = () => {
        formatAppLog("log", "at pages/index/index.vue:177", "点击设置按钮");
        uni.navigateTo({
          url: "/pages/settings/settings",
          success: () => {
            formatAppLog("log", "at pages/index/index.vue:181", "跳转成功");
          },
          fail: (err) => {
            formatAppLog("error", "at pages/index/index.vue:184", "跳转失败:", err);
          }
        });
      };
      const calculateTempTotal = (categoryId) => {
        const input = currentInputs.value.get(categoryId);
        const category = findCategoryById(categoryId);
        if (!category)
          return 0;
        const inputAmount = (input == null ? void 0 : input.amount) ? parseFloat(input.amount) : 0;
        return (category.total_amount || 0) + inputAmount;
      };
      const calculateCategoryTotal = (category) => {
        if (!category.children || category.children.length === 0) {
          const tempTotal = tempTotals.value.get(category.category_id);
          return tempTotal !== void 0 ? tempTotal : category.total_amount || 0;
        }
        return category.children.reduce((total, child) => {
          return total + calculateCategoryTotal(child);
        }, 0);
      };
      const calculatePercentage = (amount) => {
        const total = calculateTotalAmount();
        return total > 0 ? (amount / total * 100).toFixed(2) : "0";
      };
      const calculateTotalAmount = () => {
        if (!categories.value || categories.value.length === 0)
          return 0;
        return categories.value.reduce((total, category) => {
          return total + calculateCategoryTotal(category);
        }, 0);
      };
      const handleAmountInput = (categoryId, value) => {
        formatAppLog("log", "at pages/index/index.vue:227", "Handling amount input:", { categoryId, value });
        const input = currentInputs.value.get(categoryId) || { target: "", amount: "" };
        input.amount = value;
        currentInputs.value.set(categoryId, input);
        const tempTotal = calculateTempTotal(categoryId);
        tempTotals.value.set(categoryId, tempTotal);
      };
      const handleTargetInput = (categoryId, value) => {
        formatAppLog("log", "at pages/index/index.vue:239", "Handling target input:", { categoryId, value });
        const input = currentInputs.value.get(categoryId) || { target: "", amount: "" };
        input.target = value;
        currentInputs.value.set(categoryId, input);
      };
      const findCategoryById = (categoryId) => {
        const searchInCategories = (categories2) => {
          for (const category of categories2) {
            if (category.category_id === categoryId)
              return category;
            if (category.children && category.children.length > 0) {
              const found = searchInCategories(category.children);
              if (found)
                return found;
            }
          }
          return null;
        };
        return searchInCategories(categories.value);
      };
      const saveExpenses = async () => {
        const records = [];
        currentInputs.value.forEach((input, categoryId) => {
          if (input.amount && parseFloat(input.amount) !== 0) {
            records.push({
              category_id: categoryId,
              target: input.target || "",
              amount: parseFloat(input.amount)
            });
          }
        });
        if (records.length === 0) {
          uni.showToast({
            title: "请至少输入一条支出记录",
            icon: "none"
          });
          return;
        }
        isLoading.value = true;
        try {
          const now = /* @__PURE__ */ new Date();
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, "0");
          const day = String(now.getDate()).padStart(2, "0");
          const date = `${year}-${month}-${day}`;
          const result = await store2.dispatch("saveExpense", {
            date,
            records
          });
          if (result) {
            currentInputs.value.clear();
            tempTotals.value.clear();
            await fetchEarliestDate();
            uni.showToast({
              title: "保存成功",
              icon: "success"
            });
          }
        } catch (error) {
          formatAppLog("error", "at pages/index/index.vue:305", "保存支出失败:", error);
          uni.showToast({
            title: "保存失败，请重试",
            icon: "none"
          });
        } finally {
          isLoading.value = false;
        }
      };
      const fetchEarliestDate = async () => {
        try {
          const result = await dbService.selectSql(
            "SELECT MIN(record_date) as earliest_date FROM expenses WHERE user_id = ?",
            [store2.state.user.user_id]
          );
          if (result && result.length > 0 && result[0].earliest_date) {
            const date = new Date(result[0].earliest_date);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            earliestDate.value = `${year}年${month}月${day}日`;
          } else {
            earliestDate.value = "暂无记录";
          }
        } catch (error) {
          formatAppLog("error", "at pages/index/index.vue:333", "获取最早日期失败:", error);
          earliestDate.value = "暂无记录";
        }
      };
      const initializeData = async () => {
        if (isInitialized.value)
          return;
        try {
          formatAppLog("log", "at pages/index/index.vue:343", "开始获取分类数据...");
          const result = await store2.dispatch("fetchCategories");
          formatAppLog("log", "at pages/index/index.vue:345", "获取到的分类数据:", result);
          if (result && result.length > 0) {
            formatAppLog("log", "at pages/index/index.vue:348", "构建的分类树:", result);
            store2.commit("updateCategories", result);
            formatAppLog("log", "at pages/index/index.vue:350", "分类数据已更新到store");
          } else {
            formatAppLog("log", "at pages/index/index.vue:352", "未获取到分类数据");
          }
          isInitialized.value = true;
        } catch (error) {
          formatAppLog("error", "at pages/index/index.vue:356", "获取分类数据失败:", error);
        }
      };
      const showAiInputDialog = () => {
        showModal.value = true;
        aiInputText.value = "";
      };
      const closeModal = () => {
        if (!isProcessing.value) {
          showModal.value = false;
          aiInputText.value = "";
        }
      };
      const handleConfirm = async () => {
        if (!aiInputText.value.trim()) {
          uni.showToast({
            title: "请输入消费记录",
            icon: "none"
          });
          return;
        }
        isProcessing.value = true;
        try {
          await handleAiInput();
          showModal.value = false;
          aiInputText.value = "";
        } catch (error) {
          formatAppLog("error", "at pages/index/index.vue:390", "处理失败:", error);
        } finally {
          isProcessing.value = false;
        }
      };
      const getAllThirdLevelCategories = () => {
        const thirdLevelCategories = [];
        const traverse = (categories2) => {
          for (const category of categories2) {
            if (category.children && category.children.length > 0) {
              traverse(category.children);
            } else {
              thirdLevelCategories.push(category.name);
            }
          }
        };
        traverse(categories.value);
        return thirdLevelCategories;
      };
      const handleAiInput = async () => {
        if (!aiInputText.value.trim()) {
          uni.showToast({
            title: "请输入消费记录",
            icon: "none"
          });
          return;
        }
        const availableCategories = getAllThirdLevelCategories();
        formatAppLog("log", "at pages/index/index.vue:424", "可用的三级分类:", availableCategories);
        try {
          const response = await uni.request({
            url: "https://api.deepseek.com/v1/chat/completions",
            method: "POST",
            data: {
              messages: [
                {
                  role: "system",
                  content: `你是一个专业的消费记录分析助手。请分析用户输入的消费记录，并返回一个JSON数组，每个元素包含以下字段：
{
  "category_id": "分类ID",
  "target": "消费标的",
  "amount": 金额
}

可用的分类ID包括：
${availableCategories.map((cat) => `'${cat}'`).join(", ")}

非常重要的要求：
1. 只返回JSON数组，不要包含其他文字
2. 【特别重要】当用户输入多个同类消费时（如"早饭20，午饭30，晚饭30"），你必须将它们合并为一个元素，将金额相加（如80），并将消费标的合并（如"早饭,午饭,晚饭"）
3. 【特别重要】如果多个消费项目属于同一个分类ID，必须将它们合并为一个元素，将金额相加
4. 金额必须是数字类型，可以是负数（表示对冲错误记录），不要包含货币符号或单位
5. 分类ID必须从上述列表中选择，不要创建新的分类ID
6. 如果无法确定具体分类，使用列表中的最后一个分类作为默认分类

示例输入："早饭20，午饭30，晚饭30"
正确的输出（假设这些都属于"餐饮"分类）：
[{"category_id":"餐饮","target":"早饭,午饭,晚饭","amount":80}]

错误的输出（不要这样做）：
[{"category_id":"餐饮","target":"早饭","amount":20},{"category_id":"餐饮","target":"午饭","amount":30},{"category_id":"餐饮","target":"晚饭","amount":30}]`
                },
                {
                  role: "user",
                  content: aiInputText.value
                }
              ],
              model: "deepseek-chat",
              temperature: 0.2
            },
            header: {
              "Content-Type": "application/json",
              "Authorization": "Bearer sk-38ff2a6b9f754b059fa42839d5a4b426"
            }
          });
          if (response.statusCode === 200 && response.data) {
            try {
              const responseText = response.data.choices[0].message.content;
              const cleanResponseText = responseText.replace(/```json\n|\n```/g, "").trim();
              formatAppLog("log", "at pages/index/index.vue:479", "API 返回的原始数据:", cleanResponseText);
              let parsedResponse;
              try {
                parsedResponse = JSON.parse(cleanResponseText);
              } catch (e) {
                const jsonMatch = cleanResponseText.match(/\[.*\]/);
                if (jsonMatch) {
                  parsedResponse = JSON.parse(jsonMatch[0]);
                } else {
                  throw new Error("无法解析返回的 JSON 数据");
                }
              }
              if (Array.isArray(parsedResponse)) {
                const validEntries = parsedResponse.filter(
                  (entry) => entry.category_id && entry.target && typeof entry.amount === "number" && availableCategories.includes(entry.category_id)
                  // 验证分类ID是否有效
                );
                if (validEntries.length > 0) {
                  fillExpenseData(validEntries);
                  return true;
                } else {
                  throw new Error("API 返回的数据格式不正确：缺少必要字段或分类ID无效");
                }
              } else {
                throw new Error("API 返回的数据不是数组格式");
              }
            } catch (parseError) {
              formatAppLog("error", "at pages/index/index.vue:514", "JSON 解析错误:", parseError);
              throw new Error("API 返回数据格式不正确");
            }
          } else {
            throw new Error(`API 请求失败: ${response.statusCode}`);
          }
        } catch (error) {
          formatAppLog("error", "at pages/index/index.vue:521", "智能输入处理失败:", error);
          uni.showToast({
            title: error.message || "处理失败，请重试",
            icon: "none",
            duration: 3e3
          });
          throw error;
        }
      };
      const fillExpenseData = (categories2) => {
        const mergedData = /* @__PURE__ */ new Map();
        categories2.forEach((item) => {
          const targetCategory = findCategoryByName(item.category_id);
          if (!targetCategory) {
            formatAppLog("warn", "at pages/index/index.vue:540", `未找到匹配的分类: ${item.category_id}`);
            return;
          }
          const categoryId = targetCategory.category_id;
          if (mergedData.has(categoryId)) {
            const existingData = mergedData.get(categoryId);
            existingData.amount += item.amount;
            if (existingData.target !== item.target) {
              existingData.target = `${existingData.target}, ${item.target}`;
            }
          } else {
            mergedData.set(categoryId, {
              categoryId,
              target: item.target,
              amount: item.amount
            });
          }
        });
        mergedData.forEach((mergedItem, categoryId) => {
          const input = currentInputs.value.get(categoryId) || { target: "", amount: "" };
          if (input.amount) {
            const currentAmount = parseFloat(input.amount) || 0;
            mergedItem.amount += currentAmount;
            if (input.target && input.target !== mergedItem.target) {
              mergedItem.target = `${input.target}, ${mergedItem.target}`;
            }
          }
          input.target = mergedItem.target;
          input.amount = mergedItem.amount.toString();
          currentInputs.value.set(categoryId, input);
          const tempTotal = calculateTempTotal(categoryId);
          tempTotals.value.set(categoryId, tempTotal);
          const category = findCategoryById(categoryId);
          const categoryName = category ? category.name : categoryId;
          formatAppLog("log", "at pages/index/index.vue:590", `已填充数据到分类 ${categoryName}:`, input);
        });
      };
      const findCategoryByName = (categoryName) => {
        const searchInCategories = (categories2) => {
          for (const category of categories2) {
            if (!category.children || category.children.length === 0) {
              if (category.name === categoryName) {
                return category;
              }
            } else if (category.children) {
              for (const subCategory of category.children) {
                if (!subCategory.children || subCategory.children.length === 0) {
                  if (subCategory.name === categoryName) {
                    return subCategory;
                  }
                } else if (subCategory.children) {
                  for (const item of subCategory.children) {
                    if (item.name === categoryName) {
                      return item;
                    }
                  }
                }
              }
            }
          }
          return null;
        };
        return searchInCategories(categories.value);
      };
      vue.onMounted(async () => {
        formatAppLog("log", "at pages/index/index.vue:635", "页面加载，开始初始化数据...");
        formatAppLog("log", "at pages/index/index.vue:636", "当前用户状态:", store2.state.user);
        if (!store2.state.user) {
          formatAppLog("log", "at pages/index/index.vue:639", "用户未登录，跳转到登录页面");
          uni.reLaunch({
            url: "/pages/login/login"
          });
          return;
        }
        uni.setNavigationBarTitle({
          title: `${store2.state.user.username}的账本`
        });
        await Promise.all([
          initializeData(),
          fetchEarliestDate()
        ]);
        formatAppLog("log", "at pages/index/index.vue:655", "数据初始化完成");
      });
      return {
        store: store2,
        categories,
        isLoading,
        currentInputs,
        tempTotals,
        calculateCategoryTotal,
        calculatePercentage,
        calculateTotalAmount,
        handleAmountInput,
        handleTargetInput,
        saveExpenses,
        handleLogout,
        goToDetails,
        goToSettings,
        earliestDate,
        showModal,
        isProcessing,
        aiInputText,
        showAiInputDialog,
        closeModal,
        handleConfirm
      };
    }
  };
  function _sfc_render$9(_ctx, _cache, $props, $setup, $data, $options) {
    var _a;
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", { class: "header-content" }, [
          vue.createElementVNode(
            "view",
            { class: "start-date" },
            "始于:" + vue.toDisplayString($setup.earliestDate || "暂无记录"),
            1
            /* TEXT */
          ),
          vue.createElementVNode("image", {
            src: _imports_0$1,
            mode: "aspectFit",
            class: "logo"
          }),
          vue.createElementVNode(
            "text",
            { class: "title" },
            vue.toDisplayString(((_a = $setup.store.state.user) == null ? void 0 : _a.username) || "未登录") + "的账本",
            1
            /* TEXT */
          ),
          vue.createElementVNode("view", { class: "header-buttons" }, [
            vue.createElementVNode("view", {
              class: "header-button",
              onClick: _cache[0] || (_cache[0] = (...args) => $setup.goToDetails && $setup.goToDetails(...args))
            }, "明细"),
            vue.createElementVNode("view", {
              class: "header-button",
              onClick: _cache[1] || (_cache[1] = (...args) => $setup.goToSettings && $setup.goToSettings(...args))
            }, "设置")
          ])
        ])
      ]),
      vue.createElementVNode("scroll-view", {
        "scroll-y": "",
        class: "expense-list"
      }, [
        vue.createCommentVNode(" 表头 "),
        vue.createElementVNode("view", { class: "expense-row header-row" }, [
          vue.createElementVNode("view", { class: "cell category-cell" }, "分类"),
          vue.createElementVNode("view", { class: "cell target-cell" }, "标的"),
          vue.createElementVNode("view", { class: "cell amount-cell" }, "支出"),
          vue.createElementVNode("view", { class: "cell total-cell" }, "总额"),
          vue.createElementVNode("view", { class: "cell percentage-cell" }, "占比")
        ]),
        vue.createCommentVNode(" 总支出行 "),
        vue.createElementVNode("view", { class: "expense-row total-row" }, [
          vue.createElementVNode("view", { class: "cell category-cell" }, "总支出"),
          vue.createElementVNode("view", { class: "cell target-cell" }, "-"),
          vue.createElementVNode("view", { class: "cell amount-cell" }, "-"),
          vue.createElementVNode(
            "view",
            { class: "cell total-cell" },
            vue.toDisplayString($setup.calculateTotalAmount().toFixed(2)),
            1
            /* TEXT */
          ),
          vue.createElementVNode("view", { class: "cell percentage-cell" }, "100%")
        ]),
        vue.createElementVNode("view", { class: "expense-content" }, [
          vue.createCommentVNode(" 分类数据 "),
          $setup.categories && $setup.categories.length ? (vue.openBlock(), vue.createElementBlock(
            vue.Fragment,
            { key: 0 },
            [
              vue.createCommentVNode(" 一级分类 "),
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($setup.categories, (category, index) => {
                  return vue.openBlock(), vue.createElementBlock("view", {
                    key: category.category_id,
                    class: "category-group"
                  }, [
                    vue.createElementVNode("view", { class: "expense-row level-1" }, [
                      vue.createElementVNode(
                        "view",
                        { class: "cell category-cell" },
                        vue.toDisplayString(index + 1 + ". " + category.name),
                        1
                        /* TEXT */
                      ),
                      vue.createElementVNode("view", { class: "cell target-cell" }, "-"),
                      vue.createElementVNode("view", { class: "cell amount-cell" }, "-"),
                      vue.createElementVNode(
                        "view",
                        { class: "cell total-cell" },
                        vue.toDisplayString($setup.calculateCategoryTotal(category).toFixed(2)),
                        1
                        /* TEXT */
                      ),
                      vue.createElementVNode(
                        "view",
                        { class: "cell percentage-cell" },
                        vue.toDisplayString($setup.calculatePercentage($setup.calculateCategoryTotal(category))) + "%",
                        1
                        /* TEXT */
                      )
                    ]),
                    vue.createCommentVNode(" 二级分类 "),
                    category.children && category.children.length ? (vue.openBlock(true), vue.createElementBlock(
                      vue.Fragment,
                      { key: 0 },
                      vue.renderList(category.children, (subCategory, subIndex) => {
                        return vue.openBlock(), vue.createElementBlock("view", {
                          key: subCategory.category_id,
                          class: "category-group"
                        }, [
                          vue.createElementVNode("view", { class: "expense-row level-2" }, [
                            vue.createElementVNode(
                              "view",
                              { class: "cell category-cell" },
                              vue.toDisplayString(index + 1 + "." + (subIndex + 1) + " " + subCategory.name),
                              1
                              /* TEXT */
                            ),
                            vue.createElementVNode("view", { class: "cell target-cell" }, "-"),
                            vue.createElementVNode("view", { class: "cell amount-cell" }, "-"),
                            vue.createElementVNode(
                              "view",
                              { class: "cell total-cell" },
                              vue.toDisplayString($setup.calculateCategoryTotal(subCategory).toFixed(2)),
                              1
                              /* TEXT */
                            ),
                            vue.createElementVNode(
                              "view",
                              { class: "cell percentage-cell" },
                              vue.toDisplayString($setup.calculatePercentage($setup.calculateCategoryTotal(subCategory))) + "%",
                              1
                              /* TEXT */
                            )
                          ]),
                          vue.createCommentVNode(" 三级分类 "),
                          subCategory.children && subCategory.children.length ? (vue.openBlock(true), vue.createElementBlock(
                            vue.Fragment,
                            { key: 0 },
                            vue.renderList(subCategory.children, (item, itemIndex) => {
                              var _a2, _b;
                              return vue.openBlock(), vue.createElementBlock("view", {
                                key: item.category_id,
                                class: "expense-row level-3"
                              }, [
                                vue.createElementVNode(
                                  "view",
                                  { class: "cell category-cell" },
                                  vue.toDisplayString(item.name),
                                  1
                                  /* TEXT */
                                ),
                                vue.createElementVNode("view", { class: "cell target-cell" }, [
                                  vue.createElementVNode("input", {
                                    type: "text",
                                    value: ((_a2 = $setup.currentInputs.get(item.category_id)) == null ? void 0 : _a2.target) || "",
                                    onInput: (e) => $setup.handleTargetInput(item.category_id, e.detail.value),
                                    class: "input-field",
                                    placeholder: "输入标的"
                                  }, null, 40, ["value", "onInput"])
                                ]),
                                vue.createElementVNode("view", { class: "cell amount-cell" }, [
                                  vue.createElementVNode("input", {
                                    type: "digit",
                                    value: ((_b = $setup.currentInputs.get(item.category_id)) == null ? void 0 : _b.amount) || "",
                                    onInput: (e) => $setup.handleAmountInput(item.category_id, e.detail.value),
                                    class: "input-field",
                                    placeholder: "输入金额"
                                  }, null, 40, ["value", "onInput"])
                                ]),
                                vue.createElementVNode(
                                  "view",
                                  { class: "cell total-cell" },
                                  vue.toDisplayString(($setup.tempTotals.get(item.category_id) || item.total_amount || 0).toFixed(2)),
                                  1
                                  /* TEXT */
                                ),
                                vue.createElementVNode(
                                  "view",
                                  { class: "cell percentage-cell" },
                                  vue.toDisplayString($setup.calculatePercentage($setup.tempTotals.get(item.category_id) || item.total_amount || 0)) + "%",
                                  1
                                  /* TEXT */
                                )
                              ]);
                            }),
                            128
                            /* KEYED_FRAGMENT */
                          )) : vue.createCommentVNode("v-if", true)
                        ]);
                      }),
                      128
                      /* KEYED_FRAGMENT */
                    )) : vue.createCommentVNode("v-if", true)
                  ]);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ],
            64
            /* STABLE_FRAGMENT */
          )) : vue.createCommentVNode("v-if", true)
        ])
      ]),
      vue.createElementVNode("view", { class: "footer" }, [
        vue.createElementVNode("view", { class: "footer-content" }, [
          vue.createElementVNode("view", { class: "tip-text" }, "提示：可输入负数对冲错误记录"),
          vue.createElementVNode("view", { class: "button-row" }, [
            vue.createElementVNode("button", {
              class: "ai-input-button",
              onClick: _cache[2] || (_cache[2] = (...args) => $setup.showAiInputDialog && $setup.showAiInputDialog(...args))
            }, "智能输入"),
            vue.createElementVNode("button", {
              class: "save-button",
              onClick: _cache[3] || (_cache[3] = (...args) => $setup.saveExpenses && $setup.saveExpenses(...args)),
              disabled: $setup.isLoading
            }, vue.toDisplayString($setup.isLoading ? "保存中..." : "保存记录"), 9, ["disabled"])
          ])
        ])
      ]),
      vue.createCommentVNode(" 智能输入弹窗 "),
      $setup.showModal ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "ai-input-modal"
      }, [
        vue.createElementVNode("view", {
          class: "modal-mask",
          onClick: _cache[4] || (_cache[4] = (...args) => $setup.closeModal && $setup.closeModal(...args))
        }),
        vue.createElementVNode("view", { class: "modal-content" }, [
          vue.createElementVNode("view", { class: "modal-header" }, [
            vue.createElementVNode("text", { class: "modal-title" }, "智能输入"),
            vue.createElementVNode("text", {
              class: "modal-close",
              onClick: _cache[5] || (_cache[5] = (...args) => $setup.closeModal && $setup.closeModal(...args))
            }, "×")
          ]),
          vue.createElementVNode("view", { class: "modal-body" }, [
            vue.withDirectives(vue.createElementVNode(
              "textarea",
              {
                class: "ai-input-textarea",
                "onUpdate:modelValue": _cache[6] || (_cache[6] = ($event) => $setup.aiInputText = $event),
                placeholder: "请输入消费记录，例如：今天在超市买了水果花了50元，在餐厅吃饭花了100元"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $setup.aiInputText]
            ])
          ]),
          vue.createElementVNode("view", { class: "modal-footer" }, [
            vue.createElementVNode("button", {
              class: "modal-button cancel",
              onClick: _cache[7] || (_cache[7] = (...args) => $setup.closeModal && $setup.closeModal(...args))
            }, "取消"),
            vue.createElementVNode("button", {
              class: "modal-button confirm",
              onClick: _cache[8] || (_cache[8] = (...args) => $setup.handleConfirm && $setup.handleConfirm(...args)),
              disabled: $setup.isProcessing
            }, vue.toDisplayString($setup.isProcessing ? "处理中..." : "确定"), 9, ["disabled"])
          ])
        ])
      ])) : vue.createCommentVNode("v-if", true)
    ]);
  }
  const PagesIndexIndex = /* @__PURE__ */ _export_sfc(_sfc_main$a, [["render", _sfc_render$9], ["__file", "G:/myaccount/pages/index/index.vue"]]);
  const _sfc_main$9 = {
    setup() {
      const store2 = useStore();
      const username = vue.ref("");
      const password = vue.ref("");
      const isLoading = vue.ref(false);
      const errors = vue.ref({
        username: "",
        password: ""
      });
      const validateUsername = () => {
        if (!username.value) {
          errors.value.username = "用户名不能为空";
        } else if (username.value.length < 3) {
          errors.value.username = "用户名至少需要3个字符";
        } else {
          errors.value.username = "";
        }
      };
      const validatePassword = () => {
        if (!password.value) {
          errors.value.password = "密码不能为空";
        } else if (password.value.length < 6) {
          errors.value.password = "密码至少需要6个字符";
        } else {
          errors.value.password = "";
        }
      };
      const hasErrors = vue.computed(() => {
        return Object.values(errors.value).some((error) => error !== "") || !username.value || !password.value;
      });
      const handleLogin = async () => {
        validateUsername();
        validatePassword();
        if (hasErrors.value) {
          return;
        }
        isLoading.value = true;
        try {
          await store2.dispatch("login", {
            username: username.value,
            password: password.value
          });
          uni.showToast({
            title: "登录成功",
            icon: "success"
          });
          uni.reLaunch({
            url: "/pages/index/index"
          });
        } catch (error) {
          formatAppLog("error", "at pages/login/login.vue:122", "登录失败，完整错误:", error);
          let errorMessage = "登录失败";
          if (error.message.includes("用户不存在")) {
            errorMessage = "用户名不存在";
            errors.value.username = errorMessage;
          } else if (error.message.includes("密码错误")) {
            errorMessage = "密码错误";
            errors.value.password = errorMessage;
          }
          uni.showToast({
            title: errorMessage,
            icon: "none",
            duration: 2e3
          });
        } finally {
          isLoading.value = false;
        }
      };
      const goToRegister = () => {
        uni.navigateTo({
          url: "/pages/register/register"
        });
      };
      const goToForgotPassword = () => {
        uni.navigateTo({
          url: "/pages/forgot-password/forgot-password"
        });
      };
      return {
        username,
        password,
        isLoading,
        errors,
        hasErrors,
        handleLogin,
        goToRegister,
        goToForgotPassword,
        validateUsername,
        validatePassword
      };
    }
  };
  function _sfc_render$8(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", { class: "header-content" }, [
          vue.createElementVNode("image", {
            src: _imports_0$1,
            mode: "aspectFit",
            class: "logo"
          }),
          vue.createElementVNode("text", { class: "title" }, "账户登录")
        ])
      ]),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createElementVNode("view", { class: "form-content" }, [
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "label" }, "用户名"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                type: "text",
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.username = $event),
                placeholder: "请输入用户名",
                class: "input-field",
                onBlur: _cache[1] || (_cache[1] = (...args) => $setup.validateUsername && $setup.validateUsername(...args))
              },
              null,
              544
              /* NEED_HYDRATION, NEED_PATCH */
            ), [
              [vue.vModelText, $setup.username]
            ]),
            $setup.errors.username ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "error-text"
              },
              vue.toDisplayString($setup.errors.username),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ]),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "label" }, "密码"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                type: "password",
                "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $setup.password = $event),
                placeholder: "请输入密码",
                class: "input-field",
                onBlur: _cache[3] || (_cache[3] = (...args) => $setup.validatePassword && $setup.validatePassword(...args))
              },
              null,
              544
              /* NEED_HYDRATION, NEED_PATCH */
            ), [
              [vue.vModelText, $setup.password]
            ]),
            $setup.errors.password ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "error-text"
              },
              vue.toDisplayString($setup.errors.password),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ]),
          vue.createElementVNode("button", {
            class: "submit-button",
            onClick: _cache[4] || (_cache[4] = (...args) => $setup.handleLogin && $setup.handleLogin(...args)),
            disabled: $setup.isLoading || $setup.hasErrors
          }, vue.toDisplayString($setup.isLoading ? "登录中..." : "登录"), 9, ["disabled"]),
          vue.createElementVNode("view", { class: "links-container" }, [
            vue.createElementVNode("view", {
              class: "register-link",
              onClick: _cache[5] || (_cache[5] = (...args) => $setup.goToRegister && $setup.goToRegister(...args))
            }, " 还没有账号？点击注册 "),
            vue.createElementVNode("view", {
              class: "forgot-password-link",
              onClick: _cache[6] || (_cache[6] = (...args) => $setup.goToForgotPassword && $setup.goToForgotPassword(...args))
            }, " 忘记密码？ ")
          ])
        ])
      ])
    ]);
  }
  const PagesLoginLogin = /* @__PURE__ */ _export_sfc(_sfc_main$9, [["render", _sfc_render$8], ["__file", "G:/myaccount/pages/login/login.vue"]]);
  const _sfc_main$8 = {
    setup() {
      const store2 = useStore();
      const username = vue.ref("");
      const password = vue.ref("");
      const confirmPassword = vue.ref("");
      const email = vue.ref("");
      const isLoading = vue.ref(false);
      const errors = vue.ref({
        username: "",
        password: "",
        confirmPassword: "",
        email: ""
      });
      const validateUsername = () => {
        if (!username.value) {
          errors.value.username = "用户名不能为空";
        } else if (username.value.length < 3) {
          errors.value.username = "用户名至少需要3个字符";
        } else {
          errors.value.username = "";
        }
      };
      const validatePassword = () => {
        if (!password.value) {
          errors.value.password = "密码不能为空";
        } else if (password.value.length < 6) {
          errors.value.password = "密码至少需要6个字符";
        } else {
          errors.value.password = "";
        }
        if (confirmPassword.value) {
          validateConfirmPassword();
        }
      };
      const validateConfirmPassword = () => {
        if (!confirmPassword.value) {
          errors.value.confirmPassword = "请确认密码";
        } else if (confirmPassword.value !== password.value) {
          errors.value.confirmPassword = "两次输入的密码不一致";
        } else {
          errors.value.confirmPassword = "";
        }
      };
      const validateEmail = () => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!email.value) {
          errors.value.email = "邮箱不能为空";
        } else if (!emailRegex.test(email.value)) {
          errors.value.email = "请输入有效的邮箱地址";
        } else {
          errors.value.email = "";
        }
      };
      const hasErrors = vue.computed(() => {
        return Object.values(errors.value).some((error) => error !== "") || !username.value || !password.value || !confirmPassword.value || !email.value;
      });
      const validateAll = () => {
        validateUsername();
        validatePassword();
        validateConfirmPassword();
        validateEmail();
        return !hasErrors.value;
      };
      const handleRegister = async () => {
        if (!validateAll()) {
          return;
        }
        isLoading.value = true;
        try {
          await store2.dispatch("register", {
            username: username.value,
            password: password.value,
            email: email.value
          });
          uni.showToast({
            title: "注册成功",
            icon: "success"
          });
          setTimeout(() => {
            uni.redirectTo({
              url: "/pages/login/login"
            });
          }, 1500);
        } catch (error) {
          formatAppLog("error", "at pages/register/register.vue:180", "注册失败，完整错误:", error);
          let errorMessage = "注册失败";
          if (error.message.includes("用户名已存在")) {
            errorMessage = "用户名已被注册";
            errors.value.username = errorMessage;
          } else if (error.message.includes("邮箱已存在")) {
            errorMessage = "邮箱已被注册";
            errors.value.email = errorMessage;
          }
          uni.showToast({
            title: errorMessage,
            icon: "none",
            duration: 2e3
          });
        } finally {
          isLoading.value = false;
        }
      };
      const goToLogin = () => {
        uni.navigateTo({
          url: "/pages/login/login"
        });
      };
      return {
        username,
        password,
        confirmPassword,
        email,
        isLoading,
        errors,
        hasErrors,
        handleRegister,
        goToLogin,
        validateUsername,
        validatePassword,
        validateConfirmPassword,
        validateEmail
      };
    }
  };
  function _sfc_render$7(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", { class: "header-content" }, [
          vue.createElementVNode("image", {
            src: _imports_0$1,
            mode: "aspectFit",
            class: "logo"
          }),
          vue.createElementVNode("text", { class: "title" }, "账户注册")
        ])
      ]),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createElementVNode("view", { class: "form-content" }, [
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "label" }, "用户名"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                type: "text",
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.username = $event),
                placeholder: "请输入用户名（至少3个字符）",
                class: "input-field",
                onBlur: _cache[1] || (_cache[1] = (...args) => $setup.validateUsername && $setup.validateUsername(...args))
              },
              null,
              544
              /* NEED_HYDRATION, NEED_PATCH */
            ), [
              [vue.vModelText, $setup.username]
            ]),
            $setup.errors.username ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "error-text"
              },
              vue.toDisplayString($setup.errors.username),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ]),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "label" }, "密码"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                type: "password",
                "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $setup.password = $event),
                placeholder: "请输入密码（至少6个字符）",
                class: "input-field",
                onBlur: _cache[3] || (_cache[3] = (...args) => $setup.validatePassword && $setup.validatePassword(...args))
              },
              null,
              544
              /* NEED_HYDRATION, NEED_PATCH */
            ), [
              [vue.vModelText, $setup.password]
            ]),
            $setup.errors.password ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "error-text"
              },
              vue.toDisplayString($setup.errors.password),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ]),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "label" }, "确认密码"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                type: "password",
                "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => $setup.confirmPassword = $event),
                placeholder: "请再次输入密码",
                class: "input-field",
                onBlur: _cache[5] || (_cache[5] = (...args) => $setup.validateConfirmPassword && $setup.validateConfirmPassword(...args))
              },
              null,
              544
              /* NEED_HYDRATION, NEED_PATCH */
            ), [
              [vue.vModelText, $setup.confirmPassword]
            ]),
            $setup.errors.confirmPassword ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "error-text"
              },
              vue.toDisplayString($setup.errors.confirmPassword),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ]),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "label" }, "邮箱"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                type: "text",
                "onUpdate:modelValue": _cache[6] || (_cache[6] = ($event) => $setup.email = $event),
                placeholder: "请输入有效的邮箱地址",
                class: "input-field",
                onBlur: _cache[7] || (_cache[7] = (...args) => $setup.validateEmail && $setup.validateEmail(...args))
              },
              null,
              544
              /* NEED_HYDRATION, NEED_PATCH */
            ), [
              [vue.vModelText, $setup.email]
            ]),
            $setup.errors.email ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "error-text"
              },
              vue.toDisplayString($setup.errors.email),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ]),
          vue.createElementVNode("button", {
            class: "submit-button",
            onClick: _cache[8] || (_cache[8] = (...args) => $setup.handleRegister && $setup.handleRegister(...args)),
            disabled: $setup.isLoading || $setup.hasErrors
          }, vue.toDisplayString($setup.isLoading ? "注册中..." : "注册"), 9, ["disabled"]),
          vue.createElementVNode("view", {
            class: "login-link",
            onClick: _cache[9] || (_cache[9] = (...args) => $setup.goToLogin && $setup.goToLogin(...args))
          }, " 已有账号？点击登录 ")
        ])
      ])
    ]);
  }
  const PagesRegisterRegister = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["render", _sfc_render$7], ["__file", "G:/myaccount/pages/register/register.vue"]]);
  const _sfc_main$7 = {
    setup() {
      const store2 = useStore();
      const username = vue.ref("");
      const email = vue.ref("");
      const newPassword = vue.ref("");
      const confirmPassword = vue.ref("");
      const isLoading = vue.ref(false);
      const step = vue.ref(1);
      const errors = vue.ref({
        username: "",
        email: "",
        newPassword: "",
        confirmPassword: ""
      });
      const validateUsername = () => {
        if (!username.value) {
          errors.value.username = "用户名不能为空";
        } else if (username.value.length < 3) {
          errors.value.username = "用户名至少需要3个字符";
        } else {
          errors.value.username = "";
        }
      };
      const validateEmail = () => {
        if (!email.value) {
          errors.value.email = "邮箱不能为空";
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
          errors.value.email = "请输入有效的邮箱地址";
        } else {
          errors.value.email = "";
        }
      };
      const validateNewPassword = () => {
        if (!newPassword.value) {
          errors.value.newPassword = "新密码不能为空";
        } else if (newPassword.value.length < 6) {
          errors.value.newPassword = "新密码至少需要6个字符";
        } else {
          errors.value.newPassword = "";
        }
      };
      const validateConfirmPassword = () => {
        if (!confirmPassword.value) {
          errors.value.confirmPassword = "确认密码不能为空";
        } else if (confirmPassword.value !== newPassword.value) {
          errors.value.confirmPassword = "两次输入的密码不一致";
        } else {
          errors.value.confirmPassword = "";
        }
      };
      const hasErrors = vue.computed(() => {
        if (step.value === 1) {
          return Object.values(errors.value).some((error) => error !== "") || !username.value || !email.value;
        } else {
          return Object.values(errors.value).some((error) => error !== "") || !username.value || !email.value || !newPassword.value || !confirmPassword.value;
        }
      });
      const handleVerify = async () => {
        validateUsername();
        validateEmail();
        if (hasErrors.value) {
          return;
        }
        isLoading.value = true;
        try {
          const result = await store2.dispatch("verifyUserEmail", {
            username: username.value,
            email: email.value
          });
          if (result.success) {
            step.value = 2;
            uni.showToast({
              title: "身份验证成功",
              icon: "success"
            });
          }
        } catch (error) {
          formatAppLog("error", "at pages/forgot-password/forgot-password.vue:185", "身份验证失败:", error);
          let errorMessage = "身份验证失败";
          if (error.message.includes("用户不存在")) {
            errorMessage = "用户名不存在";
            errors.value.username = errorMessage;
          } else if (error.message.includes("邮箱不匹配")) {
            errorMessage = "邮箱与用户名不匹配";
            errors.value.email = errorMessage;
          }
          uni.showToast({
            title: errorMessage,
            icon: "none",
            duration: 2e3
          });
        } finally {
          isLoading.value = false;
        }
      };
      const handleResetPassword = async () => {
        validateUsername();
        validateEmail();
        validateNewPassword();
        validateConfirmPassword();
        if (hasErrors.value) {
          return;
        }
        isLoading.value = true;
        try {
          await store2.dispatch("resetPassword", {
            username: username.value,
            email: email.value,
            newPassword: newPassword.value
          });
          uni.showToast({
            title: "密码重置成功",
            icon: "success"
          });
          setTimeout(() => {
            uni.reLaunch({
              url: "/pages/login/login"
            });
          }, 1500);
        } catch (error) {
          formatAppLog("error", "at pages/forgot-password/forgot-password.vue:236", "密码重置失败:", error);
          uni.showToast({
            title: error.message || "密码重置失败，请重试",
            icon: "none",
            duration: 2e3
          });
        } finally {
          isLoading.value = false;
        }
      };
      const goToLogin = () => {
        uni.navigateTo({
          url: "/pages/login/login"
        });
      };
      return {
        username,
        email,
        newPassword,
        confirmPassword,
        isLoading,
        step,
        errors,
        hasErrors,
        handleVerify,
        handleResetPassword,
        goToLogin,
        validateUsername,
        validateEmail,
        validateNewPassword,
        validateConfirmPassword
      };
    }
  };
  function _sfc_render$6(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", { class: "header-content" }, [
          vue.createElementVNode("image", {
            src: _imports_0$1,
            mode: "aspectFit",
            class: "logo"
          }),
          vue.createElementVNode("text", { class: "title" }, "重置密码")
        ])
      ]),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createElementVNode("view", { class: "form-content" }, [
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "label" }, "用户名"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                type: "text",
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.username = $event),
                placeholder: "请输入用户名",
                class: "input-field",
                onBlur: _cache[1] || (_cache[1] = (...args) => $setup.validateUsername && $setup.validateUsername(...args))
              },
              null,
              544
              /* NEED_HYDRATION, NEED_PATCH */
            ), [
              [vue.vModelText, $setup.username]
            ]),
            $setup.errors.username ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "error-text"
              },
              vue.toDisplayString($setup.errors.username),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ]),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "label" }, "邮箱"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                type: "text",
                "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $setup.email = $event),
                placeholder: "请输入注册时使用的邮箱",
                class: "input-field",
                onBlur: _cache[3] || (_cache[3] = (...args) => $setup.validateEmail && $setup.validateEmail(...args))
              },
              null,
              544
              /* NEED_HYDRATION, NEED_PATCH */
            ), [
              [vue.vModelText, $setup.email]
            ]),
            $setup.errors.email ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "error-text"
              },
              vue.toDisplayString($setup.errors.email),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ]),
          $setup.step === 1 ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "form-item"
          }, [
            vue.createElementVNode("button", {
              class: "submit-button",
              onClick: _cache[4] || (_cache[4] = (...args) => $setup.handleVerify && $setup.handleVerify(...args)),
              disabled: $setup.isLoading || $setup.hasErrors
            }, vue.toDisplayString($setup.isLoading ? "验证中..." : "验证身份"), 9, ["disabled"])
          ])) : vue.createCommentVNode("v-if", true),
          $setup.step === 2 ? (vue.openBlock(), vue.createElementBlock("view", { key: 1 }, [
            vue.createElementVNode("view", { class: "form-item" }, [
              vue.createElementVNode("text", { class: "label" }, "新密码"),
              vue.withDirectives(vue.createElementVNode(
                "input",
                {
                  type: "password",
                  "onUpdate:modelValue": _cache[5] || (_cache[5] = ($event) => $setup.newPassword = $event),
                  placeholder: "请输入新密码",
                  class: "input-field",
                  onBlur: _cache[6] || (_cache[6] = (...args) => $setup.validateNewPassword && $setup.validateNewPassword(...args))
                },
                null,
                544
                /* NEED_HYDRATION, NEED_PATCH */
              ), [
                [vue.vModelText, $setup.newPassword]
              ]),
              $setup.errors.newPassword ? (vue.openBlock(), vue.createElementBlock(
                "text",
                {
                  key: 0,
                  class: "error-text"
                },
                vue.toDisplayString($setup.errors.newPassword),
                1
                /* TEXT */
              )) : vue.createCommentVNode("v-if", true)
            ]),
            vue.createElementVNode("view", { class: "form-item" }, [
              vue.createElementVNode("text", { class: "label" }, "确认密码"),
              vue.withDirectives(vue.createElementVNode(
                "input",
                {
                  type: "password",
                  "onUpdate:modelValue": _cache[7] || (_cache[7] = ($event) => $setup.confirmPassword = $event),
                  placeholder: "请再次输入新密码",
                  class: "input-field",
                  onBlur: _cache[8] || (_cache[8] = (...args) => $setup.validateConfirmPassword && $setup.validateConfirmPassword(...args))
                },
                null,
                544
                /* NEED_HYDRATION, NEED_PATCH */
              ), [
                [vue.vModelText, $setup.confirmPassword]
              ]),
              $setup.errors.confirmPassword ? (vue.openBlock(), vue.createElementBlock(
                "text",
                {
                  key: 0,
                  class: "error-text"
                },
                vue.toDisplayString($setup.errors.confirmPassword),
                1
                /* TEXT */
              )) : vue.createCommentVNode("v-if", true)
            ]),
            vue.createElementVNode("button", {
              class: "submit-button",
              onClick: _cache[9] || (_cache[9] = (...args) => $setup.handleResetPassword && $setup.handleResetPassword(...args)),
              disabled: $setup.isLoading || $setup.hasErrors
            }, vue.toDisplayString($setup.isLoading ? "重置中..." : "重置密码"), 9, ["disabled"])
          ])) : vue.createCommentVNode("v-if", true),
          vue.createElementVNode("view", {
            class: "back-link",
            onClick: _cache[10] || (_cache[10] = (...args) => $setup.goToLogin && $setup.goToLogin(...args))
          }, " 返回登录 ")
        ])
      ])
    ]);
  }
  const PagesForgotPasswordForgotPassword = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["render", _sfc_render$6], ["__file", "G:/myaccount/pages/forgot-password/forgot-password.vue"]]);
  const _sfc_main$6 = {
    setup() {
      const store2 = useStore();
      const startDate = vue.ref("");
      const endDate = vue.ref("");
      const selectedCategory = vue.ref(null);
      const targetFilter = vue.ref("");
      const expenses = vue.ref([]);
      const categoryOptions = vue.ref([]);
      const isLoading = vue.ref(false);
      const loadCategories = () => {
        const allCategories = store2.state.categories;
        const options = [];
        const extractLevel3Categories = (categories) => {
          categories.forEach((category) => {
            if (category.children) {
              category.children.forEach((level2) => {
                if (level2.children) {
                  level2.children.forEach((level3) => {
                    options.push({
                      id: level3.category_id,
                      name: `${category.name} / ${level2.name} / ${level3.name}`
                    });
                  });
                }
              });
            }
          });
        };
        extractLevel3Categories(allCategories);
        categoryOptions.value = [{ id: "", name: "全部" }, ...options];
      };
      const loadExpenses = async () => {
        var _a;
        try {
          let conditions = ["e.user_id = ?"];
          let params = [store2.state.user.user_id];
          if (startDate.value) {
            conditions.push("e.record_date >= ?");
            params.push(startDate.value);
          }
          if (endDate.value) {
            conditions.push("e.record_date <= ?");
            params.push(endDate.value);
          }
          if ((_a = selectedCategory.value) == null ? void 0 : _a.id) {
            conditions.push("e.category_id = ?");
            params.push(selectedCategory.value.id);
          }
          if (targetFilter.value) {
            conditions.push("e.target LIKE ?");
            params.push(`%${targetFilter.value}%`);
          }
          const sql = `
          SELECT
            e.*,
            c1.name as level1_name,
            c2.name as level2_name,
            c3.name as level3_name
          FROM expenses e
          LEFT JOIN categories c3 ON e.category_id = c3.category_id
          LEFT JOIN categories c2 ON c3.parent_id = c2.category_id
          LEFT JOIN categories c1 ON c2.parent_id = c1.category_id
          WHERE ${conditions.join(" AND ")}
          ORDER BY e.record_date DESC, e.created_at DESC
          LIMIT 40
        `;
          const result = await dbService.selectSql(sql, params);
          expenses.value = result.map((item) => ({
            ...item,
            created_at: item.record_date,
            // 使用record_date作为显示日期
            amount: parseFloat(item.amount),
            // 只显示三级分类名称
            category_name: item.level3_name || "未知分类"
          }));
        } catch (error) {
          formatAppLog("error", "at pages/details/details.vue:165", "获取数据失败:", error);
          uni.showToast({
            title: "获取数据失败",
            icon: "none"
          });
        }
      };
      const formatDate = (dateString) => {
        const date = new Date(dateString);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
      };
      const onStartDateChange = (e) => {
        startDate.value = e.detail.value;
      };
      const onEndDateChange = (e) => {
        endDate.value = e.detail.value;
      };
      const onCategoryChange = (e) => {
        selectedCategory.value = categoryOptions.value[e.detail.value];
      };
      const applyFilters = () => {
        loadExpenses();
      };
      vue.onMounted(async () => {
        if (!store2.state.user) {
          uni.reLaunch({
            url: "/pages/login/login"
          });
          return;
        }
        await Promise.all([
          store2.dispatch("fetchCategories"),
          loadCategories(),
          loadExpenses()
        ]);
      });
      return {
        startDate,
        endDate,
        selectedCategory,
        targetFilter,
        expenses,
        categoryOptions,
        isLoading,
        formatDate,
        onStartDateChange,
        onEndDateChange,
        onCategoryChange,
        applyFilters
      };
    }
  };
  function _sfc_render$5(_ctx, _cache, $props, $setup, $data, $options) {
    var _a;
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", { class: "header-content" }, [
          vue.createElementVNode("image", {
            src: _imports_0$1,
            mode: "aspectFit",
            class: "logo"
          }),
          vue.createElementVNode("text", { class: "title" }, "支出明细")
        ])
      ]),
      vue.createCommentVNode(" 筛选区域 "),
      vue.createElementVNode("view", { class: "filter-section" }, [
        vue.createElementVNode("view", { class: "filter-item" }, [
          vue.createElementVNode("text", { class: "filter-label" }, "日期区间"),
          vue.createElementVNode("view", { class: "date-range" }, [
            vue.createElementVNode("picker", {
              mode: "date",
              value: $setup.startDate,
              onChange: _cache[0] || (_cache[0] = (...args) => $setup.onStartDateChange && $setup.onStartDateChange(...args))
            }, [
              vue.createElementVNode(
                "view",
                { class: "picker-item" },
                vue.toDisplayString($setup.startDate || "开始日期"),
                1
                /* TEXT */
              )
            ], 40, ["value"]),
            vue.createElementVNode("text", { class: "date-separator" }, "至"),
            vue.createElementVNode("picker", {
              mode: "date",
              value: $setup.endDate,
              onChange: _cache[1] || (_cache[1] = (...args) => $setup.onEndDateChange && $setup.onEndDateChange(...args))
            }, [
              vue.createElementVNode(
                "view",
                { class: "picker-item" },
                vue.toDisplayString($setup.endDate || "结束日期"),
                1
                /* TEXT */
              )
            ], 40, ["value"])
          ])
        ]),
        vue.createElementVNode("view", { class: "filter-item" }, [
          vue.createElementVNode("text", { class: "filter-label" }, "三级分类"),
          vue.createElementVNode("picker", {
            range: $setup.categoryOptions,
            "range-key": "name",
            onChange: _cache[2] || (_cache[2] = (...args) => $setup.onCategoryChange && $setup.onCategoryChange(...args))
          }, [
            vue.createElementVNode(
              "view",
              { class: "picker-item" },
              vue.toDisplayString(((_a = $setup.selectedCategory) == null ? void 0 : _a.name) || "全部"),
              1
              /* TEXT */
            )
          ], 40, ["range"])
        ]),
        vue.createElementVNode("view", { class: "filter-item" }, [
          vue.createElementVNode("text", { class: "filter-label" }, "标的"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              type: "text",
              "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $setup.targetFilter = $event),
              placeholder: "请输入标的",
              class: "filter-input"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $setup.targetFilter]
          ])
        ]),
        vue.createElementVNode("button", {
          class: "filter-button",
          onClick: _cache[4] || (_cache[4] = (...args) => $setup.applyFilters && $setup.applyFilters(...args))
        }, "筛选")
      ]),
      vue.createCommentVNode(" 数据表格 "),
      vue.createElementVNode("scroll-view", {
        "scroll-y": "",
        class: "table-container"
      }, [
        vue.createElementVNode("view", { class: "table" }, [
          vue.createElementVNode("view", { class: "table-header" }, [
            vue.createElementVNode("view", { class: "table-cell date-cell" }, "时间"),
            vue.createElementVNode("view", { class: "table-cell category-cell" }, "分类"),
            vue.createElementVNode("view", { class: "table-cell target-cell" }, "标的"),
            vue.createElementVNode("view", { class: "table-cell amount-cell" }, "支出")
          ]),
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($setup.expenses, (item, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                key: index,
                class: "table-row"
              }, [
                vue.createElementVNode(
                  "view",
                  { class: "table-cell date-cell" },
                  vue.toDisplayString($setup.formatDate(item.created_at)),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "view",
                  { class: "table-cell category-cell" },
                  vue.toDisplayString(item.category_name),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "view",
                  { class: "table-cell target-cell" },
                  vue.toDisplayString(item.target),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "view",
                  { class: "table-cell amount-cell" },
                  vue.toDisplayString(item.amount.toFixed(2)),
                  1
                  /* TEXT */
                )
              ]);
            }),
            128
            /* KEYED_FRAGMENT */
          )),
          $setup.expenses.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "empty-state"
          }, " 暂无数据 ")) : vue.createCommentVNode("v-if", true)
        ])
      ])
    ]);
  }
  const PagesDetailsDetails = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["render", _sfc_render$5], ["__file", "G:/myaccount/pages/details/details.vue"]]);
  const _sfc_main$5 = {
    setup() {
      const store2 = useStore();
      const isLoading = vue.ref(false);
      const goToChangePassword = () => {
        uni.navigateTo({
          url: "/pages/change-password/change-password"
        });
      };
      const handleBackup = async () => {
        try {
          isLoading.value = true;
          const backupFileName = `myaccount_backup_${(/* @__PURE__ */ new Date()).toISOString().slice(0, 10)}.db`;
          formatAppLog("log", "at pages/settings/settings.vue:68", "准备备份数据，文件名:", backupFileName);
          try {
            formatAppLog("log", "at pages/settings/settings.vue:71", "开始请求存储权限");
            await new Promise((resolve, reject) => {
              plus.android.requestPermissions(
                ["android.permission.WRITE_EXTERNAL_STORAGE"],
                function(resultObj) {
                  formatAppLog("log", "at pages/settings/settings.vue:78", "权限请求结果:", resultObj);
                  if (resultObj.granted.length === 1) {
                    resolve();
                  } else {
                    reject(new Error("未授予存储权限"));
                  }
                },
                function(error) {
                  formatAppLog("error", "at pages/settings/settings.vue:86", "权限请求失败:", error);
                  reject(error);
                }
              );
            });
            formatAppLog("log", "at pages/settings/settings.vue:92", "已获得存储权限，准备导出数据");
            const Environment = plus.android.importClass("android.os.Environment");
            const File = plus.android.importClass("java.io.File");
            const main = plus.android.runtimeMainActivity();
            const externalStorageDir = Environment.getExternalStorageDirectory();
            const filePath = externalStorageDir.getAbsolutePath() + "/Download/" + backupFileName;
            formatAppLog("log", "at pages/settings/settings.vue:104", "使用外部存储路径:", filePath);
            const fileDir = new File(filePath).getParentFile();
            if (!fileDir.exists()) {
              fileDir.mkdirs();
            }
            formatAppLog("log", "at pages/settings/settings.vue:113", "使用直接复制方法备份数据库到:", filePath);
            const bytesCopied = await dbService.backupDatabaseByFileCopy(filePath);
            if (bytesCopied > 0) {
              await new Promise((resolve) => {
                uni.showModal({
                  title: "备份成功",
                  content: `数据已备份到以下路径：
${filePath}`,
                  showCancel: false,
                  success: () => {
                    resolve();
                  }
                });
              });
              uni.showToast({
                title: "备份成功",
                icon: "success",
                duration: 2e3
              });
            } else {
              throw new Error("文件复制失败，复制了 0 字节");
            }
          } catch (error) {
            formatAppLog("error", "at pages/settings/settings.vue:139", "备份过程出错:", error);
            uni.showToast({
              title: "备份失败: " + error.message,
              icon: "none",
              duration: 2e3
            });
          } finally {
            isLoading.value = false;
          }
        } catch (error) {
          formatAppLog("error", "at pages/settings/settings.vue:149", "备份操作失败:", error);
          uni.showToast({
            title: "备份失败，请重试",
            icon: "none",
            duration: 2e3
          });
          isLoading.value = false;
        }
      };
      const goToContact = () => {
        uni.navigateTo({
          url: "/pages/contact/contact"
        });
      };
      const goToApiSettings = () => {
        uni.navigateTo({
          url: "/pages/api-settings/api-settings"
        });
      };
      const handleLogout = async () => {
        try {
          isLoading.value = true;
          await store2.dispatch("logout");
          uni.reLaunch({
            url: "/pages/login/login"
          });
        } catch (error) {
          uni.showToast({
            title: "退出失败，请重试",
            icon: "none"
          });
        } finally {
          isLoading.value = false;
        }
      };
      const goToCategoryManagement = () => {
        uni.navigateTo({
          url: "/pages/category-management/category-management"
        });
      };
      const handleClearData = async () => {
        uni.showModal({
          title: "警告",
          content: "此操作将清空所有支出记录，但保留用户账户和分类信息。\n\n清空前将自动备份数据到下载文件夹。\n\n此操作不可恢复，确定要继续吗？",
          confirmText: "确定清空",
          confirmColor: "#f56c6c",
          cancelText: "取消",
          success: async (res) => {
            if (res.confirm) {
              try {
                isLoading.value = true;
                uni.showModal({
                  title: "再次确认",
                  content: "您真的要清空所有支出记录吗？此操作不可恢复！",
                  confirmText: "确定清空",
                  confirmColor: "#f56c6c",
                  cancelText: "取消",
                  success: async (res2) => {
                    if (res2.confirm) {
                      try {
                        const result = await store2.dispatch("clearUserData");
                        if (result && result.success) {
                          uni.showModal({
                            title: "清空成功",
                            content: `所有支出记录已清空。

数据备份已保存到下载文件夹：${result.backupFile}`,
                            showCancel: false,
                            success: () => {
                              uni.switchTab({
                                url: "/pages/index/index"
                              });
                            }
                          });
                        } else {
                          throw new Error("清空操作失败");
                        }
                      } catch (error) {
                        formatAppLog("error", "at pages/settings/settings.vue:241", "清空数据失败:", error);
                        uni.showToast({
                          title: error.message || "清空失败，请重试",
                          icon: "none",
                          duration: 2e3
                        });
                      } finally {
                        isLoading.value = false;
                      }
                    } else {
                      isLoading.value = false;
                    }
                  }
                });
              } catch (error) {
                formatAppLog("error", "at pages/settings/settings.vue:256", "清空数据失败:", error);
                uni.showToast({
                  title: "操作失败，请重试",
                  icon: "none",
                  duration: 2e3
                });
                isLoading.value = false;
              }
            }
          }
        });
      };
      return {
        goToChangePassword,
        handleBackup,
        goToContact,
        goToApiSettings,
        handleLogout,
        goToCategoryManagement,
        handleClearData
      };
    }
  };
  function _sfc_render$4(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", { class: "header-content" }, [
          vue.createElementVNode("text", { class: "title" }, "设置")
        ])
      ]),
      vue.createElementVNode("view", { class: "settings-list" }, [
        vue.createElementVNode("view", {
          class: "settings-item",
          onClick: _cache[0] || (_cache[0] = (...args) => $setup.goToCategoryManagement && $setup.goToCategoryManagement(...args))
        }, [
          vue.createElementVNode("text", { class: "item-label" }, "分类管理"),
          vue.createElementVNode("text", { class: "item-arrow" }, ">")
        ]),
        vue.createElementVNode("view", {
          class: "settings-item",
          onClick: _cache[1] || (_cache[1] = (...args) => $setup.goToChangePassword && $setup.goToChangePassword(...args))
        }, [
          vue.createElementVNode("text", { class: "item-label" }, "修改密码"),
          vue.createElementVNode("text", { class: "item-arrow" }, ">")
        ]),
        vue.createElementVNode("view", {
          class: "settings-item",
          onClick: _cache[2] || (_cache[2] = (...args) => $setup.handleBackup && $setup.handleBackup(...args))
        }, [
          vue.createElementVNode("text", { class: "item-label" }, "备份数据库"),
          vue.createElementVNode("text", { class: "item-arrow" }, ">")
        ]),
        vue.createElementVNode("view", {
          class: "settings-item danger",
          onClick: _cache[3] || (_cache[3] = (...args) => $setup.handleClearData && $setup.handleClearData(...args))
        }, [
          vue.createElementVNode("text", { class: "item-label danger-text" }, "清空数据"),
          vue.createElementVNode("text", { class: "item-arrow" }, ">")
        ]),
        vue.createElementVNode("view", {
          class: "settings-item",
          onClick: _cache[4] || (_cache[4] = (...args) => $setup.goToApiSettings && $setup.goToApiSettings(...args))
        }, [
          vue.createElementVNode("text", { class: "item-label" }, "DeepSeek API 设置"),
          vue.createElementVNode("text", { class: "item-arrow" }, ">")
        ]),
        vue.createElementVNode("view", {
          class: "settings-item",
          onClick: _cache[5] || (_cache[5] = (...args) => $setup.goToContact && $setup.goToContact(...args))
        }, [
          vue.createElementVNode("text", { class: "item-label" }, "联系我们"),
          vue.createElementVNode("text", { class: "item-arrow" }, ">")
        ]),
        vue.createElementVNode("view", {
          class: "settings-item",
          onClick: _cache[6] || (_cache[6] = (...args) => $setup.handleLogout && $setup.handleLogout(...args))
        }, [
          vue.createElementVNode("text", { class: "item-label" }, "退出账户"),
          vue.createElementVNode("text", { class: "item-arrow" }, ">")
        ])
      ])
    ]);
  }
  const PagesSettingsSettings = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["render", _sfc_render$4], ["__file", "G:/myaccount/pages/settings/settings.vue"]]);
  const _sfc_main$4 = {
    setup() {
      const store2 = useStore();
      const oldPassword = vue.ref("");
      const newPassword = vue.ref("");
      const confirmPassword = vue.ref("");
      const isLoading = vue.ref(false);
      const handleSubmit = async () => {
        if (!oldPassword.value || !newPassword.value || !confirmPassword.value) {
          uni.showToast({
            title: "请填写完整信息",
            icon: "none"
          });
          return;
        }
        if (newPassword.value !== confirmPassword.value) {
          uni.showToast({
            title: "两次输入的新密码不一致",
            icon: "none"
          });
          return;
        }
        if (newPassword.value.length < 6) {
          uni.showToast({
            title: "新密码长度不能少于6位",
            icon: "none"
          });
          return;
        }
        isLoading.value = true;
        try {
          const result = await store2.dispatch("changePassword", {
            oldPassword: oldPassword.value,
            newPassword: newPassword.value
          });
          if (result) {
            uni.showToast({
              title: "密码修改成功",
              icon: "success"
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        } catch (error) {
          uni.showToast({
            title: error.message || "密码修改失败",
            icon: "none"
          });
        } finally {
          isLoading.value = false;
        }
      };
      return {
        oldPassword,
        newPassword,
        confirmPassword,
        isLoading,
        handleSubmit
      };
    }
  };
  function _sfc_render$3(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", { class: "header-content" }, [
          vue.createElementVNode("text", { class: "title" }, "修改密码")
        ])
      ]),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createElementVNode("view", { class: "input-group" }, [
          vue.createElementVNode("text", { class: "input-label" }, "旧密码"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              type: "password",
              "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.oldPassword = $event),
              class: "input-field",
              placeholder: "请输入旧密码"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $setup.oldPassword]
          ])
        ]),
        vue.createElementVNode("view", { class: "input-group" }, [
          vue.createElementVNode("text", { class: "input-label" }, "新密码"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              type: "password",
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $setup.newPassword = $event),
              class: "input-field",
              placeholder: "请输入新密码"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $setup.newPassword]
          ])
        ]),
        vue.createElementVNode("view", { class: "input-group" }, [
          vue.createElementVNode("text", { class: "input-label" }, "确认密码"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              type: "password",
              "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $setup.confirmPassword = $event),
              class: "input-field",
              placeholder: "请再次输入新密码"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $setup.confirmPassword]
          ])
        ]),
        vue.createElementVNode("button", {
          class: "submit-button",
          onClick: _cache[3] || (_cache[3] = (...args) => $setup.handleSubmit && $setup.handleSubmit(...args)),
          disabled: $setup.isLoading
        }, vue.toDisplayString($setup.isLoading ? "提交中..." : "确认修改"), 9, ["disabled"])
      ])
    ]);
  }
  const PagesChangePasswordChangePassword = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["render", _sfc_render$3], ["__file", "G:/myaccount/pages/change-password/change-password.vue"]]);
  const _imports_0 = "/static/weixin.png";
  const _sfc_main$3 = {
    setup() {
      const copyEmail = () => {
        uni.setClipboardData({
          data: "<EMAIL>",
          success: () => {
            uni.showToast({
              title: "邮箱已复制",
              icon: "success"
            });
          }
        });
      };
      const previewImage = () => {
        uni.previewImage({
          urls: ["/static/weixin.png"]
        });
      };
      return {
        copyEmail,
        previewImage
      };
    }
  };
  function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", { class: "header-content" }, [
          vue.createElementVNode("text", { class: "title" }, "联系我们")
        ])
      ]),
      vue.createElementVNode("view", { class: "contact-list" }, [
        vue.createElementVNode("view", { class: "contact-item" }, [
          vue.createElementVNode("text", { class: "contact-label" }, "QQ邮箱"),
          vue.createElementVNode("text", {
            class: "contact-value",
            onClick: _cache[0] || (_cache[0] = (...args) => $setup.copyEmail && $setup.copyEmail(...args))
          }, "<EMAIL>")
        ]),
        vue.createElementVNode("view", { class: "contact-item" }, [
          vue.createElementVNode("text", { class: "contact-label" }, "微信"),
          vue.createElementVNode("image", {
            src: _imports_0,
            mode: "aspectFit",
            class: "qr-code",
            onClick: _cache[1] || (_cache[1] = (...args) => $setup.previewImage && $setup.previewImage(...args))
          })
        ]),
        vue.createElementVNode("view", { class: "contact-item" }, [
          vue.createElementVNode("text", { class: "contact-label" }, "使用说明"),
          vue.createElementVNode("view", { class: "user-guide" }, [
            vue.createElementVNode("view", { class: "guide-section" }, [
              vue.createElementVNode("text", { class: "guide-title" }, "应用简介"),
              vue.createElementVNode("text", { class: "guide-content" }, "本应用是一个完全本地化的记账应用，所有数据存储在您的手机上，不会上传到云端，保证您的数据安全和隐私。")
            ]),
            vue.createElementVNode("view", { class: "guide-section" }, [
              vue.createElementVNode("text", { class: "guide-title" }, "主要功能"),
              vue.createElementVNode("view", { class: "guide-list" }, [
                vue.createElementVNode("text", { class: "guide-item" }, "1. 支出记录：在首页输入支出金额和标的，可输入负数对冲错误记录"),
                vue.createElementVNode("text", { class: "guide-item" }, "2. 智能输入：点击“智能输入”按钮，输入自然语言描述的消费记录"),
                vue.createElementVNode("text", { class: "guide-item" }, "3. 明细查询：在明细页面查看所有支出记录，可按日期和分类筛选"),
                vue.createElementVNode("text", { class: "guide-item" }, "4. 分类管理：在设置页面中管理自定义分类"),
                vue.createElementVNode("text", { class: "guide-item" }, "5. 数据备份：在设置页面中备份数据到手机存储")
              ])
            ]),
            vue.createElementVNode("view", { class: "guide-section" }, [
              vue.createElementVNode("text", { class: "guide-title" }, "注意事项"),
              vue.createElementVNode("view", { class: "guide-list" }, [
                vue.createElementVNode("text", { class: "guide-item" }, "1. 定期备份数据，防止意外丢失"),
                vue.createElementVNode("text", { class: "guide-item" }, "2. 清空数据前会自动备份，但仍请谨慎操作"),
                vue.createElementVNode("text", { class: "guide-item" }, "3. 如需导入备份数据，请联系开发者"),
                vue.createElementVNode("text", { class: "guide-item" }, "4. 如忘记密码，可在登录页面点击“忘记密码”，通过注册邮箱重置"),
                vue.createElementVNode("text", { class: "guide-item" }, "5. 如发现问题或有功能建议，请通过上方联系方式反馈")
              ])
            ]),
            vue.createElementVNode("view", { class: "guide-section" }, [
              vue.createElementVNode("text", { class: "guide-title" }, "隐私声明"),
              vue.createElementVNode("text", { class: "guide-content" }, "本应用不会收集或上传您的个人数据，所有数据仅存储在您的设备上。智能输入功能会将您输入的文本发送到AI服务器进行处理，但不会存储您的数据。")
            ])
          ])
        ])
      ])
    ]);
  }
  const PagesContactContact = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render$2], ["__file", "G:/myaccount/pages/contact/contact.vue"]]);
  const _sfc_main$2 = {
    setup() {
      const store2 = useStore();
      const editForm = vue.ref({ categoryId: "", name: "" });
      const addForm = vue.ref({ parentId: null, level: 1, name: "" });
      const isLoading = vue.ref(false);
      const showEditModal = vue.ref(false);
      const showAddModal = vue.ref(false);
      const categories = vue.computed(() => store2.state.categories);
      const showEditDialog = (category) => {
        editForm.value = {
          categoryId: category.category_id,
          name: category.name
        };
        showEditModal.value = true;
      };
      const closeEditDialog = () => {
        showEditModal.value = false;
      };
      const handleEditConfirm = async () => {
        try {
          if (!editForm.value.name.trim()) {
            uni.showToast({
              title: "分类名称不能为空",
              icon: "none"
            });
            return;
          }
          await store2.dispatch("updateCategory", {
            categoryId: editForm.value.categoryId,
            name: editForm.value.name.trim()
          });
          closeEditDialog();
          await store2.dispatch("fetchCategories");
          uni.showToast({
            title: "修改成功",
            icon: "success"
          });
        } catch (error) {
          uni.showToast({
            title: error.message || "修改失败",
            icon: "none"
          });
        }
      };
      const showAddDialog = (parentId, level) => {
        addForm.value = {
          parentId,
          level,
          name: ""
        };
        showAddModal.value = true;
      };
      const closeAddDialog = () => {
        showAddModal.value = false;
      };
      const handleAddConfirm = async () => {
        try {
          if (!addForm.value.name.trim()) {
            uni.showToast({
              title: "分类名称不能为空",
              icon: "none"
            });
            return;
          }
          let sortOrder = 1;
          if (addForm.value.parentId) {
            const parent = findCategoryById(addForm.value.parentId);
            if (parent && parent.children) {
              const maxSortOrder = Math.max(...parent.children.map((c) => c.sort_order || 0), 0);
              sortOrder = maxSortOrder + 1;
            }
          } else {
            const maxSortOrder = Math.max(...categories.value.map((c) => c.sort_order || 0), 0);
            sortOrder = maxSortOrder + 1;
          }
          await store2.dispatch("addCategory", {
            name: addForm.value.name.trim(),
            parentId: addForm.value.parentId,
            level: addForm.value.level,
            sortOrder
          });
          closeAddDialog();
          await store2.dispatch("fetchCategories");
          uni.showToast({
            title: "添加成功",
            icon: "success"
          });
        } catch (error) {
          uni.showToast({
            title: error.message || "添加失败",
            icon: "none"
          });
        }
      };
      const confirmDelete = (categoryId) => {
        uni.showModal({
          title: "确认删除",
          content: "删除分类后无法恢复，确定要删除吗？",
          success: async (res) => {
            if (res.confirm) {
              try {
                await store2.dispatch("deleteCategory", categoryId);
                await store2.dispatch("fetchCategories");
                uni.showToast({
                  title: "删除成功",
                  icon: "success"
                });
              } catch (error) {
                uni.showToast({
                  title: error.message || "删除失败",
                  icon: "none"
                });
              }
            }
          }
        });
      };
      const findCategoryById = (categoryId) => {
        const searchInCategories = (categories2) => {
          for (const category of categories2) {
            if (category.category_id === categoryId)
              return category;
            if (category.children && category.children.length > 0) {
              const found = searchInCategories(category.children);
              if (found)
                return found;
            }
          }
          return null;
        };
        return searchInCategories(categories.value);
      };
      vue.onMounted(async () => {
        if (!store2.state.user) {
          uni.reLaunch({
            url: "/pages/login/login"
          });
          return;
        }
        await store2.dispatch("fetchCategories");
      });
      return {
        categories,
        editForm,
        addForm,
        isLoading,
        showEditModal,
        showAddModal,
        showEditDialog,
        closeEditDialog,
        handleEditConfirm,
        showAddDialog,
        closeAddDialog,
        handleAddConfirm,
        confirmDelete
      };
    }
  };
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", { class: "header-content" }, [
          vue.createElementVNode("text", { class: "title" }, "分类管理")
        ])
      ]),
      vue.createCommentVNode(" 加载状态指示器 "),
      $setup.isLoading ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "loading-container"
      }, [
        vue.createElementVNode("view", { class: "loading-spinner" }),
        vue.createElementVNode("text", { class: "loading-text" }, "加载中...")
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 分类列表 "),
      vue.createElementVNode("view", { class: "category-list" }, [
        vue.createCommentVNode(" 一级分类 "),
        (vue.openBlock(true), vue.createElementBlock(
          vue.Fragment,
          null,
          vue.renderList($setup.categories, (category, index) => {
            return vue.openBlock(), vue.createElementBlock("view", {
              key: category.category_id,
              class: "category-item level-1"
            }, [
              vue.createElementVNode("view", { class: "category-info" }, [
                vue.createElementVNode(
                  "text",
                  { class: "category-name" },
                  vue.toDisplayString(category.name),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode("view", { class: "category-actions" }, [
                  vue.createElementVNode("button", {
                    class: "action-btn edit-btn",
                    onClick: ($event) => $setup.showEditDialog(category)
                  }, "编辑", 8, ["onClick"]),
                  vue.createElementVNode("button", {
                    class: "action-btn add-btn",
                    onClick: ($event) => $setup.showAddDialog(category.category_id, 2)
                  }, "添加子类", 8, ["onClick"]),
                  vue.createElementVNode("button", {
                    class: "action-btn delete-btn",
                    onClick: ($event) => $setup.confirmDelete(category.category_id)
                  }, "删除", 8, ["onClick"])
                ])
              ]),
              vue.createCommentVNode(" 二级分类 "),
              category.children && category.children.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
                key: 0,
                class: "subcategory-list"
              }, [
                (vue.openBlock(true), vue.createElementBlock(
                  vue.Fragment,
                  null,
                  vue.renderList(category.children, (subCategory) => {
                    return vue.openBlock(), vue.createElementBlock("view", {
                      key: subCategory.category_id,
                      class: "category-item level-2"
                    }, [
                      vue.createElementVNode("view", { class: "category-info" }, [
                        vue.createElementVNode(
                          "text",
                          { class: "category-name" },
                          vue.toDisplayString(subCategory.name),
                          1
                          /* TEXT */
                        ),
                        vue.createElementVNode("view", { class: "category-actions" }, [
                          vue.createElementVNode("button", {
                            class: "action-btn edit-btn",
                            onClick: ($event) => $setup.showEditDialog(subCategory)
                          }, "编辑", 8, ["onClick"]),
                          vue.createElementVNode("button", {
                            class: "action-btn add-btn",
                            onClick: ($event) => $setup.showAddDialog(subCategory.category_id, 3)
                          }, "添加子类", 8, ["onClick"]),
                          vue.createElementVNode("button", {
                            class: "action-btn delete-btn",
                            onClick: ($event) => $setup.confirmDelete(subCategory.category_id)
                          }, "删除", 8, ["onClick"])
                        ])
                      ]),
                      vue.createCommentVNode(" 三级分类 "),
                      subCategory.children && subCategory.children.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
                        key: 0,
                        class: "subcategory-list"
                      }, [
                        (vue.openBlock(true), vue.createElementBlock(
                          vue.Fragment,
                          null,
                          vue.renderList(subCategory.children, (item) => {
                            return vue.openBlock(), vue.createElementBlock("view", {
                              key: item.category_id,
                              class: "category-item level-3"
                            }, [
                              vue.createElementVNode("view", { class: "category-info" }, [
                                vue.createElementVNode(
                                  "text",
                                  { class: "category-name" },
                                  vue.toDisplayString(item.name),
                                  1
                                  /* TEXT */
                                ),
                                vue.createElementVNode("view", { class: "category-actions" }, [
                                  vue.createElementVNode("button", {
                                    class: "action-btn edit-btn",
                                    onClick: ($event) => $setup.showEditDialog(item)
                                  }, "编辑", 8, ["onClick"]),
                                  vue.createElementVNode("button", {
                                    class: "action-btn delete-btn",
                                    onClick: ($event) => $setup.confirmDelete(item.category_id)
                                  }, "删除", 8, ["onClick"])
                                ])
                              ])
                            ]);
                          }),
                          128
                          /* KEYED_FRAGMENT */
                        ))
                      ])) : vue.createCommentVNode("v-if", true)
                    ]);
                  }),
                  128
                  /* KEYED_FRAGMENT */
                ))
              ])) : vue.createCommentVNode("v-if", true)
            ]);
          }),
          128
          /* KEYED_FRAGMENT */
        ))
      ]),
      vue.createCommentVNode(" 添加一级分类按钮 "),
      vue.createElementVNode("view", {
        class: "add-category-btn",
        onClick: _cache[0] || (_cache[0] = ($event) => $setup.showAddDialog(null, 1))
      }, [
        vue.createElementVNode("text", null, "添加一级分类")
      ]),
      vue.createCommentVNode(" 编辑分类弹窗 "),
      $setup.showEditModal ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 1,
        class: "modal"
      }, [
        vue.createElementVNode("view", {
          class: "modal-mask",
          onClick: _cache[1] || (_cache[1] = (...args) => $setup.closeEditDialog && $setup.closeEditDialog(...args))
        }),
        vue.createElementVNode("view", { class: "modal-content" }, [
          vue.createElementVNode("view", { class: "modal-header" }, [
            vue.createElementVNode("text", { class: "modal-title" }, "编辑分类"),
            vue.createElementVNode("text", {
              class: "modal-close",
              onClick: _cache[2] || (_cache[2] = (...args) => $setup.closeEditDialog && $setup.closeEditDialog(...args))
            }, "×")
          ]),
          vue.createElementVNode("view", { class: "modal-body" }, [
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "modal-input",
                "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $setup.editForm.name = $event),
                placeholder: "请输入分类名称"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $setup.editForm.name]
            ])
          ]),
          vue.createElementVNode("view", { class: "modal-footer" }, [
            vue.createElementVNode("button", {
              class: "modal-button cancel",
              onClick: _cache[4] || (_cache[4] = (...args) => $setup.closeEditDialog && $setup.closeEditDialog(...args))
            }, "取消"),
            vue.createElementVNode("button", {
              class: "modal-button confirm",
              onClick: _cache[5] || (_cache[5] = (...args) => $setup.handleEditConfirm && $setup.handleEditConfirm(...args))
            }, "确定")
          ])
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 添加分类弹窗 "),
      $setup.showAddModal ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 2,
        class: "modal"
      }, [
        vue.createElementVNode("view", {
          class: "modal-mask",
          onClick: _cache[6] || (_cache[6] = (...args) => $setup.closeAddDialog && $setup.closeAddDialog(...args))
        }),
        vue.createElementVNode("view", { class: "modal-content" }, [
          vue.createElementVNode("view", { class: "modal-header" }, [
            vue.createElementVNode("text", { class: "modal-title" }, "添加分类"),
            vue.createElementVNode("text", {
              class: "modal-close",
              onClick: _cache[7] || (_cache[7] = (...args) => $setup.closeAddDialog && $setup.closeAddDialog(...args))
            }, "×")
          ]),
          vue.createElementVNode("view", { class: "modal-body" }, [
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "modal-input",
                "onUpdate:modelValue": _cache[8] || (_cache[8] = ($event) => $setup.addForm.name = $event),
                placeholder: "请输入分类名称"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $setup.addForm.name]
            ])
          ]),
          vue.createElementVNode("view", { class: "modal-footer" }, [
            vue.createElementVNode("button", {
              class: "modal-button cancel",
              onClick: _cache[9] || (_cache[9] = (...args) => $setup.closeAddDialog && $setup.closeAddDialog(...args))
            }, "取消"),
            vue.createElementVNode("button", {
              class: "modal-button confirm",
              onClick: _cache[10] || (_cache[10] = (...args) => $setup.handleAddConfirm && $setup.handleAddConfirm(...args))
            }, "确定")
          ])
        ])
      ])) : vue.createCommentVNode("v-if", true)
    ]);
  }
  const PagesCategoryManagementCategoryManagement = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$1], ["__file", "G:/myaccount/pages/category-management/category-management.vue"]]);
  const _sfc_main$1 = {
    setup() {
      const store2 = useStore();
      const apiKey = vue.ref("");
      const selectedModel = vue.ref("deepseek-chat");
      const temperature = vue.ref(0.2);
      const isLoading = vue.ref(false);
      const errors = vue.ref({
        apiKey: ""
      });
      const validateApiKey = () => {
        if (!apiKey.value) {
          errors.value.apiKey = "API 密钥不能为空";
        } else if (!apiKey.value.startsWith("sk-")) {
          errors.value.apiKey = "API 密钥格式不正确，应以 sk- 开头";
        } else {
          errors.value.apiKey = "";
        }
      };
      const handleTemperatureChange = (e) => {
        temperature.value = e.detail.value / 10;
      };
      const hasErrors = vue.computed(() => {
        return Object.values(errors.value).some((error) => error !== "");
      });
      const saveSettings = async () => {
        validateApiKey();
        if (hasErrors.value) {
          return;
        }
        isLoading.value = true;
        try {
          await store2.dispatch("saveApiSettings", {
            apiKey: apiKey.value,
            model: selectedModel.value,
            temperature: temperature.value
          });
          uni.showToast({
            title: "设置已保存",
            icon: "success"
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } catch (error) {
          formatAppLog("error", "at pages/api-settings/api-settings.vue:136", "保存设置失败:", error);
          uni.showToast({
            title: "保存失败，请重试",
            icon: "none"
          });
        } finally {
          isLoading.value = false;
        }
      };
      vue.onMounted(() => {
        const settings = store2.state.apiSettings;
        if (settings) {
          apiKey.value = settings.apiKey || "";
          selectedModel.value = settings.model || "deepseek-chat";
          temperature.value = settings.temperature !== void 0 ? settings.temperature : 0.2;
        }
      });
      return {
        apiKey,
        selectedModel,
        temperature,
        isLoading,
        errors,
        hasErrors,
        validateApiKey,
        handleTemperatureChange,
        saveSettings
      };
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", { class: "header-content" }, [
          vue.createElementVNode("text", { class: "title" }, "API 设置")
        ])
      ]),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createElementVNode("view", { class: "form-content" }, [
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "label" }, "DeepSeek API 密钥"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                type: "text",
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.apiKey = $event),
                placeholder: "请输入 DeepSeek API 密钥",
                class: "input-field",
                onBlur: _cache[1] || (_cache[1] = (...args) => $setup.validateApiKey && $setup.validateApiKey(...args))
              },
              null,
              544
              /* NEED_HYDRATION, NEED_PATCH */
            ), [
              [vue.vModelText, $setup.apiKey]
            ]),
            $setup.errors.apiKey ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "error-text"
              },
              vue.toDisplayString($setup.errors.apiKey),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true),
            vue.createElementVNode("text", { class: "hint-text" }, "格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
          ]),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "label" }, "选择模型"),
            vue.createElementVNode("view", { class: "model-selector" }, [
              vue.createElementVNode(
                "view",
                {
                  class: vue.normalizeClass(["model-option", { active: $setup.selectedModel === "deepseek-chat" }]),
                  onClick: _cache[2] || (_cache[2] = ($event) => $setup.selectedModel = "deepseek-chat")
                },
                [
                  vue.createElementVNode("text", { class: "model-name" }, "DeepSeek-V3"),
                  vue.createElementVNode("text", { class: "model-id" }, "deepseek-chat"),
                  vue.createElementVNode("text", { class: "model-desc" }, "通用对话模型，适合日常对话和分析")
                ],
                2
                /* CLASS */
              ),
              vue.createElementVNode(
                "view",
                {
                  class: vue.normalizeClass(["model-option", { active: $setup.selectedModel === "deepseek-reasoner" }]),
                  onClick: _cache[3] || (_cache[3] = ($event) => $setup.selectedModel = "deepseek-reasoner")
                },
                [
                  vue.createElementVNode("text", { class: "model-name" }, "DeepSeek-R1"),
                  vue.createElementVNode("text", { class: "model-id" }, "deepseek-reasoner"),
                  vue.createElementVNode("text", { class: "model-desc" }, "推理模型，适合复杂逻辑和数学计算")
                ],
                2
                /* CLASS */
              )
            ])
          ]),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "label" }, "温度 (Temperature)"),
            vue.createElementVNode("view", { class: "slider-container" }, [
              vue.createElementVNode("slider", {
                value: $setup.temperature * 10,
                min: "0",
                max: "10",
                "show-value": "",
                onChange: _cache[4] || (_cache[4] = (...args) => $setup.handleTemperatureChange && $setup.handleTemperatureChange(...args))
              }, null, 40, ["value"]),
              vue.createElementVNode(
                "text",
                { class: "slider-value" },
                vue.toDisplayString($setup.temperature.toFixed(1)),
                1
                /* TEXT */
              )
            ]),
            vue.createElementVNode("text", { class: "hint-text" }, "较低的值使输出更确定，较高的值使输出更随机")
          ]),
          vue.createElementVNode("button", {
            class: "submit-button",
            onClick: _cache[5] || (_cache[5] = (...args) => $setup.saveSettings && $setup.saveSettings(...args)),
            disabled: $setup.isLoading || $setup.hasErrors
          }, vue.toDisplayString($setup.isLoading ? "保存中..." : "保存设置"), 9, ["disabled"])
        ])
      ])
    ]);
  }
  const PagesApiSettingsApiSettings = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__file", "G:/myaccount/pages/api-settings/api-settings.vue"]]);
  __definePage("pages/index/index", PagesIndexIndex);
  __definePage("pages/login/login", PagesLoginLogin);
  __definePage("pages/register/register", PagesRegisterRegister);
  __definePage("pages/forgot-password/forgot-password", PagesForgotPasswordForgotPassword);
  __definePage("pages/details/details", PagesDetailsDetails);
  __definePage("pages/settings/settings", PagesSettingsSettings);
  __definePage("pages/change-password/change-password", PagesChangePasswordChangePassword);
  __definePage("pages/contact/contact", PagesContactContact);
  __definePage("pages/category-management/category-management", PagesCategoryManagementCategoryManagement);
  __definePage("pages/api-settings/api-settings", PagesApiSettingsApiSettings);
  const _sfc_main = {
    onLaunch: async function() {
      formatAppLog("log", "at App.vue:6", "App Launch");
      formatAppLog("log", "at App.vue:7", "当前应用图标路径:", plus.runtime.appIcon);
      try {
        await dbService.initDatabase();
        formatAppLog("log", "at App.vue:10", "数据库初始化成功");
      } catch (error) {
        formatAppLog("error", "at App.vue:12", "数据库初始化失败:", error);
      }
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:16", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:19", "App Hide");
    },
    onUnload: async function() {
      try {
        await dbService.closeDatabase();
        formatAppLog("log", "at App.vue:24", "数据库关闭成功");
      } catch (error) {
        formatAppLog("error", "at App.vue:26", "数据库关闭失败:", error);
      }
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "G:/myaccount/App.vue"]]);
  const byteToHex = [];
  for (let i = 0; i < 256; ++i) {
    byteToHex.push((i + 256).toString(16).slice(1));
  }
  function unsafeStringify(arr, offset = 0) {
    return (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + "-" + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + "-" + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + "-" + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + "-" + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase();
  }
  var lookup = [
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    62,
    0,
    62,
    0,
    63,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    0,
    0,
    0,
    0,
    63,
    0,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51
  ];
  function base64Decode(source, target) {
    var sourceLength = source.length;
    var paddingLength = source[sourceLength - 2] === "=" ? 2 : source[sourceLength - 1] === "=" ? 1 : 0;
    var tmp;
    var byteIndex = 0;
    var baseLength = sourceLength - paddingLength & 4294967292;
    for (var i = 0; i < baseLength; i += 4) {
      tmp = lookup[source.charCodeAt(i)] << 18 | lookup[source.charCodeAt(i + 1)] << 12 | lookup[source.charCodeAt(i + 2)] << 6 | lookup[source.charCodeAt(i + 3)];
      target[byteIndex++] = tmp >> 16 & 255;
      target[byteIndex++] = tmp >> 8 & 255;
      target[byteIndex++] = tmp & 255;
    }
    if (paddingLength === 1) {
      tmp = lookup[source.charCodeAt(i)] << 10 | lookup[source.charCodeAt(i + 1)] << 4 | lookup[source.charCodeAt(i + 2)] >> 2;
      target[byteIndex++] = tmp >> 8 & 255;
      target[byteIndex++] = tmp & 255;
    }
    if (paddingLength === 2) {
      tmp = lookup[source.charCodeAt(i)] << 2 | lookup[source.charCodeAt(i + 1)] >> 4;
      target[byteIndex++] = tmp & 255;
    }
  }
  const crypto = {
    getRandomValues(arr) {
      if (!(arr instanceof Int8Array || arr instanceof Uint8Array || arr instanceof Int16Array || arr instanceof Uint16Array || arr instanceof Int32Array || arr instanceof Uint32Array || arr instanceof Uint8ClampedArray)) {
        throw new Error("Expected an integer array");
      }
      if (arr.byteLength > 65536) {
        throw new Error("Can only request a maximum of 65536 bytes");
      }
      var crypto2 = requireNativePlugin("DCloud-Crypto");
      base64Decode(crypto2.getRandomValues(arr.byteLength), new Uint8Array(
        arr.buffer,
        arr.byteOffset,
        arr.byteLength
      ));
      return arr;
    }
  };
  let getRandomValues;
  const rnds8 = new Uint8Array(16);
  function rng() {
    if (!getRandomValues) {
      if (typeof crypto === "undefined" || !crypto.getRandomValues) {
        throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
      }
      getRandomValues = crypto.getRandomValues.bind(crypto);
    }
    return getRandomValues(rnds8);
  }
  const randomUUID = typeof crypto !== "undefined" && crypto.randomUUID && crypto.randomUUID.bind(crypto);
  const native = { randomUUID };
  function v4(options, buf, offset) {
    var _a;
    if (native.randomUUID && !buf && !options) {
      return native.randomUUID();
    }
    options = options || {};
    const rnds = options.random ?? ((_a = options.rng) == null ? void 0 : _a.call(options)) ?? rng();
    if (rnds.length < 16) {
      throw new Error("Random bytes length must be >= 16");
    }
    rnds[6] = rnds[6] & 15 | 64;
    rnds[8] = rnds[8] & 63 | 128;
    if (buf) {
      offset = offset || 0;
      if (offset < 0 || offset + 16 > buf.length) {
        throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);
      }
      for (let i = 0; i < 16; ++i) {
        buf[offset + i] = rnds[i];
      }
      return buf;
    }
    return unsafeStringify(rnds);
  }
  const nodeCrypto = new Proxy({}, {
    get(_, key) {
      throw new Error(`Module "" has been externalized for browser compatibility. Cannot access ".${key}" in client code.  See https://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
    }
  });
  var randomFallback = null;
  function randomBytes(len) {
    try {
      return crypto.getRandomValues(new Uint8Array(len));
    } catch {
    }
    try {
      return nodeCrypto.randomBytes(len);
    } catch {
    }
    if (!randomFallback) {
      throw Error(
        "Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative"
      );
    }
    return randomFallback(len);
  }
  function setRandomFallback(random) {
    randomFallback = random;
  }
  function genSaltSync(rounds, seed_length) {
    rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;
    if (typeof rounds !== "number")
      throw Error(
        "Illegal arguments: " + typeof rounds + ", " + typeof seed_length
      );
    if (rounds < 4)
      rounds = 4;
    else if (rounds > 31)
      rounds = 31;
    var salt = [];
    salt.push("$2b$");
    if (rounds < 10)
      salt.push("0");
    salt.push(rounds.toString());
    salt.push("$");
    salt.push(base64_encode(randomBytes(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN));
    return salt.join("");
  }
  function genSalt(rounds, seed_length, callback) {
    if (typeof seed_length === "function")
      callback = seed_length, seed_length = void 0;
    if (typeof rounds === "function")
      callback = rounds, rounds = void 0;
    if (typeof rounds === "undefined")
      rounds = GENSALT_DEFAULT_LOG2_ROUNDS;
    else if (typeof rounds !== "number")
      throw Error("illegal arguments: " + typeof rounds);
    function _async(callback2) {
      nextTick(function() {
        try {
          callback2(null, genSaltSync(rounds));
        } catch (err) {
          callback2(err);
        }
      });
    }
    if (callback) {
      if (typeof callback !== "function")
        throw Error("Illegal callback: " + typeof callback);
      _async(callback);
    } else
      return new Promise(function(resolve, reject) {
        _async(function(err, res) {
          if (err) {
            reject(err);
            return;
          }
          resolve(res);
        });
      });
  }
  function hashSync(password, salt) {
    if (typeof salt === "undefined")
      salt = GENSALT_DEFAULT_LOG2_ROUNDS;
    if (typeof salt === "number")
      salt = genSaltSync(salt);
    if (typeof password !== "string" || typeof salt !== "string")
      throw Error("Illegal arguments: " + typeof password + ", " + typeof salt);
    return _hash(password, salt);
  }
  function hash(password, salt, callback, progressCallback) {
    function _async(callback2) {
      if (typeof password === "string" && typeof salt === "number")
        genSalt(salt, function(err, salt2) {
          _hash(password, salt2, callback2, progressCallback);
        });
      else if (typeof password === "string" && typeof salt === "string")
        _hash(password, salt, callback2, progressCallback);
      else
        nextTick(
          callback2.bind(
            this,
            Error("Illegal arguments: " + typeof password + ", " + typeof salt)
          )
        );
    }
    if (callback) {
      if (typeof callback !== "function")
        throw Error("Illegal callback: " + typeof callback);
      _async(callback);
    } else
      return new Promise(function(resolve, reject) {
        _async(function(err, res) {
          if (err) {
            reject(err);
            return;
          }
          resolve(res);
        });
      });
  }
  function safeStringCompare(known, unknown) {
    var diff = known.length ^ unknown.length;
    for (var i = 0; i < known.length; ++i) {
      diff |= known.charCodeAt(i) ^ unknown.charCodeAt(i);
    }
    return diff === 0;
  }
  function compareSync(password, hash2) {
    if (typeof password !== "string" || typeof hash2 !== "string")
      throw Error("Illegal arguments: " + typeof password + ", " + typeof hash2);
    if (hash2.length !== 60)
      return false;
    return safeStringCompare(
      hashSync(password, hash2.substring(0, hash2.length - 31)),
      hash2
    );
  }
  function compare(password, hashValue, callback, progressCallback) {
    function _async(callback2) {
      if (typeof password !== "string" || typeof hashValue !== "string") {
        nextTick(
          callback2.bind(
            this,
            Error(
              "Illegal arguments: " + typeof password + ", " + typeof hashValue
            )
          )
        );
        return;
      }
      if (hashValue.length !== 60) {
        nextTick(callback2.bind(this, null, false));
        return;
      }
      hash(
        password,
        hashValue.substring(0, 29),
        function(err, comp) {
          if (err)
            callback2(err);
          else
            callback2(null, safeStringCompare(comp, hashValue));
        },
        progressCallback
      );
    }
    if (callback) {
      if (typeof callback !== "function")
        throw Error("Illegal callback: " + typeof callback);
      _async(callback);
    } else
      return new Promise(function(resolve, reject) {
        _async(function(err, res) {
          if (err) {
            reject(err);
            return;
          }
          resolve(res);
        });
      });
  }
  function getRounds(hash2) {
    if (typeof hash2 !== "string")
      throw Error("Illegal arguments: " + typeof hash2);
    return parseInt(hash2.split("$")[2], 10);
  }
  function getSalt(hash2) {
    if (typeof hash2 !== "string")
      throw Error("Illegal arguments: " + typeof hash2);
    if (hash2.length !== 60)
      throw Error("Illegal hash length: " + hash2.length + " != 60");
    return hash2.substring(0, 29);
  }
  function truncates(password) {
    if (typeof password !== "string")
      throw Error("Illegal arguments: " + typeof password);
    return utf8Length(password) > 72;
  }
  var nextTick = typeof process !== "undefined" && process && typeof process.nextTick === "function" ? typeof setImmediate === "function" ? setImmediate : process.nextTick : setTimeout;
  function utf8Length(string) {
    var len = 0, c = 0;
    for (var i = 0; i < string.length; ++i) {
      c = string.charCodeAt(i);
      if (c < 128)
        len += 1;
      else if (c < 2048)
        len += 2;
      else if ((c & 64512) === 55296 && (string.charCodeAt(i + 1) & 64512) === 56320) {
        ++i;
        len += 4;
      } else
        len += 3;
    }
    return len;
  }
  function utf8Array(string) {
    var offset = 0, c1, c2;
    var buffer = new Array(utf8Length(string));
    for (var i = 0, k = string.length; i < k; ++i) {
      c1 = string.charCodeAt(i);
      if (c1 < 128) {
        buffer[offset++] = c1;
      } else if (c1 < 2048) {
        buffer[offset++] = c1 >> 6 | 192;
        buffer[offset++] = c1 & 63 | 128;
      } else if ((c1 & 64512) === 55296 && ((c2 = string.charCodeAt(i + 1)) & 64512) === 56320) {
        c1 = 65536 + ((c1 & 1023) << 10) + (c2 & 1023);
        ++i;
        buffer[offset++] = c1 >> 18 | 240;
        buffer[offset++] = c1 >> 12 & 63 | 128;
        buffer[offset++] = c1 >> 6 & 63 | 128;
        buffer[offset++] = c1 & 63 | 128;
      } else {
        buffer[offset++] = c1 >> 12 | 224;
        buffer[offset++] = c1 >> 6 & 63 | 128;
        buffer[offset++] = c1 & 63 | 128;
      }
    }
    return buffer;
  }
  var BASE64_CODE = "./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split("");
  var BASE64_INDEX = [
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    0,
    1,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    -1,
    -1,
    -1,
    -1,
    -1,
    -1,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    -1,
    -1,
    -1,
    -1,
    -1
  ];
  function base64_encode(b, len) {
    var off = 0, rs = [], c1, c2;
    if (len <= 0 || len > b.length)
      throw Error("Illegal len: " + len);
    while (off < len) {
      c1 = b[off++] & 255;
      rs.push(BASE64_CODE[c1 >> 2 & 63]);
      c1 = (c1 & 3) << 4;
      if (off >= len) {
        rs.push(BASE64_CODE[c1 & 63]);
        break;
      }
      c2 = b[off++] & 255;
      c1 |= c2 >> 4 & 15;
      rs.push(BASE64_CODE[c1 & 63]);
      c1 = (c2 & 15) << 2;
      if (off >= len) {
        rs.push(BASE64_CODE[c1 & 63]);
        break;
      }
      c2 = b[off++] & 255;
      c1 |= c2 >> 6 & 3;
      rs.push(BASE64_CODE[c1 & 63]);
      rs.push(BASE64_CODE[c2 & 63]);
    }
    return rs.join("");
  }
  function base64_decode(s, len) {
    var off = 0, slen = s.length, olen = 0, rs = [], c1, c2, c3, c4, o, code;
    if (len <= 0)
      throw Error("Illegal len: " + len);
    while (off < slen - 1 && olen < len) {
      code = s.charCodeAt(off++);
      c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
      code = s.charCodeAt(off++);
      c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
      if (c1 == -1 || c2 == -1)
        break;
      o = c1 << 2 >>> 0;
      o |= (c2 & 48) >> 4;
      rs.push(String.fromCharCode(o));
      if (++olen >= len || off >= slen)
        break;
      code = s.charCodeAt(off++);
      c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
      if (c3 == -1)
        break;
      o = (c2 & 15) << 4 >>> 0;
      o |= (c3 & 60) >> 2;
      rs.push(String.fromCharCode(o));
      if (++olen >= len || off >= slen)
        break;
      code = s.charCodeAt(off++);
      c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
      o = (c3 & 3) << 6 >>> 0;
      o |= c4;
      rs.push(String.fromCharCode(o));
      ++olen;
    }
    var res = [];
    for (off = 0; off < olen; off++)
      res.push(rs[off].charCodeAt(0));
    return res;
  }
  var BCRYPT_SALT_LEN = 16;
  var GENSALT_DEFAULT_LOG2_ROUNDS = 10;
  var BLOWFISH_NUM_ROUNDS = 16;
  var MAX_EXECUTION_TIME = 100;
  var P_ORIG = [
    608135816,
    2242054355,
    320440878,
    57701188,
    2752067618,
    698298832,
    137296536,
    3964562569,
    1160258022,
    953160567,
    3193202383,
    887688300,
    3232508343,
    3380367581,
    1065670069,
    3041331479,
    2450970073,
    2306472731
  ];
  var S_ORIG = [
    3509652390,
    2564797868,
    805139163,
    3491422135,
    3101798381,
    1780907670,
    3128725573,
    4046225305,
    614570311,
    3012652279,
    134345442,
    2240740374,
    1667834072,
    1901547113,
    2757295779,
    4103290238,
    227898511,
    1921955416,
    1904987480,
    2182433518,
    2069144605,
    3260701109,
    2620446009,
    720527379,
    3318853667,
    677414384,
    3393288472,
    3101374703,
    2390351024,
    1614419982,
    1822297739,
    2954791486,
    3608508353,
    3174124327,
    2024746970,
    1432378464,
    3864339955,
    2857741204,
    1464375394,
    1676153920,
    1439316330,
    715854006,
    3033291828,
    289532110,
    2706671279,
    2087905683,
    3018724369,
    1668267050,
    732546397,
    1947742710,
    3462151702,
    2609353502,
    2950085171,
    1814351708,
    2050118529,
    680887927,
    999245976,
    1800124847,
    3300911131,
    1713906067,
    1641548236,
    4213287313,
    1216130144,
    1575780402,
    4018429277,
    3917837745,
    3693486850,
    3949271944,
    596196993,
    3549867205,
    258830323,
    2213823033,
    772490370,
    2760122372,
    1774776394,
    2652871518,
    566650946,
    4142492826,
    1728879713,
    2882767088,
    1783734482,
    3629395816,
    2517608232,
    2874225571,
    1861159788,
    326777828,
    3124490320,
    2130389656,
    2716951837,
    967770486,
    1724537150,
    2185432712,
    2364442137,
    1164943284,
    2105845187,
    998989502,
    3765401048,
    2244026483,
    1075463327,
    1455516326,
    1322494562,
    910128902,
    469688178,
    1117454909,
    936433444,
    3490320968,
    3675253459,
    1240580251,
    122909385,
    2157517691,
    634681816,
    4142456567,
    3825094682,
    3061402683,
    2540495037,
    79693498,
    3249098678,
    1084186820,
    1583128258,
    426386531,
    1761308591,
    1047286709,
    322548459,
    995290223,
    1845252383,
    2603652396,
    3431023940,
    2942221577,
    3202600964,
    3727903485,
    1712269319,
    422464435,
    3234572375,
    1170764815,
    3523960633,
    3117677531,
    1434042557,
    442511882,
    3600875718,
    1076654713,
    1738483198,
    4213154764,
    2393238008,
    3677496056,
    1014306527,
    4251020053,
    793779912,
    2902807211,
    842905082,
    4246964064,
    1395751752,
    1040244610,
    2656851899,
    3396308128,
    445077038,
    3742853595,
    3577915638,
    679411651,
    2892444358,
    2354009459,
    1767581616,
    3150600392,
    3791627101,
    3102740896,
    284835224,
    4246832056,
    1258075500,
    768725851,
    2589189241,
    3069724005,
    3532540348,
    1274779536,
    3789419226,
    2764799539,
    1660621633,
    3471099624,
    4011903706,
    913787905,
    3497959166,
    737222580,
    2514213453,
    2928710040,
    3937242737,
    1804850592,
    3499020752,
    2949064160,
    2386320175,
    2390070455,
    2415321851,
    4061277028,
    2290661394,
    2416832540,
    1336762016,
    1754252060,
    3520065937,
    3014181293,
    791618072,
    3188594551,
    3933548030,
    2332172193,
    3852520463,
    3043980520,
    413987798,
    3465142937,
    3030929376,
    4245938359,
    2093235073,
    3534596313,
    375366246,
    2157278981,
    2479649556,
    555357303,
    3870105701,
    2008414854,
    3344188149,
    4221384143,
    3956125452,
    2067696032,
    3594591187,
    2921233993,
    2428461,
    544322398,
    577241275,
    1471733935,
    610547355,
    4027169054,
    1432588573,
    1507829418,
    2025931657,
    3646575487,
    545086370,
    48609733,
    2200306550,
    1653985193,
    298326376,
    1316178497,
    3007786442,
    2064951626,
    458293330,
    2589141269,
    3591329599,
    3164325604,
    727753846,
    2179363840,
    146436021,
    1461446943,
    4069977195,
    705550613,
    3059967265,
    3887724982,
    4281599278,
    3313849956,
    1404054877,
    2845806497,
    146425753,
    1854211946,
    1266315497,
    3048417604,
    3681880366,
    3289982499,
    290971e4,
    1235738493,
    2632868024,
    2414719590,
    3970600049,
    1771706367,
    1449415276,
    3266420449,
    422970021,
    1963543593,
    2690192192,
    3826793022,
    1062508698,
    1531092325,
    1804592342,
    2583117782,
    2714934279,
    4024971509,
    1294809318,
    4028980673,
    1289560198,
    2221992742,
    1669523910,
    35572830,
    157838143,
    1052438473,
    1016535060,
    1802137761,
    1753167236,
    1386275462,
    3080475397,
    2857371447,
    1040679964,
    2145300060,
    2390574316,
    1461121720,
    2956646967,
    4031777805,
    4028374788,
    33600511,
    2920084762,
    1018524850,
    629373528,
    3691585981,
    3515945977,
    2091462646,
    2486323059,
    586499841,
    988145025,
    935516892,
    3367335476,
    2599673255,
    2839830854,
    265290510,
    3972581182,
    2759138881,
    3795373465,
    1005194799,
    847297441,
    406762289,
    1314163512,
    1332590856,
    1866599683,
    4127851711,
    750260880,
    613907577,
    1450815602,
    3165620655,
    3734664991,
    3650291728,
    3012275730,
    3704569646,
    1427272223,
    778793252,
    1343938022,
    2676280711,
    2052605720,
    1946737175,
    3164576444,
    3914038668,
    3967478842,
    3682934266,
    1661551462,
    3294938066,
    4011595847,
    840292616,
    3712170807,
    616741398,
    312560963,
    711312465,
    1351876610,
    322626781,
    1910503582,
    271666773,
    2175563734,
    1594956187,
    70604529,
    3617834859,
    1007753275,
    1495573769,
    4069517037,
    2549218298,
    2663038764,
    504708206,
    2263041392,
    3941167025,
    2249088522,
    1514023603,
    1998579484,
    1312622330,
    694541497,
    2582060303,
    2151582166,
    1382467621,
    776784248,
    2618340202,
    3323268794,
    2497899128,
    2784771155,
    503983604,
    4076293799,
    907881277,
    423175695,
    432175456,
    1378068232,
    4145222326,
    3954048622,
    3938656102,
    3820766613,
    2793130115,
    2977904593,
    26017576,
    3274890735,
    3194772133,
    1700274565,
    1756076034,
    4006520079,
    3677328699,
    720338349,
    1533947780,
    354530856,
    688349552,
    3973924725,
    1637815568,
    332179504,
    3949051286,
    53804574,
    2852348879,
    3044236432,
    1282449977,
    3583942155,
    3416972820,
    4006381244,
    1617046695,
    2628476075,
    3002303598,
    1686838959,
    431878346,
    2686675385,
    1700445008,
    1080580658,
    1009431731,
    832498133,
    3223435511,
    2605976345,
    2271191193,
    2516031870,
    1648197032,
    4164389018,
    2548247927,
    300782431,
    375919233,
    238389289,
    3353747414,
    2531188641,
    2019080857,
    1475708069,
    455242339,
    2609103871,
    448939670,
    3451063019,
    1395535956,
    2413381860,
    1841049896,
    1491858159,
    885456874,
    4264095073,
    4001119347,
    1565136089,
    3898914787,
    1108368660,
    540939232,
    1173283510,
    2745871338,
    3681308437,
    4207628240,
    3343053890,
    4016749493,
    1699691293,
    1103962373,
    3625875870,
    2256883143,
    3830138730,
    1031889488,
    3479347698,
    1535977030,
    4236805024,
    3251091107,
    2132092099,
    1774941330,
    1199868427,
    1452454533,
    157007616,
    2904115357,
    342012276,
    595725824,
    1480756522,
    206960106,
    497939518,
    591360097,
    863170706,
    2375253569,
    3596610801,
    1814182875,
    2094937945,
    3421402208,
    1082520231,
    3463918190,
    2785509508,
    435703966,
    3908032597,
    1641649973,
    2842273706,
    3305899714,
    1510255612,
    2148256476,
    2655287854,
    3276092548,
    4258621189,
    236887753,
    3681803219,
    274041037,
    1734335097,
    3815195456,
    3317970021,
    1899903192,
    1026095262,
    4050517792,
    356393447,
    2410691914,
    3873677099,
    3682840055,
    3913112168,
    2491498743,
    4132185628,
    2489919796,
    1091903735,
    1979897079,
    3170134830,
    3567386728,
    3557303409,
    857797738,
    1136121015,
    1342202287,
    507115054,
    2535736646,
    337727348,
    3213592640,
    1301675037,
    2528481711,
    1895095763,
    1721773893,
    3216771564,
    62756741,
    2142006736,
    835421444,
    2531993523,
    1442658625,
    3659876326,
    2882144922,
    676362277,
    1392781812,
    170690266,
    3921047035,
    1759253602,
    3611846912,
    1745797284,
    664899054,
    1329594018,
    3901205900,
    3045908486,
    2062866102,
    2865634940,
    3543621612,
    3464012697,
    1080764994,
    553557557,
    3656615353,
    3996768171,
    991055499,
    499776247,
    1265440854,
    648242737,
    3940784050,
    980351604,
    3713745714,
    1749149687,
    3396870395,
    4211799374,
    3640570775,
    1161844396,
    3125318951,
    1431517754,
    545492359,
    4268468663,
    3499529547,
    1437099964,
    2702547544,
    3433638243,
    2581715763,
    2787789398,
    1060185593,
    1593081372,
    2418618748,
    4260947970,
    69676912,
    2159744348,
    86519011,
    2512459080,
    3838209314,
    1220612927,
    3339683548,
    133810670,
    1090789135,
    1078426020,
    1569222167,
    845107691,
    3583754449,
    4072456591,
    1091646820,
    628848692,
    1613405280,
    3757631651,
    526609435,
    236106946,
    48312990,
    2942717905,
    3402727701,
    1797494240,
    859738849,
    992217954,
    4005476642,
    2243076622,
    3870952857,
    3732016268,
    765654824,
    3490871365,
    2511836413,
    1685915746,
    3888969200,
    1414112111,
    2273134842,
    3281911079,
    4080962846,
    172450625,
    2569994100,
    980381355,
    4109958455,
    2819808352,
    2716589560,
    2568741196,
    3681446669,
    3329971472,
    1835478071,
    660984891,
    3704678404,
    4045999559,
    3422617507,
    3040415634,
    1762651403,
    1719377915,
    3470491036,
    2693910283,
    3642056355,
    3138596744,
    1364962596,
    2073328063,
    1983633131,
    926494387,
    3423689081,
    2150032023,
    4096667949,
    1749200295,
    3328846651,
    309677260,
    2016342300,
    1779581495,
    3079819751,
    111262694,
    1274766160,
    443224088,
    298511866,
    1025883608,
    3806446537,
    1145181785,
    168956806,
    3641502830,
    3584813610,
    1689216846,
    3666258015,
    3200248200,
    1692713982,
    2646376535,
    4042768518,
    1618508792,
    1610833997,
    3523052358,
    4130873264,
    2001055236,
    3610705100,
    2202168115,
    4028541809,
    2961195399,
    1006657119,
    2006996926,
    3186142756,
    1430667929,
    3210227297,
    1314452623,
    4074634658,
    4101304120,
    2273951170,
    1399257539,
    3367210612,
    3027628629,
    1190975929,
    2062231137,
    2333990788,
    2221543033,
    2438960610,
    1181637006,
    548689776,
    2362791313,
    3372408396,
    3104550113,
    3145860560,
    296247880,
    1970579870,
    3078560182,
    3769228297,
    1714227617,
    3291629107,
    3898220290,
    166772364,
    1251581989,
    493813264,
    448347421,
    195405023,
    2709975567,
    677966185,
    3703036547,
    1463355134,
    2715995803,
    1338867538,
    1343315457,
    2802222074,
    2684532164,
    233230375,
    2599980071,
    2000651841,
    3277868038,
    1638401717,
    4028070440,
    3237316320,
    6314154,
    819756386,
    300326615,
    590932579,
    1405279636,
    3267499572,
    3150704214,
    2428286686,
    3959192993,
    3461946742,
    1862657033,
    1266418056,
    963775037,
    2089974820,
    2263052895,
    1917689273,
    448879540,
    3550394620,
    3981727096,
    150775221,
    3627908307,
    1303187396,
    508620638,
    2975983352,
    2726630617,
    1817252668,
    1876281319,
    1457606340,
    908771278,
    3720792119,
    3617206836,
    2455994898,
    1729034894,
    1080033504,
    976866871,
    3556439503,
    2881648439,
    1522871579,
    1555064734,
    1336096578,
    3548522304,
    2579274686,
    3574697629,
    3205460757,
    3593280638,
    3338716283,
    3079412587,
    564236357,
    2993598910,
    1781952180,
    1464380207,
    3163844217,
    3332601554,
    1699332808,
    1393555694,
    1183702653,
    3581086237,
    1288719814,
    691649499,
    2847557200,
    2895455976,
    3193889540,
    2717570544,
    1781354906,
    1676643554,
    2592534050,
    3230253752,
    1126444790,
    2770207658,
    2633158820,
    2210423226,
    2615765581,
    2414155088,
    3127139286,
    673620729,
    2805611233,
    1269405062,
    4015350505,
    3341807571,
    4149409754,
    1057255273,
    2012875353,
    2162469141,
    2276492801,
    2601117357,
    993977747,
    3918593370,
    2654263191,
    753973209,
    36408145,
    2530585658,
    25011837,
    3520020182,
    2088578344,
    530523599,
    2918365339,
    1524020338,
    1518925132,
    3760827505,
    3759777254,
    1202760957,
    3985898139,
    3906192525,
    674977740,
    4174734889,
    2031300136,
    2019492241,
    3983892565,
    4153806404,
    3822280332,
    352677332,
    2297720250,
    60907813,
    90501309,
    3286998549,
    1016092578,
    2535922412,
    2839152426,
    457141659,
    509813237,
    4120667899,
    652014361,
    1966332200,
    2975202805,
    55981186,
    2327461051,
    676427537,
    3255491064,
    2882294119,
    3433927263,
    1307055953,
    942726286,
    933058658,
    2468411793,
    3933900994,
    4215176142,
    1361170020,
    2001714738,
    2830558078,
    3274259782,
    1222529897,
    1679025792,
    2729314320,
    3714953764,
    1770335741,
    151462246,
    3013232138,
    1682292957,
    1483529935,
    471910574,
    1539241949,
    458788160,
    3436315007,
    1807016891,
    3718408830,
    978976581,
    1043663428,
    3165965781,
    1927990952,
    4200891579,
    2372276910,
    3208408903,
    3533431907,
    1412390302,
    2931980059,
    4132332400,
    1947078029,
    3881505623,
    4168226417,
    2941484381,
    1077988104,
    1320477388,
    886195818,
    18198404,
    3786409e3,
    2509781533,
    112762804,
    3463356488,
    1866414978,
    891333506,
    18488651,
    661792760,
    1628790961,
    3885187036,
    3141171499,
    876946877,
    2693282273,
    1372485963,
    791857591,
    2686433993,
    3759982718,
    3167212022,
    3472953795,
    2716379847,
    445679433,
    3561995674,
    3504004811,
    3574258232,
    54117162,
    3331405415,
    2381918588,
    3769707343,
    4154350007,
    1140177722,
    4074052095,
    668550556,
    3214352940,
    367459370,
    261225585,
    2610173221,
    4209349473,
    3468074219,
    3265815641,
    314222801,
    3066103646,
    3808782860,
    282218597,
    3406013506,
    3773591054,
    379116347,
    1285071038,
    846784868,
    2669647154,
    3771962079,
    3550491691,
    2305946142,
    453669953,
    1268987020,
    3317592352,
    3279303384,
    3744833421,
    2610507566,
    3859509063,
    266596637,
    3847019092,
    517658769,
    3462560207,
    3443424879,
    370717030,
    4247526661,
    2224018117,
    4143653529,
    4112773975,
    2788324899,
    2477274417,
    1456262402,
    2901442914,
    1517677493,
    1846949527,
    2295493580,
    3734397586,
    2176403920,
    1280348187,
    1908823572,
    3871786941,
    846861322,
    1172426758,
    3287448474,
    3383383037,
    1655181056,
    3139813346,
    901632758,
    1897031941,
    2986607138,
    3066810236,
    3447102507,
    1393639104,
    373351379,
    950779232,
    625454576,
    3124240540,
    4148612726,
    2007998917,
    544563296,
    2244738638,
    2330496472,
    2058025392,
    1291430526,
    424198748,
    50039436,
    29584100,
    3605783033,
    2429876329,
    2791104160,
    1057563949,
    3255363231,
    3075367218,
    3463963227,
    1469046755,
    985887462
  ];
  var C_ORIG = [
    1332899944,
    1700884034,
    1701343084,
    1684370003,
    1668446532,
    1869963892
  ];
  function _encipher(lr, off, P, S) {
    var n, l = lr[off], r = lr[off + 1];
    l ^= P[0];
    n = S[l >>> 24];
    n += S[256 | l >> 16 & 255];
    n ^= S[512 | l >> 8 & 255];
    n += S[768 | l & 255];
    r ^= n ^ P[1];
    n = S[r >>> 24];
    n += S[256 | r >> 16 & 255];
    n ^= S[512 | r >> 8 & 255];
    n += S[768 | r & 255];
    l ^= n ^ P[2];
    n = S[l >>> 24];
    n += S[256 | l >> 16 & 255];
    n ^= S[512 | l >> 8 & 255];
    n += S[768 | l & 255];
    r ^= n ^ P[3];
    n = S[r >>> 24];
    n += S[256 | r >> 16 & 255];
    n ^= S[512 | r >> 8 & 255];
    n += S[768 | r & 255];
    l ^= n ^ P[4];
    n = S[l >>> 24];
    n += S[256 | l >> 16 & 255];
    n ^= S[512 | l >> 8 & 255];
    n += S[768 | l & 255];
    r ^= n ^ P[5];
    n = S[r >>> 24];
    n += S[256 | r >> 16 & 255];
    n ^= S[512 | r >> 8 & 255];
    n += S[768 | r & 255];
    l ^= n ^ P[6];
    n = S[l >>> 24];
    n += S[256 | l >> 16 & 255];
    n ^= S[512 | l >> 8 & 255];
    n += S[768 | l & 255];
    r ^= n ^ P[7];
    n = S[r >>> 24];
    n += S[256 | r >> 16 & 255];
    n ^= S[512 | r >> 8 & 255];
    n += S[768 | r & 255];
    l ^= n ^ P[8];
    n = S[l >>> 24];
    n += S[256 | l >> 16 & 255];
    n ^= S[512 | l >> 8 & 255];
    n += S[768 | l & 255];
    r ^= n ^ P[9];
    n = S[r >>> 24];
    n += S[256 | r >> 16 & 255];
    n ^= S[512 | r >> 8 & 255];
    n += S[768 | r & 255];
    l ^= n ^ P[10];
    n = S[l >>> 24];
    n += S[256 | l >> 16 & 255];
    n ^= S[512 | l >> 8 & 255];
    n += S[768 | l & 255];
    r ^= n ^ P[11];
    n = S[r >>> 24];
    n += S[256 | r >> 16 & 255];
    n ^= S[512 | r >> 8 & 255];
    n += S[768 | r & 255];
    l ^= n ^ P[12];
    n = S[l >>> 24];
    n += S[256 | l >> 16 & 255];
    n ^= S[512 | l >> 8 & 255];
    n += S[768 | l & 255];
    r ^= n ^ P[13];
    n = S[r >>> 24];
    n += S[256 | r >> 16 & 255];
    n ^= S[512 | r >> 8 & 255];
    n += S[768 | r & 255];
    l ^= n ^ P[14];
    n = S[l >>> 24];
    n += S[256 | l >> 16 & 255];
    n ^= S[512 | l >> 8 & 255];
    n += S[768 | l & 255];
    r ^= n ^ P[15];
    n = S[r >>> 24];
    n += S[256 | r >> 16 & 255];
    n ^= S[512 | r >> 8 & 255];
    n += S[768 | r & 255];
    l ^= n ^ P[16];
    lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];
    lr[off + 1] = l;
    return lr;
  }
  function _streamtoword(data, offp) {
    for (var i = 0, word = 0; i < 4; ++i)
      word = word << 8 | data[offp] & 255, offp = (offp + 1) % data.length;
    return { key: word, offp };
  }
  function _key(key, P, S) {
    var offset = 0, lr = [0, 0], plen = P.length, slen = S.length, sw;
    for (var i = 0; i < plen; i++)
      sw = _streamtoword(key, offset), offset = sw.offp, P[i] = P[i] ^ sw.key;
    for (i = 0; i < plen; i += 2)
      lr = _encipher(lr, 0, P, S), P[i] = lr[0], P[i + 1] = lr[1];
    for (i = 0; i < slen; i += 2)
      lr = _encipher(lr, 0, P, S), S[i] = lr[0], S[i + 1] = lr[1];
  }
  function _ekskey(data, key, P, S) {
    var offp = 0, lr = [0, 0], plen = P.length, slen = S.length, sw;
    for (var i = 0; i < plen; i++)
      sw = _streamtoword(key, offp), offp = sw.offp, P[i] = P[i] ^ sw.key;
    offp = 0;
    for (i = 0; i < plen; i += 2)
      sw = _streamtoword(data, offp), offp = sw.offp, lr[0] ^= sw.key, sw = _streamtoword(data, offp), offp = sw.offp, lr[1] ^= sw.key, lr = _encipher(lr, 0, P, S), P[i] = lr[0], P[i + 1] = lr[1];
    for (i = 0; i < slen; i += 2)
      sw = _streamtoword(data, offp), offp = sw.offp, lr[0] ^= sw.key, sw = _streamtoword(data, offp), offp = sw.offp, lr[1] ^= sw.key, lr = _encipher(lr, 0, P, S), S[i] = lr[0], S[i + 1] = lr[1];
  }
  function _crypt(b, salt, rounds, callback, progressCallback) {
    var cdata = C_ORIG.slice(), clen = cdata.length, err;
    if (rounds < 4 || rounds > 31) {
      err = Error("Illegal number of rounds (4-31): " + rounds);
      if (callback) {
        nextTick(callback.bind(this, err));
        return;
      } else
        throw err;
    }
    if (salt.length !== BCRYPT_SALT_LEN) {
      err = Error(
        "Illegal salt length: " + salt.length + " != " + BCRYPT_SALT_LEN
      );
      if (callback) {
        nextTick(callback.bind(this, err));
        return;
      } else
        throw err;
    }
    rounds = 1 << rounds >>> 0;
    var P, S, i = 0, j;
    if (typeof Int32Array === "function") {
      P = new Int32Array(P_ORIG);
      S = new Int32Array(S_ORIG);
    } else {
      P = P_ORIG.slice();
      S = S_ORIG.slice();
    }
    _ekskey(salt, b, P, S);
    function next() {
      if (progressCallback)
        progressCallback(i / rounds);
      if (i < rounds) {
        var start = Date.now();
        for (; i < rounds; ) {
          i = i + 1;
          _key(b, P, S);
          _key(salt, P, S);
          if (Date.now() - start > MAX_EXECUTION_TIME)
            break;
        }
      } else {
        for (i = 0; i < 64; i++)
          for (j = 0; j < clen >> 1; j++)
            _encipher(cdata, j << 1, P, S);
        var ret = [];
        for (i = 0; i < clen; i++)
          ret.push((cdata[i] >> 24 & 255) >>> 0), ret.push((cdata[i] >> 16 & 255) >>> 0), ret.push((cdata[i] >> 8 & 255) >>> 0), ret.push((cdata[i] & 255) >>> 0);
        if (callback) {
          callback(null, ret);
          return;
        } else
          return ret;
      }
      if (callback)
        nextTick(next);
    }
    if (typeof callback !== "undefined") {
      next();
    } else {
      var res;
      while (true)
        if (typeof (res = next()) !== "undefined")
          return res || [];
    }
  }
  function _hash(password, salt, callback, progressCallback) {
    var err;
    if (typeof password !== "string" || typeof salt !== "string") {
      err = Error("Invalid string / salt: Not a string");
      if (callback) {
        nextTick(callback.bind(this, err));
        return;
      } else
        throw err;
    }
    var minor, offset;
    if (salt.charAt(0) !== "$" || salt.charAt(1) !== "2") {
      err = Error("Invalid salt version: " + salt.substring(0, 2));
      if (callback) {
        nextTick(callback.bind(this, err));
        return;
      } else
        throw err;
    }
    if (salt.charAt(2) === "$")
      minor = String.fromCharCode(0), offset = 3;
    else {
      minor = salt.charAt(2);
      if (minor !== "a" && minor !== "b" && minor !== "y" || salt.charAt(3) !== "$") {
        err = Error("Invalid salt revision: " + salt.substring(2, 4));
        if (callback) {
          nextTick(callback.bind(this, err));
          return;
        } else
          throw err;
      }
      offset = 4;
    }
    if (salt.charAt(offset + 2) > "$") {
      err = Error("Missing salt rounds");
      if (callback) {
        nextTick(callback.bind(this, err));
        return;
      } else
        throw err;
    }
    var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10, r2 = parseInt(salt.substring(offset + 1, offset + 2), 10), rounds = r1 + r2, real_salt = salt.substring(offset + 3, offset + 25);
    password += minor >= "a" ? "\0" : "";
    var passwordb = utf8Array(password), saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);
    function finish(bytes) {
      var res = [];
      res.push("$2");
      if (minor >= "a")
        res.push(minor);
      res.push("$");
      if (rounds < 10)
        res.push("0");
      res.push(rounds.toString());
      res.push("$");
      res.push(base64_encode(saltb, saltb.length));
      res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));
      return res.join("");
    }
    if (typeof callback == "undefined")
      return finish(_crypt(passwordb, saltb, rounds));
    else {
      _crypt(
        passwordb,
        saltb,
        rounds,
        function(err2, bytes) {
          if (err2)
            callback(err2, null);
          else
            callback(null, finish(bytes));
        },
        progressCallback
      );
    }
  }
  function encodeBase64(bytes, length) {
    return base64_encode(bytes, length);
  }
  function decodeBase64(string, length) {
    return base64_decode(string, length);
  }
  const bcrypt = {
    setRandomFallback,
    genSaltSync,
    genSalt,
    hashSync,
    hash,
    compareSync,
    compare,
    getRounds,
    getSalt,
    truncates,
    encodeBase64,
    decodeBase64
  };
  class LocalDataService {
    // 用户注册
    async register(username, password, email) {
      try {
        if (!username || !password || !email) {
          throw new Error("用户名、密码和邮箱都不能为空");
        }
        const existingUser = await dbService.selectSql(
          "SELECT * FROM users WHERE username = ? OR email = ?",
          [username, email]
        );
        if (existingUser && existingUser.length > 0) {
          formatAppLog("log", "at services/localDataService.js:21", "用户已存在，准备删除:", existingUser);
          await dbService.executeSql(
            "DELETE FROM users WHERE username = ? OR email = ?",
            [username, email]
          );
          formatAppLog("log", "at services/localDataService.js:26", "已删除现有用户");
        }
        const userId = v4().replace(/-/g, "").substr(0, 13);
        formatAppLog("log", "at services/localDataService.js:30", "生成的用户ID:", userId);
        let hashedPassword;
        try {
          const salt = bcrypt.genSaltSync(10);
          hashedPassword = bcrypt.hashSync(password, salt);
          formatAppLog("log", "at services/localDataService.js:37", "密码加密成功:", !!hashedPassword);
        } catch (error) {
          formatAppLog("error", "at services/localDataService.js:39", "密码加密失败:", error);
          throw new Error("密码处理失败，请重试");
        }
        if (!hashedPassword) {
          throw new Error("密码处理失败，请重试");
        }
        formatAppLog("log", "at services/localDataService.js:47", "准备插入数据:", {
          userId,
          username,
          passwordLength: hashedPassword ? hashedPassword.length : 0,
          email
        });
        try {
          await dbService.executeSql(
            "INSERT INTO users (user_id, username, password, email) VALUES (?, ?, ?, ?)",
            [userId, username, hashedPassword, email]
          );
          formatAppLog("log", "at services/localDataService.js:59", "数据插入成功");
        } catch (error) {
          formatAppLog("error", "at services/localDataService.js:61", "数据插入失败，详细信息:", {
            error: JSON.stringify(error, null, 2),
            userId,
            username,
            hashedPassword: hashedPassword ? "已加密" : null,
            email
          });
          throw error;
        }
        return {
          user_id: userId,
          username,
          email
        };
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:77", "注册失败:", error);
        throw error;
      }
    }
    // 用户登录
    async login(username, password) {
      try {
        formatAppLog("log", "at services/localDataService.js:85", "开始登录流程:", { username });
        try {
          await dbService.openDatabase();
          formatAppLog("log", "at services/localDataService.js:90", "数据库连接已确认");
        } catch (error) {
          formatAppLog("error", "at services/localDataService.js:92", "数据库连接失败:", error);
          throw new Error("数据库连接失败，请重试");
        }
        let users;
        try {
          users = await dbService.selectSql(
            "SELECT * FROM users WHERE username = ?",
            [username]
          );
          formatAppLog("log", "at services/localDataService.js:103", "用户查询结果:", users);
        } catch (error) {
          formatAppLog("error", "at services/localDataService.js:105", "用户查询失败:", error);
          throw new Error("用户查询失败，请重试");
        }
        if (!users || users.length === 0) {
          formatAppLog("log", "at services/localDataService.js:110", "未找到用户:", username);
          throw new Error("用户不存在");
        }
        const user = users[0];
        formatAppLog("log", "at services/localDataService.js:115", "找到用户:", {
          userId: user.user_id,
          username: user.username,
          hasPassword: !!user.password,
          passwordLength: user.password ? user.password.length : 0
        });
        let isValid = false;
        try {
          isValid = bcrypt.compareSync(password, user.password);
          formatAppLog("log", "at services/localDataService.js:126", "密码验证结果:", isValid);
        } catch (error) {
          formatAppLog("error", "at services/localDataService.js:128", "密码验证出错:", error);
          throw new Error("密码验证失败，请重试");
        }
        if (!isValid) {
          throw new Error("密码错误");
        }
        let categories;
        try {
          categories = await dbService.selectSql(
            "SELECT * FROM categories WHERE user_id = ?",
            [user.user_id]
          );
          formatAppLog("log", "at services/localDataService.js:143", "用户分类查询结果:", categories);
        } catch (error) {
          formatAppLog("error", "at services/localDataService.js:145", "分类查询失败:", error);
        }
        if (!categories || categories.length === 0) {
          try {
            await this.createDefaultCategories(user.user_id);
            formatAppLog("log", "at services/localDataService.js:153", "默认分类创建成功");
          } catch (error) {
            formatAppLog("error", "at services/localDataService.js:155", "创建默认分类失败:", error);
          }
        } else {
          formatAppLog("log", "at services/localDataService.js:159", "用户已有分类，跳过创建");
        }
        const userData = {
          user_id: user.user_id,
          username: user.username,
          email: user.email
        };
        formatAppLog("log", "at services/localDataService.js:168", "登录成功，返回用户数据:", userData);
        return userData;
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:172", "登录失败，完整错误:", error);
        throw error;
      }
    }
    // 获取分类列表
    async getCategories(userId) {
      try {
        formatAppLog("log", "at services/localDataService.js:180", "开始获取分类，用户ID:", userId);
        const categories = await dbService.selectSql(
          "SELECT * FROM categories WHERE user_id = ? ORDER BY level, sort_order",
          [userId]
        );
        formatAppLog("log", "at services/localDataService.js:185", "从数据库获取的原始分类数据:", categories);
        const tree = this.buildCategoryTree(categories);
        formatAppLog("log", "at services/localDataService.js:188", "构建的分类树:", tree);
        return tree;
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:192", "获取分类失败:", error);
        throw error;
      }
    }
    // 保存支出记录
    async saveExpense(userId, date, records) {
      try {
        for (const record of records) {
          const recordId = v4().replace(/-/g, "").substr(0, 13);
          await dbService.executeSql(
            "INSERT INTO expenses (record_id, user_id, category_id, target, amount, record_date) VALUES (?, ?, ?, ?, ?, ?)",
            [recordId, userId, record.category_id, record.target, record.amount, date]
          );
          await this.updateCategoryAmount(record.category_id, record.amount);
        }
        return { success: true };
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:214", "保存支出失败:", error);
        throw error;
      }
    }
    // 修改密码
    async changePassword(userId, oldPassword, newPassword) {
      try {
        if (!oldPassword || !newPassword) {
          throw new Error("旧密码和新密码都不能为空");
        }
        if (newPassword.length < 6) {
          throw new Error("新密码长度不能少于6位");
        }
        const users = await dbService.selectSql(
          "SELECT * FROM users WHERE user_id = ?",
          [userId]
        );
        if (!users || users.length === 0) {
          throw new Error("用户不存在");
        }
        const user = users[0];
        const isValid = bcrypt.compareSync(oldPassword, user.password);
        if (!isValid) {
          throw new Error("旧密码错误");
        }
        const salt = bcrypt.genSaltSync(10);
        const hashedPassword = bcrypt.hashSync(newPassword, salt);
        await dbService.executeSql(
          "UPDATE users SET password = ? WHERE user_id = ?",
          [hashedPassword, userId]
        );
        return { success: true };
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:260", "修改密码失败:", error);
        throw error;
      }
    }
    // 验证用户邮箱（用于密码重置）
    async verifyUserEmail(username, email) {
      try {
        formatAppLog("log", "at services/localDataService.js:268", "开始验证用户邮箱:", { username, email });
        const users = await dbService.selectSql(
          "SELECT * FROM users WHERE username = ?",
          [username]
        );
        if (!users || users.length === 0) {
          throw new Error("用户不存在");
        }
        const user = users[0];
        if (user.email !== email) {
          throw new Error("邮箱不匹配");
        }
        return {
          success: true,
          user_id: user.user_id
        };
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:292", "验证用户邮箱失败:", error);
        throw error;
      }
    }
    // 重置密码（忘记密码时使用）
    async resetPassword(username, email, newPassword) {
      try {
        if (!username || !email || !newPassword) {
          throw new Error("用户名、邮箱和新密码都不能为空");
        }
        if (newPassword.length < 6) {
          throw new Error("新密码长度不能少于6位");
        }
        const verifyResult = await this.verifyUserEmail(username, email);
        if (!verifyResult.success) {
          throw new Error("身份验证失败");
        }
        const salt = bcrypt.genSaltSync(10);
        const hashedPassword = bcrypt.hashSync(newPassword, salt);
        await dbService.executeSql(
          "UPDATE users SET password = ? WHERE user_id = ?",
          [hashedPassword, verifyResult.user_id]
        );
        return { success: true };
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:327", "重置密码失败:", error);
        throw error;
      }
    }
    // 构建分类树
    buildCategoryTree(categories) {
      try {
        const tree = [];
        const map = {};
        categories.forEach((category) => {
          map[category.category_id] = {
            ...category,
            children: [],
            total_amount: parseFloat(category.total_amount || 0),
            level: parseInt(category.level)
            // 确保 level 是数字
          };
        });
        categories.forEach((category) => {
          const node = map[category.category_id];
          if (!category.parent_id) {
            tree.push(node);
          } else {
            const parent = map[category.parent_id];
            if (parent) {
              parent.children.push(node);
            } else {
              formatAppLog("error", "at services/localDataService.js:359", "未找到父分类:", category.parent_id, "对应的分类:", category.name);
            }
          }
        });
        const sortCategories = (cats) => {
          if (!Array.isArray(cats))
            return cats;
          cats.sort((a, b) => {
            if (a.level !== b.level) {
              return a.level - b.level;
            }
            return (a.sort_order || 0) - (b.sort_order || 0);
          });
          cats.forEach((cat) => {
            if (cat.children && cat.children.length > 0) {
              cat.children = sortCategories(cat.children);
            }
          });
          return cats;
        };
        const sortedTree = sortCategories(tree);
        return sortedTree;
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:385", "构建分类树失败:", error);
        throw error;
      }
    }
    // 更新分类金额
    async updateCategoryAmount(categoryId, amount) {
      try {
        const updateCategory = async (id) => {
          const category = await dbService.selectSql(
            "SELECT * FROM categories WHERE category_id = ?",
            [id]
          );
          if (category && category.length > 0) {
            await dbService.executeSql(
              "UPDATE categories SET total_amount = total_amount + ? WHERE category_id = ?",
              [amount, id]
            );
            if (category[0].parent_id) {
              await updateCategory(category[0].parent_id);
            }
          }
        };
        await updateCategory(categoryId);
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:414", "更新分类金额失败:", error);
        throw error;
      }
    }
    // 创建默认分类
    async createDefaultCategories(userId) {
      try {
        const existingCategories = await dbService.selectSql(
          "SELECT category_id FROM categories WHERE user_id = ?",
          [userId]
        );
        if (existingCategories && existingCategories.length > 0) {
          formatAppLog("log", "at services/localDataService.js:429", "用户已有分类，跳过创建");
          return;
        }
        const defaultCategories = [
          // 1. 生存必需类
          ["1", null, "生存必需类", 1, 1],
          ["1.1", "1", "餐饮日用", 2, 1],
          ["1.1.1", "1.1", "食材采购", 3, 1],
          ["1.1.2", "1.1", "外卖外食", 3, 2],
          ["1.1.3", "1.1", "商超购物", 3, 3],
          ["1.2", "1", "交通出行", 2, 2],
          ["1.2.1", "1.2", "公共交通", 3, 1],
          ["1.2.2", "1.2", "油/电/费", 3, 2],
          ["1.2.3", "1.2", "车辆维保", 3, 3],
          ["1.3", "1", "住房相关", 2, 3],
          ["1.3.1", "1.3", "房租/房贷", 3, 1],
          ["1.3.2", "1.3", "水电燃气", 3, 2],
          ["1.3.3", "1.3", "物业费", 3, 3],
          ["1.3.4", "1.3", "维修养护", 3, 4],
          ["1.4", "1", "医疗", 2, 4],
          ["1.4.1", "1.4", "医院医疗", 3, 1],
          ["1.4.2", "1.4", "常规药品", 3, 2],
          ["1.4.3", "1.4", "体检疫苗", 3, 3],
          ["1.5", "1", "父母子女", 2, 5],
          ["1.5.1", "1.5", "父母", 3, 1],
          ["1.5.2", "1.5", "子女教育", 3, 2],
          ["1.5.3", "1.5", "子女消费", 3, 3],
          // 2. 发展提升类
          ["2", null, "发展提升类", 1, 2],
          ["2.1", "2", "教育培训", 2, 1],
          ["2.1.1", "2.1", "课程费用", 3, 1],
          ["2.1.2", "2.1", "书籍资料", 3, 2],
          ["2.1.3", "2.1", "技能提升", 3, 3],
          ["2.2", "2", "职业发展", 2, 2],
          ["2.2.1", "2.2", "专业工具", 3, 1],
          ["2.2.2", "2.2", "公司交费", 3, 2],
          // 3. 品质生活类
          ["3", null, "品质生活类", 1, 3],
          ["3.1", "3", "服饰美容", 2, 1],
          ["3.1.1", "3.1", "服装鞋帽", 3, 1],
          ["3.1.2", "3.1", "护肤美妆", 3, 2],
          ["3.1.3", "3.1", "发型护理", 3, 3],
          ["3.2", "3", "数码电子", 2, 2],
          ["3.2.1", "3.2", "设备购置", 3, 1],
          ["3.2.2", "3.2", "配件耗材", 3, 2],
          ["3.3", "3", "娱乐休闲", 2, 3],
          ["3.3.1", "3.3", "歌舞休闲", 3, 1],
          ["3.3.2", "3.3", "运动健身", 3, 2],
          ["3.3.3", "3.3", "影游演展", 3, 3],
          ["3.3.4", "3.3", "其他娱乐", 3, 4],
          ["3.4", "3", "旅行度假", 2, 4],
          ["3.4.1", "3.4", "交通住宿", 3, 1],
          ["3.4.2", "3.4", "景区消费", 3, 2],
          // 4. 人际往来类
          ["4", null, "人际往来类", 1, 4],
          ["4.1", "4", "社交人情", 2, 1],
          ["4.1.1", "4.1", "节日礼品", 3, 1],
          ["4.1.2", "4.1", "聚餐请客", 3, 2],
          ["4.1.3", "4.1", "红包礼金", 3, 3],
          ["4.2", "4", "宠物养护", 2, 2],
          ["4.2.1", "4.2", "宠物费用", 3, 1],
          // 5. 资产流动类
          ["5", null, "资产流动类", 1, 5],
          ["5.1", "5", "投资理财", 2, 1],
          ["5.1.1", "5.1", "股基理财", 3, 1],
          ["5.1.2", "5.1", "资产投资", 3, 2],
          ["5.1.3", "5.1", "保险费用", 3, 3],
          ["5.2", "5", "债务管理", 2, 2],
          ["5.2.1", "5.2", "贷款还款", 3, 1],
          ["5.2.2", "5.2", "其他借款", 3, 2],
          // 6. 其他支出类
          ["6", null, "其他支出类", 1, 6],
          ["6.1", "6", "其他支出", 2, 1],
          ["6.1.1", "6.1", "罚款税金", 3, 1],
          ["6.1.2", "6.1", "捐赠", 3, 2],
          ["6.1.3", "6.1", "其他支出", 3, 3]
        ];
        formatAppLog("log", "at services/localDataService.js:516", "开始创建默认分类，用户ID:", userId);
        const categoryMap = /* @__PURE__ */ new Map();
        for (const [displayId, parentDisplayId, name, level, sortOrder] of defaultCategories) {
          const uniquePrefix = v4().replace(/-/g, "").substr(0, 8);
          const categoryId = `${uniquePrefix}_${displayId}`;
          let parentId = null;
          if (parentDisplayId) {
            parentId = categoryMap.get(parentDisplayId);
            if (!parentId) {
              formatAppLog("error", "at services/localDataService.js:530", "未找到父分类:", parentDisplayId);
              continue;
            }
          }
          try {
            await dbService.executeSql(
              "INSERT INTO categories (category_id, user_id, parent_id, name, level, sort_order) VALUES (?, ?, ?, ?, ?, ?)",
              [categoryId, userId, parentId, name, level, sortOrder]
            );
            formatAppLog("log", "at services/localDataService.js:540", "分类创建成功:", { categoryId, displayId, name });
            categoryMap.set(displayId, categoryId);
          } catch (error) {
            formatAppLog("error", "at services/localDataService.js:545", "创建单个分类失败:", {
              categoryId,
              displayId,
              parentId,
              name,
              level,
              sortOrder,
              error: JSON.stringify(error, null, 2)
            });
            throw error;
          }
        }
        formatAppLog("log", "at services/localDataService.js:558", "所有默认分类创建完成");
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:560", "创建默认分类失败:", error);
        throw error;
      }
    }
    // 添加新分类
    async addCategory(userId, name, parentId, level, sortOrder) {
      try {
        const categoryId = v4().replace(/-/g, "").substr(0, 13);
        await dbService.executeSql(
          "INSERT INTO categories (category_id, user_id, parent_id, name, level, sort_order) VALUES (?, ?, ?, ?, ?, ?)",
          [categoryId, userId, parentId, name, level, sortOrder]
        );
        return {
          category_id: categoryId,
          user_id: userId,
          parent_id: parentId,
          name,
          level,
          sort_order: sortOrder,
          total_amount: 0,
          children: []
        };
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:589", "添加分类失败:", error);
        throw error;
      }
    }
    // 修改分类
    async updateCategory(categoryId, name) {
      try {
        await dbService.executeSql(
          "UPDATE categories SET name = ? WHERE category_id = ?",
          [name, categoryId]
        );
        return { success: true };
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:603", "修改分类失败:", error);
        throw error;
      }
    }
    // 删除分类
    async deleteCategory(categoryId) {
      try {
        const children = await dbService.selectSql(
          "SELECT category_id FROM categories WHERE parent_id = ?",
          [categoryId]
        );
        if (children && children.length > 0) {
          throw new Error("该分类下有子分类，无法删除");
        }
        const expenses = await dbService.selectSql(
          "SELECT record_id FROM expenses WHERE category_id = ?",
          [categoryId]
        );
        if (expenses && expenses.length > 0) {
          throw new Error("该分类下有支出记录，无法删除");
        }
        await dbService.executeSql(
          "DELETE FROM categories WHERE category_id = ?",
          [categoryId]
        );
        return { success: true };
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:639", "删除分类失败:", error);
        throw error;
      }
    }
    // 更新分类排序
    async updateCategoryOrder(categoryId, newSortOrder) {
      try {
        await dbService.executeSql(
          "UPDATE categories SET sort_order = ? WHERE category_id = ?",
          [newSortOrder, categoryId]
        );
        return { success: true };
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:653", "更新分类排序失败:", error);
        throw error;
      }
    }
    // 清空用户的支出记录
    async clearUserExpenses(userId) {
      try {
        formatAppLog("log", "at services/localDataService.js:661", "开始清空用户支出记录:", userId);
        const timestamp = (/* @__PURE__ */ new Date()).toISOString().slice(0, 10);
        const backupFileName = `expenses_backup_${timestamp}.json`;
        let backupFilePath = "";
        const expenses = await dbService.selectSql(
          "SELECT * FROM expenses WHERE user_id = ?",
          [userId]
        );
        formatAppLog("log", "at services/localDataService.js:674", `找到 ${expenses ? expenses.length : 0} 条支出记录待清空`);
        if (expenses && expenses.length > 0) {
          try {
            formatAppLog("log", "at services/localDataService.js:679", "开始请求存储权限");
            await new Promise((resolve, reject) => {
              plus.android.requestPermissions(
                ["android.permission.WRITE_EXTERNAL_STORAGE"],
                function(resultObj) {
                  formatAppLog("log", "at services/localDataService.js:686", "权限请求结果:", resultObj);
                  if (resultObj.granted.length === 1) {
                    resolve();
                  } else {
                    reject(new Error("未授予存储权限"));
                  }
                },
                function(error) {
                  formatAppLog("error", "at services/localDataService.js:694", "权限请求失败:", error);
                  reject(error);
                }
              );
            });
            formatAppLog("log", "at services/localDataService.js:700", "已获得存储权限，准备导出数据");
            const Environment = plus.android.importClass("android.os.Environment");
            const File = plus.android.importClass("java.io.File");
            const main = plus.android.runtimeMainActivity();
            const externalStorageDir = Environment.getExternalStorageDirectory();
            const filePath = externalStorageDir.getAbsolutePath() + "/Download/" + backupFileName;
            formatAppLog("log", "at services/localDataService.js:712", "使用外部存储路径:", filePath);
            const fileDir = new File(filePath).getParentFile();
            if (!fileDir.exists()) {
              fileDir.mkdirs();
            }
            const backupData = JSON.stringify(expenses, null, 2);
            const FileOutputStream = plus.android.importClass("java.io.FileOutputStream");
            const OutputStreamWriter = plus.android.importClass("java.io.OutputStreamWriter");
            const BufferedWriter = plus.android.importClass("java.io.BufferedWriter");
            const fos = new FileOutputStream(filePath);
            const osw = new OutputStreamWriter(fos, "UTF-8");
            const bw = new BufferedWriter(osw);
            bw.write(backupData);
            bw.flush();
            bw.close();
            osw.close();
            fos.close();
            formatAppLog("log", "at services/localDataService.js:738", "数据备份成功:", filePath);
            backupFilePath = filePath;
          } catch (backupError) {
            formatAppLog("error", "at services/localDataService.js:741", "备份数据失败:", backupError);
          }
        }
        await dbService.executeSql(
          "DELETE FROM expenses WHERE user_id = ?",
          [userId]
        );
        await dbService.executeSql(
          "UPDATE categories SET total_amount = 0 WHERE user_id = ?",
          [userId]
        );
        formatAppLog("log", "at services/localDataService.js:758", "清空支出记录成功");
        return {
          success: true,
          message: "清空成功",
          backupFile: backupFilePath || backupFileName
        };
      } catch (error) {
        formatAppLog("error", "at services/localDataService.js:765", "清空支出记录失败:", error);
        throw error;
      }
    }
  }
  const localDataService = new LocalDataService();
  const store = createStore({
    state: {
      user: uni.getStorageSync("user") ? JSON.parse(uni.getStorageSync("user")) : null,
      categories: [],
      isLoading: false,
      error: null,
      categoryMap: /* @__PURE__ */ new Map(),
      apiSettings: uni.getStorageSync("apiSettings") ? JSON.parse(uni.getStorageSync("apiSettings")) : {
        apiKey: "sk-38ff2a6b9f754b059fa42839d5a4b426",
        model: "deepseek-chat",
        temperature: 0.2
      }
    },
    mutations: {
      setUser(state, user) {
        state.user = user;
        if (user) {
          uni.setStorageSync("user", JSON.stringify(user));
        } else {
          uni.removeStorageSync("user");
        }
      },
      setApiSettings(state, settings) {
        state.apiSettings = settings;
        uni.setStorageSync("apiSettings", JSON.stringify(settings));
      },
      clearUserInfo(state) {
        state.user = null;
        state.categories = [];
        state.error = null;
        uni.removeStorageSync("user");
      },
      setLoading(state, isLoading) {
        state.isLoading = isLoading;
      },
      setError(state, error) {
        state.error = error;
      },
      updateCategories(state, categories) {
        const categoryMap = /* @__PURE__ */ new Map();
        const processCategories = (items, parentId = null) => {
          items.forEach((category) => {
            category.total_amount = Number(category.total_amount || 0);
            category.parent_id = parentId;
            categoryMap.set(category.category_id, category);
            if (category.children && category.children.length > 0) {
              processCategories(category.children, category.category_id);
            }
          });
        };
        processCategories(categories);
        categoryMap.forEach((category) => {
          if (category.parent_id) {
            const parent = categoryMap.get(category.parent_id);
            if (parent) {
              if (!parent.children) {
                parent.children = [];
              }
              if (!parent.children.find((child) => child.category_id === category.category_id)) {
                parent.children.push(category);
              }
            }
          }
        });
        state.categories = categories.filter((c) => !c.parent_id);
        state.categoryMap = categoryMap;
      },
      updateCategoryAmount(state, { categoryId, amount }) {
        const category = state.categoryMap.get(categoryId);
        if (!category)
          return;
        category.total_amount = (category.total_amount || 0) + amount;
        const updateParentAmount = (parentId, deltaAmount) => {
          if (!parentId)
            return;
          const parent = state.categoryMap.get(parentId);
          if (parent) {
            parent.total_amount = (parent.total_amount || 0) + deltaAmount;
            updateParentAmount(parent.parent_id, deltaAmount);
          }
        };
        updateParentAmount(category.parent_id, amount);
      }
    },
    actions: {
      async init({ commit }) {
        try {
          await dbService.open();
          await dbService.initTables();
        } catch (error) {
          formatAppLog("error", "at store/index.js:112", "初始化失败:", error);
          commit("setError", error.message);
        }
      },
      async register({ commit }, { username, password, email }) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          const user = await localDataService.register(username, password, email);
          commit("setUser", user);
          return user;
        } catch (error) {
          formatAppLog("error", "at store/index.js:126", "注册失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      },
      async login({ commit }, { username, password }) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          const user = await localDataService.login(username, password);
          commit("setUser", user);
          const categories = await localDataService.getCategories(user.user_id);
          formatAppLog("log", "at store/index.js:144", "登录后获取的分类数据:", categories);
          commit("updateCategories", categories);
          return user;
        } catch (error) {
          formatAppLog("error", "at store/index.js:148", "登录失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      },
      logout({ commit }) {
        commit("clearUserInfo");
      },
      async fetchCategories({ commit, state }) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          if (!state.user) {
            throw new Error("用户未登录");
          }
          const categories = await localDataService.getCategories(state.user.user_id);
          commit("updateCategories", categories);
          return categories;
        } catch (error) {
          formatAppLog("error", "at store/index.js:174", "获取分类失败:", error);
          commit("setError", error.message);
          return [];
        } finally {
          commit("setLoading", false);
        }
      },
      async saveExpense({ commit, state, dispatch }, { date, records }) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          if (!state.user) {
            throw new Error("用户未登录");
          }
          await localDataService.saveExpense(state.user.user_id, date, records);
          await dispatch("fetchCategories");
          return { success: true };
        } catch (error) {
          formatAppLog("error", "at store/index.js:198", "保存支出失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      },
      async changePassword({ commit, state }, { oldPassword, newPassword }) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          if (!state.user) {
            throw new Error("用户未登录");
          }
          const result = await localDataService.changePassword(
            state.user.user_id,
            oldPassword,
            newPassword
          );
          if (result.success) {
            commit("clearUserInfo");
            uni.reLaunch({
              url: "/pages/login/login"
            });
          }
          return result;
        } catch (error) {
          formatAppLog("error", "at store/index.js:232", "修改密码失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      },
      updateCategories({ commit }, categories) {
        commit("updateCategories", categories);
      },
      async addCategory({ commit, state, dispatch }, { name, parentId, level, sortOrder }) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          if (!state.user) {
            throw new Error("用户未登录");
          }
          const newCategory = await localDataService.addCategory(
            state.user.user_id,
            name,
            parentId,
            level,
            sortOrder
          );
          await dispatch("fetchCategories");
          return newCategory;
        } catch (error) {
          formatAppLog("error", "at store/index.js:266", "添加分类失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      },
      async updateCategory({ commit, dispatch }, { categoryId, name }) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          await localDataService.updateCategory(categoryId, name);
          await dispatch("fetchCategories");
          return { success: true };
        } catch (error) {
          formatAppLog("error", "at store/index.js:286", "修改分类失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      },
      async deleteCategory({ commit, dispatch }, categoryId) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          await localDataService.deleteCategory(categoryId);
          await dispatch("fetchCategories");
          return { success: true };
        } catch (error) {
          formatAppLog("error", "at store/index.js:306", "删除分类失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      },
      async updateCategoryOrder({ commit, dispatch }, { categoryId, newSortOrder }) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          await localDataService.updateCategoryOrder(categoryId, newSortOrder);
          await dispatch("fetchCategories");
          return { success: true };
        } catch (error) {
          formatAppLog("error", "at store/index.js:326", "更新分类排序失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      },
      // 清空用户的支出记录
      async clearUserData({ commit, state, dispatch }) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          if (!state.user) {
            throw new Error("用户未登录");
          }
          const result = await localDataService.clearUserExpenses(state.user.user_id);
          await dispatch("fetchCategories");
          return result;
        } catch (error) {
          formatAppLog("error", "at store/index.js:351", "清空支出记录失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      },
      // 验证用户邮箱（用于密码重置）
      async verifyUserEmail({ commit }, { username, email }) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          const result = await localDataService.verifyUserEmail(username, email);
          return result;
        } catch (error) {
          formatAppLog("error", "at store/index.js:368", "验证用户邮箱失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      },
      // 重置密码（忘记密码时使用）
      async resetPassword({ commit }, { username, email, newPassword }) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          const result = await localDataService.resetPassword(username, email, newPassword);
          return result;
        } catch (error) {
          formatAppLog("error", "at store/index.js:385", "重置密码失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      },
      // 保存 API 设置
      async saveApiSettings({ commit }, settings) {
        try {
          commit("setLoading", true);
          commit("setError", null);
          if (!settings.apiKey) {
            throw new Error("API 密钥不能为空");
          }
          if (!settings.model) {
            throw new Error("模型不能为空");
          }
          commit("setApiSettings", settings);
          return { success: true };
        } catch (error) {
          formatAppLog("error", "at store/index.js:413", "保存 API 设置失败:", error);
          commit("setError", error.message);
          throw error;
        } finally {
          commit("setLoading", false);
        }
      }
    },
    getters: {
      isLoggedIn: (state) => !!state.user,
      categories: (state) => state.categories,
      isLoading: (state) => state.isLoading,
      error: (state) => state.error,
      getCategoryById: (state) => (categoryId) => {
        return state.categoryMap.get(categoryId) || null;
      },
      getCategoryTotal: (state) => (categoryId) => {
        const calculateTotal = (category2) => {
          if (!category2.children || category2.children.length === 0) {
            return category2.total_amount || 0;
          }
          return category2.children.reduce((total, child) => {
            return total + calculateTotal(child);
          }, category2.total_amount || 0);
        };
        const category = state.categoryMap.get(categoryId);
        return category ? calculateTotal(category) : 0;
      }
    }
  });
  function createApp() {
    const app = vue.createVueApp(App);
    app.use(store);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
