<template>
	<view class="content">
		<view class="header">
			<view class="header-left" @click="goBack">
				<text class="header-back">返回</text>
			</view>
			<text class="header-title">创建子项目</text>
			<view class="header-right"></view>
		</view>

		<scroll-view class="form-container" scroll-y enable-back-to-top>
			<view v-if="isLoading" class="empty-tip">
				<text>加载中...</text>
			</view>

			<view v-else class="form-content">
				<!-- 项目选择 -->
				<view class="form-item">
					<text class="form-label">选择上级项目 *</text>
					<view class="input-container">
						<picker
							:value="selectedProjectIndex"
							:range="projectOptions"
							@change="onProjectChange"
						>
							<view class="picker-view">
								<text v-if="selectedProject">{{ selectedProject.projectName }}</text>
								<text v-else class="picker-placeholder">请选择上级项目</text>
							</view>
						</picker>
					</view>
					<text v-if="errors.projectName" class="form-error">{{ errors.projectName }}</text>
				</view>

				<!-- 子项目名称 -->
				<view class="form-item">
					<text class="form-label">子项目名称 *</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.subprojectName"
							placeholder="请输入子项目名称"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
					<text v-if="errors.subprojectName" class="form-error">{{ errors.subprojectName }}</text>
				</view>

				<!-- 建设地点（自动填入，可修改） -->
				<view class="form-item">
					<text class="form-label">建设地点</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.constructionLocation"
							placeholder="建设地点（自动关联上级项目）"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
					<text class="form-hint">自动关联上级项目的建设地点，可修改</text>
				</view>

				<!-- 建设单位（自动填入，可修改） -->
				<view class="form-item">
					<text class="form-label">建设单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.constructionUnit"
							placeholder="建设单位（自动关联上级项目）"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
					<text class="form-hint">自动关联上级项目的企业法人，可修改</text>
				</view>

				<!-- 代建单位 -->
				<view class="form-item">
					<text class="form-label">代建单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.agentUnit"
							placeholder="请输入代建单位"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
				</view>

				<!-- 勘察单位 -->
				<view class="form-item">
					<text class="form-label">勘察单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.surveyUnit"
							placeholder="请输入勘察单位"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
				</view>

				<!-- 设计单位 -->
				<view class="form-item">
					<text class="form-label">设计单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.designUnit"
							placeholder="请输入设计单位"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
				</view>

				<!-- 监理单位 -->
				<view class="form-item">
					<text class="form-label">监理单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.supervisionUnit"
							placeholder="请输入监理单位"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
				</view>

				<!-- 施工单位 -->
				<view class="form-item">
					<text class="form-label">施工单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.constructorUnit"
							placeholder="请输入施工单位"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
				</view>

				<!-- 项目描述（自动填入，可修改） -->
				<view class="form-item">
					<text class="form-label">项目描述</text>
					<view class="input-container">
						<textarea
							class="form-textarea"
							v-model="formData.projectDescription"
							placeholder="项目描述（自动关联上级项目）"
							auto-height
							@focus="onInputFocus"
						/>
					</view>
					<text class="form-hint">自动关联上级项目的建设规模及内容，可修改</text>
				</view>

				<!-- 操作按钮 -->
				<view class="button-group">
					<button class="cancel-button" @click="goBack">取消</button>
					<button class="save-button" @click="saveSubproject" :disabled="isSaving">
						{{ isSaving ? '创建中...' : '创建子项目' }}
					</button>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		queryTableData,
		insertData
	} from '@/utils/sqlite.js';

	export default {
		data() {
			return {
				isLoading: false,
				isSaving: false,
				projects: [],
				projectOptions: [],
				selectedProjectIndex: 0,
				selectedProject: null,
				formData: {
					subprojectName: '',
					constructionLocation: '',
					constructionUnit: '',
					agentUnit: '',
					surveyUnit: '',
					designUnit: '',
					supervisionUnit: '',
					constructorUnit: '',
					projectDescription: ''
				},
				errors: {}
			}
		},
		onLoad() {
			this.loadProjects();
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 输入框获得焦点时的处理
			onInputFocus(e) {
				// 延迟一点时间，等待输入法弹出
				setTimeout(() => {
					// 获取当前输入框的位置并滚动到可见区域
					const query = uni.createSelectorQuery().in(this);
					query.select('.form-container').scrollOffset((res) => {
						if (res) {
							// 滚动到输入框位置，留出一些空间
							const scrollTop = res.scrollTop + 200;
							uni.pageScrollTo({
								scrollTop: scrollTop,
								duration: 300
							});
						}
					}).exec();
				}, 300);
			},

			// 加载项目列表
			async loadProjects() {
				this.isLoading = true;
				try {
					const projects = await queryTableData('projects');
					this.projects = projects || [];
					this.projectOptions = this.projects.map(p => p.projectName || '未命名项目');

					if (this.projects.length === 0) {
						uni.showToast({
							title: '请先创建项目',
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 2000);
					}
				} catch (e) {
					console.error('加载项目列表失败', e);
					uni.showToast({
						title: '加载项目列表失败',
						icon: 'none'
					});
				} finally {
					this.isLoading = false;
				}
			},

			// 项目选择变更
			onProjectChange(e) {
				const index = e.detail.value;
				this.selectedProjectIndex = index;
				this.selectedProject = this.projects[index];

				// 自动填入关联信息
				if (this.selectedProject) {
					this.formData.constructionLocation = this.selectedProject.constructionLocation || '';
					this.formData.constructionUnit = this.selectedProject.legalPerson || '';
					this.formData.projectDescription = this.selectedProject.constructionScale || '';
				}
			},

			// 验证表单
			validateForm() {
				const errors = {};
				let isValid = true;

				// 验证项目选择
				if (!this.selectedProject) {
					errors.projectName = '请选择上级项目';
					isValid = false;
				}

				// 验证子项目名称
				if (!this.formData.subprojectName.trim()) {
					errors.subprojectName = '请输入子项目名称';
					isValid = false;
				}

				this.errors = errors;
				return isValid;
			},

			// 保存子项目
			async saveSubproject() {
				if (!this.validateForm()) {
					return;
				}

				this.isSaving = true;
				try {
					const data = {
						projectId: this.selectedProject.id,
						projectName: this.selectedProject.projectName,
						subprojectName: this.formData.subprojectName.trim(),
						constructionLocation: this.formData.constructionLocation.trim(),
						constructionUnit: this.formData.constructionUnit.trim(),
						agentUnit: this.formData.agentUnit.trim(),
						surveyUnit: this.formData.surveyUnit.trim(),
						designUnit: this.formData.designUnit.trim(),
						supervisionUnit: this.formData.supervisionUnit.trim(),
						constructorUnit: this.formData.constructorUnit.trim(),
						projectDescription: this.formData.projectDescription.trim()
					};

					const result = await insertData('subprojects', data);

					if (result) {
						uni.showToast({
							title: '子项目创建成功',
							icon: 'success'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else {
						uni.showToast({
							title: '创建失败',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('保存子项目失败', e);
					uni.showToast({
						title: '保存失败: ' + (e.message || e),
						icon: 'none'
					});
				} finally {
					this.isSaving = false;
				}
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 20rpx 30rpx;
		padding-top: var(--status-bar-height);
	}

	.header-left, .header-right {
		width: 120rpx;
	}

	.header-back {
		color: #FFFFFF;
		font-size: 28rpx;
	}

	.header-title {
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: bold;
	}

	.form-container {
		flex: 1;
		height: 0; /* 重要：让scroll-view能够正确计算高度 */
	}

	.form-content {
		padding: 30rpx;
		padding-bottom: 100rpx; /* 增加底部空间，避免输入法遮挡 */
	}

	.empty-tip {
		text-align: center;
		padding: 100rpx 0;
		color: #999999;
		font-size: 28rpx;
	}

	.form-item {
		margin-bottom: 40rpx;
	}

	.form-label {
		display: block;
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 20rpx;
		font-weight: 500;
	}

	.input-container {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		border: 2rpx solid #E5E5E5;
		overflow: hidden;
	}

	.form-input {
		width: 100%;
		padding: 24rpx 20rpx;
		font-size: 28rpx;
		color: #333333;
		background-color: transparent;
		border: none;
		outline: none;
	}

	.form-textarea {
		width: 100%;
		padding: 24rpx 20rpx;
		font-size: 28rpx;
		color: #333333;
		background-color: transparent;
		border: none;
		outline: none;
		min-height: 120rpx;
		resize: none;
	}

	.picker-view {
		padding: 24rpx 20rpx;
		font-size: 28rpx;
		color: #333333;
		background-color: #FFFFFF;
	}

	.picker-placeholder {
		color: #999999;
	}

	.form-error {
		display: block;
		color: #FF3B30;
		font-size: 24rpx;
		margin-top: 10rpx;
	}

	.form-hint {
		display: block;
		color: #666666;
		font-size: 24rpx;
		margin-top: 10rpx;
		font-style: italic;
	}

	.button-group {
		display: flex;
		gap: 20rpx;
		margin-top: 60rpx;
		padding-bottom: 40rpx;
	}

	.cancel-button {
		flex: 1;
		height: 88rpx;
		background-color: #F2F2F7;
		color: #666666;
		border: none;
		border-radius: 8rpx;
		font-size: 32rpx;
		font-weight: 500;
	}

	.save-button {
		flex: 2;
		height: 88rpx;
		background-color: #007AFF;
		color: #FFFFFF;
		border: none;
		border-radius: 8rpx;
		font-size: 32rpx;
		font-weight: 500;
	}

	.save-button:disabled {
		background-color: #CCCCCC;
		color: #999999;
	}
</style>
