<template>
	<view class="content">
		<AppHeader
			title="subprojects"
			subtitle="子项目表"
		/>

		<scroll-view class="form-container" scroll-y enable-back-to-top>
			<view v-if="isLoading" class="empty-tip">
				<text>加载中...</text>
			</view>

			<view v-else class="form-content">
				<!-- 项目选择 -->
				<view class="form-item">
					<text class="form-label">选择上级项目 *</text>
					<view class="input-container">
						<picker
							:value="selectedProjectIndex"
							:range="projectOptions"
							@change="onProjectChange"
						>
							<view class="picker-view">
								<text v-if="selectedProject">{{ selectedProject.projectName }}</text>
								<text v-else class="picker-placeholder">请选择上级项目</text>
							</view>
						</picker>
					</view>
					<text v-if="errors.projectName" class="form-error">{{ errors.projectName }}</text>
				</view>

				<!-- 子项目名称 -->
				<view class="form-item">
					<text class="form-label">子项目名称 *</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.subprojectName"
							placeholder="请输入子项目名称"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
					<text v-if="errors.subprojectName" class="form-error">{{ errors.subprojectName }}</text>
				</view>

				<!-- 建设地点（自动填入，可修改） -->
				<view class="form-item">
					<text class="form-label">建设地点</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.constructionLocation"
							placeholder="建设地点（自动关联上级项目）"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
					<text class="form-hint">自动关联上级项目的建设地点，可修改</text>
				</view>

				<!-- 建设单位（自动填入，可修改） -->
				<view class="form-item">
					<text class="form-label">建设单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.constructionUnit"
							placeholder="建设单位（自动关联上级项目）"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
					<text class="form-hint">自动关联上级项目的企业法人，可修改</text>
				</view>

				<!-- 代建单位 -->
				<view class="form-item">
					<text class="form-label">代建单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.agentUnit"
							placeholder="请输入代建单位"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
				</view>

				<!-- 勘察单位 -->
				<view class="form-item">
					<text class="form-label">勘察单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.surveyUnit"
							placeholder="请输入勘察单位"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
				</view>

				<!-- 设计单位 -->
				<view class="form-item">
					<text class="form-label">设计单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.designUnit"
							placeholder="请输入设计单位"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
				</view>

				<!-- 监理单位 -->
				<view class="form-item">
					<text class="form-label">监理单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.supervisionUnit"
							placeholder="请输入监理单位"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
				</view>

				<!-- 施工单位 -->
				<view class="form-item">
					<text class="form-label">施工单位</text>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.constructorUnit"
							placeholder="请输入施工单位"
							type="text"
							@focus="onInputFocus"
						/>
					</view>
				</view>

				<!-- 项目描述（自动填入，可修改） -->
				<view class="form-item">
					<text class="form-label">项目描述</text>
					<view class="input-container">
						<textarea
							class="form-textarea"
							v-model="formData.projectDescription"
							placeholder="项目描述（自动关联上级项目）"
							auto-height
							@focus="onInputFocus"
						/>
					</view>
					<text class="form-hint">自动关联上级项目的建设规模及内容，可修改</text>
				</view>

				<!-- 操作按钮 -->
				<view class="button-group">
					<button class="cancel-button" @click="goBack">取消</button>
					<button class="save-button" @click="saveSubproject" :disabled="isSaving">
						{{ isSaving ? '创建中...' : '创建子项目' }}
					</button>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		queryTableData,
		insertData
	} from '@/utils/sqlite.js';
	import { FormUtils } from '@/utils/form.js';
	import AppHeader from '@/components/AppHeader.vue';

	export default {
		components: {
			AppHeader
		},
		data() {
			return {
				isLoading: false,
				isSaving: false,
				projects: [],
				projectOptions: [],
				selectedProjectIndex: 0,
				selectedProject: null,
				formData: {
					subprojectName: '',
					constructionLocation: '',
					constructionUnit: '',
					agentUnit: '',
					surveyUnit: '',
					designUnit: '',
					supervisionUnit: '',
					constructorUnit: '',
					projectDescription: ''
				},
				errors: {}
			}
		},
		onLoad() {
			this.loadProjects();
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 输入框获得焦点时的处理
			onInputFocus() {
				FormUtils.onInputFocus();
			},

			// 加载项目列表
			async loadProjects() {
				this.isLoading = true;
				try {
					const projects = await queryTableData('projects');
					this.projects = projects || [];
					this.projectOptions = this.projects.map(p => p.projectName || '未命名项目');

					if (this.projects.length === 0) {
						uni.showToast({
							title: '请先创建项目',
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 2000);
					}
				} catch (e) {
					console.error('加载项目列表失败', e);
					uni.showToast({
						title: '加载项目列表失败',
						icon: 'none'
					});
				} finally {
					this.isLoading = false;
				}
			},

			// 项目选择变更
			onProjectChange(e) {
				const index = e.detail.value;
				this.selectedProjectIndex = index;
				this.selectedProject = this.projects[index];

				// 自动填入关联信息
				if (this.selectedProject) {
					this.formData.constructionLocation = this.selectedProject.constructionLocation || '';
					this.formData.constructionUnit = this.selectedProject.legalPerson || '';
					this.formData.projectDescription = this.selectedProject.constructionScale || '';
				}
			},

			// 验证表单
			validateForm() {
				const errors = {};
				let isValid = true;

				// 验证项目选择
				if (!this.selectedProject) {
					errors.projectName = '请选择上级项目';
					isValid = false;
				}

				// 验证子项目名称
				if (!this.formData.subprojectName.trim()) {
					errors.subprojectName = '请输入子项目名称';
					isValid = false;
				}

				this.errors = errors;
				return isValid;
			},

			// 保存子项目
			async saveSubproject() {
				if (!this.validateForm()) {
					return;
				}

				this.isSaving = true;
				try {
					const data = {
						projectId: this.selectedProject.id,
						projectName: this.selectedProject.projectName,
						subprojectName: this.formData.subprojectName.trim(),
						constructionLocation: this.formData.constructionLocation.trim(),
						constructionUnit: this.formData.constructionUnit.trim(),
						agentUnit: this.formData.agentUnit.trim(),
						surveyUnit: this.formData.surveyUnit.trim(),
						designUnit: this.formData.designUnit.trim(),
						supervisionUnit: this.formData.supervisionUnit.trim(),
						constructorUnit: this.formData.constructorUnit.trim(),
						projectDescription: this.formData.projectDescription.trim()
					};

					const result = await insertData('subprojects', data);

					if (result) {
						uni.showToast({
							title: '子项目创建成功',
							icon: 'success'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else {
						uni.showToast({
							title: '创建失败',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('保存子项目失败', e);
					uni.showToast({
						title: '保存失败: ' + (e.message || e),
						icon: 'none'
					});
				} finally {
					this.isSaving = false;
				}
			}
		}
	}
</script>

<style>
	@import '@/static/styles/form.css';

	/* 页面特定样式 */
	.form-container {
		height: 0; /* 重要：让scroll-view能够正确计算高度 */
	}

	.save-button {
		flex: 2;
	}
</style>
