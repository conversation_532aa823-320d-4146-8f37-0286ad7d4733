/**
 * 表单工具类
 * 统一管理表单相关的通用逻辑
 */

import { TableUtils } from '@/config/tables.js';

export const FormUtils = {
  // 获取输入框占位符
  getPlaceholder(column) {
    if (!column) return '请输入';
    if (column.is_primary_key === 1 && column.type === 'INTEGER') {
      return '自动生成';
    }
    const displayName = TableUtils.getColumnDisplayName(column.name);
    return `请输入${displayName}`;
  },

  // 判断输入框是否禁用
  isDisabled(column) {
    if (!column) return false;
    return column.is_primary_key === 1 && column.type === 'INTEGER';
  },

  // 获取输入框类型
  getInputType(column) {
    if (!column) return 'text';
    switch (column.type) {
      case 'INTEGER': return 'number';
      case 'REAL': return 'digit';
      default: return 'text';
    }
  },

  // 获取列的选项
  getColumnOptions(column) {
    try {
      if (column && column.options) {
        let options;
        if (typeof column.options === 'string') {
          options = JSON.parse(column.options);
        } else if (Array.isArray(column.options)) {
          options = column.options;
        }
        return Array.isArray(options) ? options : [];
      }
    } catch (e) {
      console.error('解析列选项失败', e, column);
    }
    return [];
  },

  // 初始化表单数据
  initFormData(columns, rowData = null) {
    const formData = {};
    
    for (const column of columns) {
      // 跳过无效列或自增主键
      if (!column || !column.name || (column.is_primary_key === 1 && column.type === 'INTEGER')) {
        continue;
      }

      // 如果有行数据，使用行数据初始化
      if (rowData && rowData[column.name] !== undefined) {
        formData[column.name] = rowData[column.name];
      } else {
        formData[column.name] = '';
      }
    }

    return formData;
  },

  // 验证表单数据
  validateForm(columns, formData, isHiddenFn) {
    const errors = {};
    let isValid = true;

    for (const column of columns) {
      // 跳过无效列或自增主键或隐藏字段
      if (!column || !column.name ||
          (column.is_primary_key === 1 && column.type === 'INTEGER') ||
          (isHiddenFn && isHiddenFn(column))) {
        continue;
      }

      const value = formData[column.name];

      // 检查必填字段
      if (column.is_not_null === 1) {
        if (!value || (typeof value === 'object' && !value.value)) {
          const displayName = TableUtils.getColumnDisplayName(column.name);
          errors[column.name] = `${displayName} 不能为空`;
          isValid = false;
          continue;
        }
      }

      // 检查数据类型
      if (value) {
        const actualValue = typeof value === 'object' ? value.value : value;

        if (column.type === 'INTEGER' && !/^-?\d+$/.test(actualValue)) {
          const displayName = TableUtils.getColumnDisplayName(column.name);
          errors[column.name] = `${displayName} 必须是整数`;
          isValid = false;
        } else if (column.type === 'REAL' && !/^-?\d+(\.\d+)?$/.test(actualValue)) {
          const displayName = TableUtils.getColumnDisplayName(column.name);
          errors[column.name] = `${displayName} 必须是数字`;
          isValid = false;
        }
      }
    }

    return { isValid, errors };
  },

  // 准备保存数据
  prepareSaveData(columns, formData, isHiddenFn) {
    const data = {};

    for (const column of columns) {
      // 跳过无效列或自增主键
      if (!column || !column.name ||
          (column.is_primary_key === 1 && column.type === 'INTEGER')) {
        continue;
      }

      // 对于隐藏字段，只跳过那些不在formData中的字段
      if (isHiddenFn && isHiddenFn(column) && !(column.name in formData)) {
        continue;
      }

      const value = formData[column.name];

      // 处理外键值
      if (typeof value === 'object' && value.value) {
        data[column.name] = value.value;
      } else if (value || value === 0 || value === '') {
        // 确保空字符串和0也能被保存
        data[column.name] = value;
      } else if (column.is_not_null === 1) {
        // 如果是必填字段但没有值，设置为空字符串
        data[column.name] = '';
      }
    }

    return data;
  },

  // 输入框获得焦点时的处理（用于解决输入法遮挡问题）
  onInputFocus() {
    // 延迟一点时间，等待输入法弹出
    setTimeout(() => {
      // 获取当前输入框在页面中的位置
      const query = uni.createSelectorQuery();
      query.select('.form-container').boundingClientRect((rect) => {
        if (rect) {
          // 计算需要滚动的距离，确保输入框在可视区域内
          const systemInfo = uni.getSystemInfoSync();
          const screenHeight = systemInfo.windowHeight;
          
          // 假设输入法高度约为屏幕高度的40%
          const keyboardHeight = screenHeight * 0.4;
          const visibleHeight = screenHeight - keyboardHeight;
          
          // 滚动到输入框位置，确保有足够空间显示
          const targetScrollTop = Math.max(0, rect.height - visibleHeight + 200);
          
          // 使用页面滚动
          uni.pageScrollTo({
            scrollTop: targetScrollTop,
            duration: 300
          });
        }
      }).exec();
    }, 300);
  }
};
