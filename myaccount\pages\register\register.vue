<template>
  <view class="container">
    <view class="header">
      <view class="header-content">
        <image src="/static/logo.jpg" mode="aspectFit" class="logo"></image>
        <text class="title">账户注册</text>
      </view>
    </view>

    <view class="form-container">
      <view class="form-content">
        <view class="form-item">
          <text class="label">用户名</text>
          <input 
            type="text" 
            v-model="username" 
            placeholder="请输入用户名（至少3个字符）"
            class="input-field"
            @blur="validateUsername"
          />
          <text v-if="errors.username" class="error-text">{{ errors.username }}</text>
        </view>
        
        <view class="form-item">
          <text class="label">密码</text>
          <input 
            type="password" 
            v-model="password" 
            placeholder="请输入密码（至少6个字符）"
            class="input-field"
            @blur="validatePassword"
          />
          <text v-if="errors.password" class="error-text">{{ errors.password }}</text>
        </view>

        <view class="form-item">
          <text class="label">确认密码</text>
          <input 
            type="password" 
            v-model="confirmPassword" 
            placeholder="请再次输入密码"
            class="input-field"
            @blur="validateConfirmPassword"
          />
          <text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
        </view>

        <view class="form-item">
          <text class="label">邮箱</text>
          <input 
            type="text" 
            v-model="email" 
            placeholder="请输入有效的邮箱地址"
            class="input-field"
            @blur="validateEmail"
          />
          <text v-if="errors.email" class="error-text">{{ errors.email }}</text>
        </view>

        <button 
          class="submit-button" 
          @click="handleRegister"
          :disabled="isLoading || hasErrors"
        >
          {{ isLoading ? '注册中...' : '注册' }}
        </button>

        <view class="login-link" @click="goToLogin">
          已有账号？点击登录
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'

export default {
  setup() {
    const store = useStore()
    const username = ref('')
    const password = ref('')
    const confirmPassword = ref('')
    const email = ref('')
    const isLoading = ref(false)
    const errors = ref({
      username: '',
      password: '',
      confirmPassword: '',
      email: ''
    })

    const validateUsername = () => {
      if (!username.value) {
        errors.value.username = '用户名不能为空'
      } else if (username.value.length < 3) {
        errors.value.username = '用户名至少需要3个字符'
      } else {
        errors.value.username = ''
      }
    }

    const validatePassword = () => {
      if (!password.value) {
        errors.value.password = '密码不能为空'
      } else if (password.value.length < 6) {
        errors.value.password = '密码至少需要6个字符'
      } else {
        errors.value.password = ''
      }
      // 当密码改变时，同时验证确认密码
      if (confirmPassword.value) {
        validateConfirmPassword()
      }
    }

    const validateConfirmPassword = () => {
      if (!confirmPassword.value) {
        errors.value.confirmPassword = '请确认密码'
      } else if (confirmPassword.value !== password.value) {
        errors.value.confirmPassword = '两次输入的密码不一致'
      } else {
        errors.value.confirmPassword = ''
      }
    }

    const validateEmail = () => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!email.value) {
        errors.value.email = '邮箱不能为空'
      } else if (!emailRegex.test(email.value)) {
        errors.value.email = '请输入有效的邮箱地址'
      } else {
        errors.value.email = ''
      }
    }

    const hasErrors = computed(() => {
      return Object.values(errors.value).some(error => error !== '') ||
             !username.value ||
             !password.value ||
             !confirmPassword.value ||
             !email.value
    })

    const validateAll = () => {
      validateUsername()
      validatePassword()
      validateConfirmPassword()
      validateEmail()
      return !hasErrors.value
    }

    const handleRegister = async () => {
      if (!validateAll()) {
        return
      }

      isLoading.value = true
      try {
        await store.dispatch('register', {
          username: username.value,
          password: password.value,
          email: email.value
        })
        
        uni.showToast({
          title: '注册成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/login/login'
          })
        }, 1500)
      } catch (error) {
        console.error('注册失败，完整错误:', error)
        let errorMessage = '注册失败'
        
        // 处理具体的错误类型
        if (error.message.includes('用户名已存在')) {
          errorMessage = '用户名已被注册'
          errors.value.username = errorMessage
        } else if (error.message.includes('邮箱已存在')) {
          errorMessage = '邮箱已被注册'
          errors.value.email = errorMessage
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        })
      } finally {
        isLoading.value = false
      }
    }

    const goToLogin = () => {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    }

    return {
      username,
      password,
      confirmPassword,
      email,
      isLoading,
      errors,
      hasErrors,
      handleRegister,
      goToLogin,
      validateUsername,
      validatePassword,
      validateConfirmPassword,
      validateEmail
    }
  }
}
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0;
  box-sizing: border-box;
}

.header {
  background: #4a90e2;
  padding: 30px 16px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 30px;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-top: 15px;
  }

  .logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    margin-right: 12px;
  }

  .title {
    color: white;
    font-size: 20px;
    font-weight: 500;
  }
}

.form-container {
  flex: 1;
  padding: 0 16px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.form-content {
  width: 100%;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.form-item {
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
  
  .label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
  }

  .input-field {
    width: 100%;
    height: 40px;
    padding: 0 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;

    &:focus {
      border-color: #4a90e2;
      outline: none;
      box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
    }
  }

  .error-text {
    display: block;
    margin-top: 4px;
    color: #ff4d4f;
    font-size: 12px;
  }
}

.submit-button {
  width: 100%;
  height: 40px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  margin-top: 24px;
  transition: background-color 0.3s ease;

  &:hover {
    background: #357abd;
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
}

.login-link {
  text-align: center;
  margin-top: 16px;
  color: #4a90e2;
  font-size: 14px;
  cursor: pointer;
  
  &:hover {
    text-decoration: underline;
  }
}
</style> 