/**
 * 应用配置文件
 */

// 应用信息
export const APP_INFO = {
  // 应用名称
  name: 'SQLite数据库管理器',
  // 应用版本
  version: '1.1.0',
  // 应用描述
  description: '一个简单易用的SQLite数据库管理工具，可以创建表、定义列、管理数据，支持编辑和删除数据，调整列顺序，以及数据的搜索、筛选、批量操作和导入导出功能',
  // 应用作者
  author: 'SQLite数据库管理器团队',
  // 应用主题色
  themeColor: '#007AFF'
};

// 数据库配置
export const DB_CONFIG = {
  // 数据库名称
  name: 'sqlite_manager.db',
  // 数据库路径
  path: '_doc/sqlite_manager.db',
  // 系统表名
  systemTables: {
    tables: 'sys_tables',
    columns: 'sys_columns'
  }
};

// 界面配置
export const UI_CONFIG = {
  // 长按触发时间（毫秒）
  longPressThreshold: 800,
  // 表格每页显示的数据条数
  tablePageSize: 50,
  // 表格最大高度
  tableMaxHeight: 800,
  // 搜索防抖时间（毫秒）
  searchDebounceTime: 300
};

// 数据类型配置
export const DATA_TYPES = [
  {
    label: '文本',
    value: 'TEXT',
    description: '用于存储字符串，可以是字母、数字、符号等'
  },
  {
    label: '整数',
    value: 'INTEGER',
    description: '用于存储整数，如1, 2, 3等'
  },
  {
    label: '小数',
    value: 'REAL',
    description: '用于存储浮点数，如1.5, 3.14等'
  },
  {
    label: '二进制',
    value: 'BLOB',
    description: '用于存储二进制数据，如图片、文件等'
  }
];

// 导出格式配置
export const EXPORT_FORMATS = [
  {
    label: 'CSV',
    value: 'csv',
    mimeType: 'text/csv',
    extension: '.csv',
    description: '逗号分隔值，可用Excel打开'
  },
  {
    label: 'JSON',
    value: 'json',
    mimeType: 'application/json',
    extension: '.json',
    description: 'JavaScript对象表示法'
  }
];

// 筛选操作符配置
export const FILTER_OPERATORS = {
  // 文本类型操作符
  TEXT: [
    { label: '等于', value: '=' },
    { label: '不等于', value: '!=' },
    { label: '包含', value: '包含' },
    { label: '不包含', value: '不包含' },
    { label: '开头是', value: '开头是' },
    { label: '结尾是', value: '结尾是' }
  ],
  // 数值类型操作符
  NUMERIC: [
    { label: '等于', value: '=' },
    { label: '不等于', value: '!=' },
    { label: '大于', value: '>' },
    { label: '小于', value: '<' },
    { label: '大于等于', value: '>=' },
    { label: '小于等于', value: '<=' }
  ],
  // 通用操作符
  COMMON: [
    { label: '等于', value: '=' },
    { label: '不等于', value: '!=' }
  ]
};

// 帮助信息
export const HELP_INFO = {
  title: '应用帮助',
  content:
    'SQLite数据库管理器使用说明：\n\n' +
    '1. 点击右下角"+"按钮创建新表\n' +
    '2. 点击表格项目查看表详情\n' +
    '3. 在表详情页可以：\n' +
    '   - 查看列定义\n' +
    '   - 长按列调整顺序\n' +
    '   - 查看和管理数据\n' +
    '   - 长按数据行编辑或删除\n' +
    '4. 点击表详情页右上角"操作"可以：\n' +
    '   - 编辑表结构（添加/删除列）\n' +
    '   - 删除表\n' +
    '5. 数据管理功能：\n' +
    '   - 搜索：在搜索框输入关键字\n' +
    '   - 筛选：点击筛选按钮设置条件\n' +
    '   - 批量：点击批量按钮进行多选操作\n' +
    '   - 导入：支持CSV、JSON格式\n' +
    '   - 导出：支持CSV、JSON格式'
};

// 默认配置
export default {
  APP_INFO,
  DB_CONFIG,
  UI_CONFIG,
  DATA_TYPES,
  EXPORT_FORMATS,
  FILTER_OPERATORS,
  HELP_INFO
};
