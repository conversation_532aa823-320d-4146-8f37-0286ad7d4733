
.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 0.78125rem 0.9375rem 0.9375rem 0.9375rem; /* 适中的上下内边距 */
		padding-top: calc(var(--status-bar-height) + 0.46875rem); /* 适中的顶部空间 */
		min-height: 3.125rem; /* 适中的最小高度 */
}
.header-left, .header-right {
		width: 3.75rem;
		align-self: flex-start; /* 让左右按钮对齐到顶部 */
		margin-top: 0.3125rem;
}
.header-center {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
		max-width: 12.5rem;
}
.header-back {
		color: #FFFFFF;
		font-size: 0.875rem;
}
.header-title {
		color: #FFFFFF;
		font-size: 1.125rem;
		font-weight: bold;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-bottom: 0.25rem; /* 与中文标题的间距 */
}
.header-subtitle {
		color: #FFFFFF;
		font-size: 0.875rem;
		font-weight: normal;
		opacity: 0.9; /* 稍微透明，区分主标题 */
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
}
.form-container {
		flex: 1;
		height: 0; /* 重要：让scroll-view能够正确计算高度 */
}
.form-content {
		padding: 0.9375rem;
		padding-bottom: 9.375rem; /* 大幅增加底部空间，确保最后的输入框能完全显示 */
}
.empty-tip {
		text-align: center;
		padding: 3.125rem 0;
		color: #999999;
		font-size: 0.875rem;
}
.form-item {
		margin-bottom: 1.25rem;
}
.form-label {
		display: block;
		font-size: 0.875rem;
		color: #333333;
		margin-bottom: 0.625rem;
		font-weight: 500;
}
.input-container {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		border: 0.0625rem solid #E5E5E5;
		overflow: hidden;
}
.form-input {
		width: 100%;
		padding: 0.75rem 0.625rem;
		font-size: 0.875rem;
		color: #333333;
		background-color: transparent;
		border: none;
		outline: none;
}
.form-textarea {
		width: 100%;
		padding: 0.75rem 0.625rem;
		font-size: 0.875rem;
		color: #333333;
		background-color: transparent;
		border: none;
		outline: none;
		min-height: 3.75rem;
		resize: none;
}
.picker-view {
		padding: 0.75rem 0.625rem;
		font-size: 0.875rem;
		color: #333333;
		background-color: #FFFFFF;
}
.picker-placeholder {
		color: #999999;
}
.form-error {
		display: block;
		color: #FF3B30;
		font-size: 0.75rem;
		margin-top: 0.3125rem;
}
.form-hint {
		display: block;
		color: #666666;
		font-size: 0.75rem;
		margin-top: 0.3125rem;
		font-style: italic;
}
.button-group {
		display: flex;
		gap: 0.625rem;
		margin-top: 1.875rem;
		padding-bottom: 1.25rem;
}
.cancel-button {
		flex: 1;
		height: 2.75rem;
		background-color: #F2F2F7;
		color: #666666;
		border: none;
		border-radius: 0.25rem;
		font-size: 1rem;
		font-weight: 500;
}
.save-button {
		flex: 2;
		height: 2.75rem;
		background-color: #007AFF;
		color: #FFFFFF;
		border: none;
		border-radius: 0.25rem;
		font-size: 1rem;
		font-weight: 500;
}
.save-button:disabled {
		background-color: #CCCCCC;
		color: #999999;
}
