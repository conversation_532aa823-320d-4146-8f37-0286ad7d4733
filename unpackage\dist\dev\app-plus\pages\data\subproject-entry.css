
.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 0.625rem 0.9375rem;
		padding-top: var(--status-bar-height);
}
.header-left, .header-right {
		width: 3.75rem;
}
.header-back {
		color: #FFFFFF;
		font-size: 0.875rem;
}
.header-title {
		color: #FFFFFF;
		font-size: 1.125rem;
		font-weight: bold;
}
.form-container {
		flex: 1;
		height: 0; /* 重要：让scroll-view能够正确计算高度 */
}
.form-content {
		padding: 0.9375rem;
		padding-bottom: 3.125rem; /* 增加底部空间，避免输入法遮挡 */
}
.empty-tip {
		text-align: center;
		padding: 3.125rem 0;
		color: #999999;
		font-size: 0.875rem;
}
.form-item {
		margin-bottom: 1.25rem;
}
.form-label {
		display: block;
		font-size: 0.875rem;
		color: #333333;
		margin-bottom: 0.625rem;
		font-weight: 500;
}
.input-container {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		border: 0.0625rem solid #E5E5E5;
		overflow: hidden;
}
.form-input {
		width: 100%;
		padding: 0.75rem 0.625rem;
		font-size: 0.875rem;
		color: #333333;
		background-color: transparent;
		border: none;
		outline: none;
}
.form-textarea {
		width: 100%;
		padding: 0.75rem 0.625rem;
		font-size: 0.875rem;
		color: #333333;
		background-color: transparent;
		border: none;
		outline: none;
		min-height: 3.75rem;
		resize: none;
}
.picker-view {
		padding: 0.75rem 0.625rem;
		font-size: 0.875rem;
		color: #333333;
		background-color: #FFFFFF;
}
.picker-placeholder {
		color: #999999;
}
.form-error {
		display: block;
		color: #FF3B30;
		font-size: 0.75rem;
		margin-top: 0.3125rem;
}
.form-hint {
		display: block;
		color: #666666;
		font-size: 0.75rem;
		margin-top: 0.3125rem;
		font-style: italic;
}
.button-group {
		display: flex;
		gap: 0.625rem;
		margin-top: 1.875rem;
		padding-bottom: 1.25rem;
}
.cancel-button {
		flex: 1;
		height: 2.75rem;
		background-color: #F2F2F7;
		color: #666666;
		border: none;
		border-radius: 0.25rem;
		font-size: 1rem;
		font-weight: 500;
}
.save-button {
		flex: 2;
		height: 2.75rem;
		background-color: #007AFF;
		color: #FFFFFF;
		border: none;
		border-radius: 0.25rem;
		font-size: 1rem;
		font-weight: 500;
}
.save-button:disabled {
		background-color: #CCCCCC;
		color: #999999;
}
