<template>
  <view class="container">
    <view class="header">
      <view class="header-content">
        <text class="title">联系我们</text>
      </view>
    </view>

    <view class="contact-list">
      <view class="contact-item">
        <text class="contact-label">QQ邮箱</text>
        <text class="contact-value" @tap="copyEmail"><EMAIL></text>
      </view>

      <view class="contact-item">
        <text class="contact-label">微信</text>
        <image src="/static/weixin.png" mode="aspectFit" class="qr-code" @tap="previewImage"></image>
      </view>

      <view class="contact-item">
        <text class="contact-label">使用说明</text>
        <view class="user-guide">
          <view class="guide-section">
            <text class="guide-title">应用简介</text>
            <text class="guide-content">本应用是一个完全本地化的记账应用，所有数据存储在您的手机上，不会上传到云端，保证您的数据安全和隐私。</text>
          </view>

          <view class="guide-section">
            <text class="guide-title">主要功能</text>
            <view class="guide-list">
              <text class="guide-item">1. 支出记录：在首页输入支出金额和标的，可输入负数对冲错误记录</text>
              <text class="guide-item">2. 智能输入：点击“智能输入”按钮，输入自然语言描述的消费记录</text>
              <text class="guide-item">3. 明细查询：在明细页面查看所有支出记录，可按日期和分类筛选</text>
              <text class="guide-item">4. 分类管理：在设置页面中管理自定义分类</text>
              <text class="guide-item">5. 数据备份：在设置页面中备份数据到手机存储</text>
            </view>
          </view>

          <view class="guide-section">
            <text class="guide-title">注意事项</text>
            <view class="guide-list">
              <text class="guide-item">1. 定期备份数据，防止意外丢失</text>
              <text class="guide-item">2. 清空数据前会自动备份，但仍请谨慎操作</text>
              <text class="guide-item">3. 如需导入备份数据，请联系开发者</text>
              <text class="guide-item">4. 如忘记密码，可在登录页面点击“忘记密码”，通过注册邮箱重置</text>
              <text class="guide-item">5. 如发现问题或有功能建议，请通过上方联系方式反馈</text>
            </view>
          </view>

          <view class="guide-section">
            <text class="guide-title">隐私声明</text>
            <text class="guide-content">本应用不会收集或上传您的个人数据，所有数据仅存储在您的设备上。智能输入功能会将您输入的文本发送到AI服务器进行处理，但不会存储您的数据。</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  setup() {
    const copyEmail = () => {
      uni.setClipboardData({
        data: '<EMAIL>',
        success: () => {
          uni.showToast({
            title: '邮箱已复制',
            icon: 'success'
          })
        }
      })
    }

    const previewImage = () => {
      uni.previewImage({
        urls: ['/static/weixin.png']
      })
    }

    return {
      copyEmail,
      previewImage
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: #4a90e2;
  padding: 30px 16px;
  color: white;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-top: 15px;
  }

  .title {
    color: white;
    font-size: 20px;
    font-weight: 500;
  }
}

.contact-list {
  margin: 12px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.contact-item {
  padding: 16px;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }

  .contact-label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .contact-value {
    font-size: 16px;
    color: #4a90e2;
  }

  .qr-code {
    width: 200px;
    height: 200px;
    display: block;
    margin: 0 auto;
  }

  .user-guide {
    margin-top: 10px;
  }

  .guide-section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .guide-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    display: block;
  }

  .guide-content {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    display: block;
  }

  .guide-list {
    display: flex;
    flex-direction: column;
  }

  .guide-item {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 6px;
    padding-left: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>