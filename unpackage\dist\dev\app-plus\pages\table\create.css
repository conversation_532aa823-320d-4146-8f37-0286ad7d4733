
.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 0.625rem 0.9375rem;
		padding-top: var(--status-bar-height);
}
.header-left, .header-right {
		width: 3.75rem;
}
.header-back {
		color: #FFFFFF;
		font-size: 0.875rem;
}
.header-title {
		color: #FFFFFF;
		font-size: 1.125rem;
		font-weight: bold;
}
.form-container {
		flex: 1;
		padding: 0.9375rem;
}
.form-item {
		margin-bottom: 0.9375rem;
}
.input-container {
		width: 100%;
		border: 1px solid #DDDDDD;
		border-radius: 0.25rem;
		background-color: #FFFFFF;
		padding: 0;
		margin: 0;
}
.form-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 0.3125rem;
}
.form-label {
		font-size: 1rem;
		font-weight: bold;
		margin-bottom: 0.3125rem;
}
.form-action {
		background-color: #007AFF;
		padding: 0.3125rem 0.625rem;
		border-radius: 0.1875rem;
}
.form-action-text {
		color: #FFFFFF;
		font-size: 0.75rem;
}
.form-input, .form-textarea {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem;
		font-size: 0.875rem;
		width: 100%;
		min-width: 15.625rem;
		min-height: 2.5rem;
		box-sizing: border-box;
		margin: 0;
		border: none;
}
.form-textarea {
		height: 5rem;
		min-height: 5rem;
}
.form-error {
		color: #FF0000;
		font-size: 0.75rem;
		margin-top: 0.3125rem;
}
.empty-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 6.25rem;
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		color: #999999;
		font-size: 0.875rem;
}
.column-list {
		display: flex;
		flex-direction: column;
		gap: 0.625rem;
}
.column-item {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem;
}
.column-item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 0.3125rem;
}
.column-name {
		font-size: 0.9375rem;
		font-weight: bold;
}
.column-actions {
		display: flex;
		gap: 0.3125rem;
}
.column-action {
		background-color: #FF3B30;
		padding: 0.1875rem 0.375rem;
		border-radius: 0.125rem;
}
.column-action-text {
		color: #FFFFFF;
		font-size: 0.6875rem;
}
.column-details {
		display: flex;
		flex-direction: column;
		gap: 0.1875rem;
}
.column-type {
		font-size: 0.8125rem;
		color: #333333;
}
.column-attributes {
		display: flex;
		flex-wrap: wrap;
		gap: 0.3125rem;
}
.column-attribute {
		background-color: #F0F0F0;
		padding: 0.125rem 0.375rem;
		border-radius: 0.125rem;
		font-size: 0.6875rem;
		color: #666666;
}
.column-foreign-key {
		font-size: 0.6875rem;
		color: #666666;
		font-style: italic;
		margin-top: 0.1875rem;
}
.save-button {
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 1rem;
		padding: 0.625rem;
		border-radius: 0.25rem;
		margin-top: 0.9375rem;
}
