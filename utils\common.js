/**
 * 通用工具函数
 */

/**
 * 格式化日期时间
 * @param {Date|number|string} date 日期对象、时间戳或日期字符串
 * @param {string} format 格式化模板，如 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';

  // 转换为Date对象
  let dateObj;
  if (typeof date === 'string') {
    dateObj = new Date(date.replace(/-/g, '/'));
  } else if (typeof date === 'number') {
    dateObj = new Date(date);
  } else {
    dateObj = date;
  }

  if (!(dateObj instanceof Date) || isNaN(dateObj)) {
    return '';
  }

  const year = dateObj.getFullYear();
  const month = dateObj.getMonth() + 1;
  const day = dateObj.getDate();
  const hours = dateObj.getHours();
  const minutes = dateObj.getMinutes();
  const seconds = dateObj.getSeconds();

  const o = {
    'YYYY': year,
    'MM': padZero(month),
    'M': month,
    'DD': padZero(day),
    'D': day,
    'HH': padZero(hours),
    'H': hours,
    'mm': padZero(minutes),
    'm': minutes,
    'ss': padZero(seconds),
    's': seconds
  };

  return format.replace(/(YYYY|MM|M|DD|D|HH|H|mm|m|ss|s)/g, match => o[match]);
}

/**
 * 数字补零
 * @param {number} num 数字
 * @returns {string} 补零后的字符串
 */
export function padZero(num) {
  return num < 10 ? '0' + num : String(num);
}

/**
 * 生成UUID
 * @returns {string} UUID
 */
export function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 深拷贝对象
 * @param {*} obj 要拷贝的对象
 * @returns {*} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj);
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }

  if (obj instanceof Object) {
    const copy = {};
    Object.keys(obj).forEach(key => {
      copy[key] = deepClone(obj[key]);
    });
    return copy;
  }

  return obj;
}

/**
 * 防抖函数
 * @param {Function} func 要执行的函数
 * @param {number} wait 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 300) {
  let timeout;
  return function(...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(this, args);
    }, wait);
  };
}

/**
 * 节流函数
 * @param {Function} func 要执行的函数
 * @param {number} wait 等待时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, wait = 300) {
  let timeout = null;
  let previous = 0;

  return function(...args) {
    const now = Date.now();
    const remaining = wait - (now - previous);

    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = now;
      func.apply(this, args);
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now();
        timeout = null;
        func.apply(this, args);
      }, remaining);
    }
  };
}

/**
 * 格式化文件大小
 * @param {number} size 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(size) {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
}

/**
 * 检查对象是否为空
 * @param {Object} obj 要检查的对象
 * @returns {boolean} 是否为空
 */
export function isEmptyObject(obj) {
  if (!obj) return true;
  return Object.keys(obj).length === 0;
}

/**
 * 获取URL参数
 * @param {string} url URL字符串
 * @returns {Object} 参数对象
 */
export function getUrlParams(url) {
  const params = {};
  const search = url.split('?')[1];

  if (!search) return params;

  search.split('&').forEach(item => {
    const [key, value] = item.split('=');
    params[key] = decodeURIComponent(value);
  });

  return params;
}

/**
 * 显示加载提示
 * @param {string} title 提示文字
 * @param {boolean} mask 是否显示遮罩
 */
export function showLoading(title = '加载中...', mask = true) {
  uni.showLoading({
    title,
    mask
  });
}

/**
 * 隐藏加载提示
 * 添加延迟和多次调用，确保所有加载提示都被隐藏
 */
export function hideLoading() {
  // 立即隐藏一次
  uni.hideLoading();

  // 延迟 100ms 再隐藏一次，确保所有加载提示都被隐藏
  setTimeout(() => {
    uni.hideLoading();
  }, 100);

  // 延迟 300ms 再隐藏一次，以防万一
  setTimeout(() => {
    uni.hideLoading();
  }, 300);
}

/**
 * 显示提示信息
 * @param {string} title 提示文字
 * @param {string} icon 图标类型
 * @param {number} duration 显示时间（毫秒）
 */
export function showToast(title, icon = 'none', duration = 2000) {
  uni.showToast({
    title,
    icon,
    duration
  });
}

/**
 * 显示确认对话框
 * @param {string} title 标题
 * @param {string} content 内容
 * @param {string} confirmText 确认按钮文字
 * @param {string} cancelText 取消按钮文字
 * @returns {Promise<boolean>} 是否确认
 */
export function showConfirm(title, content, confirmText = '确定', cancelText = '取消') {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      confirmText,
      cancelText,
      success(res) {
        resolve(res.confirm);
      }
    });
  });
}

/**
 * 检查数据类型
 * @param {*} value 要检查的值
 * @param {string} type 类型
 * @returns {boolean} 是否匹配
 */
export function checkType(value, type) {
  const typeMap = {
    'string': val => typeof val === 'string',
    'number': val => typeof val === 'number' && !isNaN(val),
    'boolean': val => typeof val === 'boolean',
    'object': val => val !== null && typeof val === 'object' && !Array.isArray(val),
    'array': val => Array.isArray(val),
    'function': val => typeof val === 'function',
    'date': val => val instanceof Date && !isNaN(val),
    'null': val => val === null,
    'undefined': val => val === undefined
  };

  return typeMap[type.toLowerCase()] ? typeMap[type.toLowerCase()](value) : false;
}

/**
 * 随机生成指定范围内的整数
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 随机整数
 */
export function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 获取当前时间戳
 * @returns {number} 时间戳
 */
export function getTimestamp() {
  return Date.now();
}

/**
 * 检查是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
export function isMobile() {
  const systemInfo = uni.getSystemInfoSync();
  return systemInfo.platform === 'android' || systemInfo.platform === 'ios';
}

/**
 * 获取应用版本号
 * @returns {string} 版本号
 */
export function getAppVersion() {
  const systemInfo = uni.getSystemInfoSync();
  return systemInfo.appVersion || '1.0.0';
}
