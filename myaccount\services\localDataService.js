import { dbService } from '../utils/db'
import { v4 as uuidv4 } from 'uuid'
import bcrypt from 'bcryptjs'

class LocalDataService {
    // 用户注册
    async register(username, password, email) {
        try {
            if (!username || !password || !email) {
                throw new Error('用户名、密码和邮箱都不能为空')
            }

            // 检查用户是否已存在
            const existingUser = await dbService.selectSql(
                'SELECT * FROM users WHERE username = ? OR email = ?',
                [username, email]
            )

            // 如果用户已存在，先删除
            if (existingUser && existingUser.length > 0) {
                console.log('用户已存在，准备删除:', existingUser)
                await dbService.executeSql(
                    'DELETE FROM users WHERE username = ? OR email = ?',
                    [username, email]
                )
                console.log('已删除现有用户')
            }

            const userId = uuidv4().replace(/-/g, '').substr(0, 13)
            console.log('生成的用户ID:', userId)

            // 密码加密
            let hashedPassword
            try {
                const salt = bcrypt.genSaltSync(10)
                hashedPassword = bcrypt.hashSync(password, salt)
                console.log('密码加密成功:', !!hashedPassword)
            } catch (error) {
                console.error('密码加密失败:', error)
                throw new Error('密码处理失败，请重试')
            }

            if (!hashedPassword) {
                throw new Error('密码处理失败，请重试')
            }

            console.log('准备插入数据:', {
                userId,
                username,
                passwordLength: hashedPassword ? hashedPassword.length : 0,
                email
            })

            try {
                await dbService.executeSql(
                    'INSERT INTO users (user_id, username, password, email) VALUES (?, ?, ?, ?)',
                    [userId, username, hashedPassword, email]
                )
                console.log('数据插入成功')
            } catch (error) {
                console.error('数据插入失败，详细信息:', {
                    error: JSON.stringify(error, null, 2),
                    userId,
                    username,
                    hashedPassword: hashedPassword ? '已加密' : null,
                    email
                })
                throw error
            }

            return {
                user_id: userId,
                username,
                email
            }
        } catch (error) {
            console.error('注册失败:', error)
            throw error
        }
    }

    // 用户登录
    async login(username, password) {
        try {
            console.log('开始登录流程:', { username })

            // 确保数据库已打开
            try {
                await dbService.openDatabase()
                console.log('数据库连接已确认')
            } catch (error) {
                console.error('数据库连接失败:', error)
                throw new Error('数据库连接失败，请重试')
            }

            // 直接查询指定用户名的用户
            let users
            try {
                users = await dbService.selectSql(
                    'SELECT * FROM users WHERE username = ?',
                    [username]
                )
                console.log('用户查询结果:', users)
            } catch (error) {
                console.error('用户查询失败:', error)
                throw new Error('用户查询失败，请重试')
            }

            if (!users || users.length === 0) {
                console.log('未找到用户:', username)
                throw new Error('用户不存在')
            }

            const user = users[0]
            console.log('找到用户:', {
                userId: user.user_id,
                username: user.username,
                hasPassword: !!user.password,
                passwordLength: user.password ? user.password.length : 0
            })

            // 验证密码
            let isValid = false
            try {
                isValid = bcrypt.compareSync(password, user.password)
                console.log('密码验证结果:', isValid)
            } catch (error) {
                console.error('密码验证出错:', error)
                throw new Error('密码验证失败，请重试')
            }

            if (!isValid) {
                throw new Error('密码错误')
            }

            // 检查用户是否已有分类
            let categories
            try {
                categories = await dbService.selectSql(
                    'SELECT * FROM categories WHERE user_id = ?',
                    [user.user_id]
                )
                console.log('用户分类查询结果:', categories)
            } catch (error) {
                console.error('分类查询失败:', error)
                // 不中断登录流程，继续执行
            }

            // 如果没有分类，创建默认分类
            if (!categories || categories.length === 0) {
                try {
                    await this.createDefaultCategories(user.user_id)
                    console.log('默认分类创建成功')
                } catch (error) {
                    console.error('创建默认分类失败:', error)
                    // 不中断登录流程，继续执行
                }
            } else {
                console.log('用户已有分类，跳过创建')
            }

            const userData = {
                user_id: user.user_id,
                username: user.username,
                email: user.email
            }

            console.log('登录成功，返回用户数据:', userData)
            return userData

        } catch (error) {
            console.error('登录失败，完整错误:', error)
            throw error
        }
    }

    // 获取分类列表
    async getCategories(userId) {
        try {
            console.log('开始获取分类，用户ID:', userId)
            const categories = await dbService.selectSql(
                'SELECT * FROM categories WHERE user_id = ? ORDER BY level, sort_order',
                [userId]
            )
            console.log('从数据库获取的原始分类数据:', categories)

            const tree = this.buildCategoryTree(categories)
            console.log('构建的分类树:', tree)

            return tree
        } catch (error) {
            console.error('获取分类失败:', error)
            throw error
        }
    }

    // 保存支出记录
    async saveExpense(userId, date, records) {
        try {
            for (const record of records) {
                const recordId = uuidv4().replace(/-/g, '').substr(0, 13)

                await dbService.executeSql(
                    'INSERT INTO expenses (record_id, user_id, category_id, target, amount, record_date) VALUES (?, ?, ?, ?, ?, ?)',
                    [recordId, userId, record.category_id, record.target, record.amount, date]
                )

                // 更新分类总金额
                await this.updateCategoryAmount(record.category_id, record.amount)
            }

            return { success: true }
        } catch (error) {
            console.error('保存支出失败:', error)
            throw error
        }
    }

    // 修改密码
    async changePassword(userId, oldPassword, newPassword) {
        try {
            if (!oldPassword || !newPassword) {
                throw new Error('旧密码和新密码都不能为空')
            }

            if (newPassword.length < 6) {
                throw new Error('新密码长度不能少于6位')
            }

            // 获取用户信息
            const users = await dbService.selectSql(
                'SELECT * FROM users WHERE user_id = ?',
                [userId]
            )

            if (!users || users.length === 0) {
                throw new Error('用户不存在')
            }

            const user = users[0]

            // 验证旧密码
            const isValid = bcrypt.compareSync(oldPassword, user.password)
            if (!isValid) {
                throw new Error('旧密码错误')
            }

            // 加密新密码
            const salt = bcrypt.genSaltSync(10)
            const hashedPassword = bcrypt.hashSync(newPassword, salt)

            // 更新密码
            await dbService.executeSql(
                'UPDATE users SET password = ? WHERE user_id = ?',
                [hashedPassword, userId]
            )

            return { success: true }
        } catch (error) {
            console.error('修改密码失败:', error)
            throw error
        }
    }

    // 验证用户邮箱（用于密码重置）
    async verifyUserEmail(username, email) {
        try {
            console.log('开始验证用户邮箱:', { username, email })

            // 查询用户
            const users = await dbService.selectSql(
                'SELECT * FROM users WHERE username = ?',
                [username]
            )

            if (!users || users.length === 0) {
                throw new Error('用户不存在')
            }

            const user = users[0]

            // 验证邮箱是否匹配
            if (user.email !== email) {
                throw new Error('邮箱不匹配')
            }

            return {
                success: true,
                user_id: user.user_id
            }
        } catch (error) {
            console.error('验证用户邮箱失败:', error)
            throw error
        }
    }

    // 重置密码（忘记密码时使用）
    async resetPassword(username, email, newPassword) {
        try {
            if (!username || !email || !newPassword) {
                throw new Error('用户名、邮箱和新密码都不能为空')
            }

            if (newPassword.length < 6) {
                throw new Error('新密码长度不能少于6位')
            }

            // 验证用户邮箱
            const verifyResult = await this.verifyUserEmail(username, email)

            if (!verifyResult.success) {
                throw new Error('身份验证失败')
            }

            // 加密新密码
            const salt = bcrypt.genSaltSync(10)
            const hashedPassword = bcrypt.hashSync(newPassword, salt)

            // 更新密码
            await dbService.executeSql(
                'UPDATE users SET password = ? WHERE user_id = ?',
                [hashedPassword, verifyResult.user_id]
            )

            return { success: true }
        } catch (error) {
            console.error('重置密码失败:', error)
            throw error
        }
    }

    // 构建分类树
    buildCategoryTree(categories) {
        try {

            const tree = []
            const map = {}

            // 首先将所有分类放入map
            categories.forEach(category => {
                map[category.category_id] = {
                    ...category,
                    children: [],
                    total_amount: parseFloat(category.total_amount || 0),
                    level: parseInt(category.level) // 确保 level 是数字
                }
            })

            // 构建树形结构
            categories.forEach(category => {
                const node = map[category.category_id]
                if (!category.parent_id) {
                    tree.push(node)
                } else {
                    const parent = map[category.parent_id]
                    if (parent) {
                        parent.children.push(node)
                    } else {
                        console.error('未找到父分类:', category.parent_id, '对应的分类:', category.name)
                    }
                }
            })

            // 按 level 和 sort_order 排序
            const sortCategories = (cats) => {
                if (!Array.isArray(cats)) return cats
                cats.sort((a, b) => {
                    if (a.level !== b.level) {
                        return a.level - b.level
                    }
                    return (a.sort_order || 0) - (b.sort_order || 0)
                })
                cats.forEach(cat => {
                    if (cat.children && cat.children.length > 0) {
                        cat.children = sortCategories(cat.children)
                    }
                })
                return cats
            }

            const sortedTree = sortCategories(tree)

            return sortedTree
        } catch (error) {
            console.error('构建分类树失败:', error)
            throw error
        }
    }

    // 更新分类金额
    async updateCategoryAmount(categoryId, amount) {
        try {
            // 获取当前分类及其所有父分类
            const updateCategory = async (id) => {
                const category = await dbService.selectSql(
                    'SELECT * FROM categories WHERE category_id = ?',
                    [id]
                )

                if (category && category.length > 0) {
                    await dbService.executeSql(
                        'UPDATE categories SET total_amount = total_amount + ? WHERE category_id = ?',
                        [amount, id]
                    )

                    if (category[0].parent_id) {
                        await updateCategory(category[0].parent_id)
                    }
                }
            }

            await updateCategory(categoryId)
        } catch (error) {
            console.error('更新分类金额失败:', error)
            throw error
        }
    }

    // 创建默认分类
    async createDefaultCategories(userId) {
        try {
            // 先检查是否已存在分类
            const existingCategories = await dbService.selectSql(
                'SELECT category_id FROM categories WHERE user_id = ?',
                [userId]
            )

            if (existingCategories && existingCategories.length > 0) {
                console.log('用户已有分类，跳过创建')
                return
            }

            // 默认分类结构 [显示ID, 父分类显示ID, 名称, 级别, 排序]
            const defaultCategories = [
                // 1. 生存必需类
                ['1', null, '生存必需类', 1, 1],
                ['1.1', '1', '餐饮日用', 2, 1],
                ['1.1.1', '1.1', '食材采购', 3, 1],
                ['1.1.2', '1.1', '外卖外食', 3, 2],
                ['1.1.3', '1.1', '商超购物', 3, 3],
                ['1.2', '1', '交通出行', 2, 2],
                ['1.2.1', '1.2', '公共交通', 3, 1],
                ['1.2.2', '1.2', '油/电/费', 3, 2],
                ['1.2.3', '1.2', '车辆维保', 3, 3],
                ['1.3', '1', '住房相关', 2, 3],
                ['1.3.1', '1.3', '房租/房贷', 3, 1],
                ['1.3.2', '1.3', '水电燃气', 3, 2],
                ['1.3.3', '1.3', '物业费', 3, 3],
                ['1.3.4', '1.3', '维修养护', 3, 4],
                ['1.4', '1', '医疗', 2, 4],
                ['1.4.1', '1.4', '医院医疗', 3, 1],
                ['1.4.2', '1.4', '常规药品', 3, 2],
                ['1.4.3', '1.4', '体检疫苗', 3, 3],
                ['1.5', '1', '父母子女', 2, 5],
                ['1.5.1', '1.5', '父母', 3, 1],
                ['1.5.2', '1.5', '子女教育', 3, 2],
                ['1.5.3', '1.5', '子女消费', 3, 3],

                // 2. 发展提升类
                ['2', null, '发展提升类', 1, 2],
                ['2.1', '2', '教育培训', 2, 1],
                ['2.1.1', '2.1', '课程费用', 3, 1],
                ['2.1.2', '2.1', '书籍资料', 3, 2],
                ['2.1.3', '2.1', '技能提升', 3, 3],
                ['2.2', '2', '职业发展', 2, 2],
                ['2.2.1', '2.2', '专业工具', 3, 1],
                ['2.2.2', '2.2', '公司交费', 3, 2],

                // 3. 品质生活类
                ['3', null, '品质生活类', 1, 3],
                ['3.1', '3', '服饰美容', 2, 1],
                ['3.1.1', '3.1', '服装鞋帽', 3, 1],
                ['3.1.2', '3.1', '护肤美妆', 3, 2],
                ['3.1.3', '3.1', '发型护理', 3, 3],
                ['3.2', '3', '数码电子', 2, 2],
                ['3.2.1', '3.2', '设备购置', 3, 1],
                ['3.2.2', '3.2', '配件耗材', 3, 2],
                ['3.3', '3', '娱乐休闲', 2, 3],
                ['3.3.1', '3.3', '歌舞休闲', 3, 1],
                ['3.3.2', '3.3', '运动健身', 3, 2],
                ['3.3.3', '3.3', '影游演展', 3, 3],
                ['3.3.4', '3.3', '其他娱乐', 3, 4],
                ['3.4', '3', '旅行度假', 2, 4],
                ['3.4.1', '3.4', '交通住宿', 3, 1],
                ['3.4.2', '3.4', '景区消费', 3, 2],

                // 4. 人际往来类
                ['4', null, '人际往来类', 1, 4],
                ['4.1', '4', '社交人情', 2, 1],
                ['4.1.1', '4.1', '节日礼品', 3, 1],
                ['4.1.2', '4.1', '聚餐请客', 3, 2],
                ['4.1.3', '4.1', '红包礼金', 3, 3],
                ['4.2', '4', '宠物养护', 2, 2],
                ['4.2.1', '4.2', '宠物费用', 3, 1],


                // 5. 资产流动类
                ['5', null, '资产流动类', 1, 5],
                ['5.1', '5', '投资理财', 2, 1],
                ['5.1.1', '5.1', '股基理财', 3, 1],
                ['5.1.2', '5.1', '资产投资', 3, 2],
                ['5.1.3', '5.1', '保险费用', 3, 3],
                ['5.2', '5', '债务管理', 2, 2],
                ['5.2.1', '5.2', '贷款还款', 3, 1],
                ['5.2.2', '5.2', '其他借款', 3, 2],

                // 6. 其他支出类
                ['6', null, '其他支出类', 1, 6],
                ['6.1', '6', '其他支出', 2, 1],
                ['6.1.1', '6.1', '罚款税金', 3, 1],
                ['6.1.2', '6.1', '捐赠', 3, 2],
                ['6.1.3', '6.1', '其他支出', 3, 3]

            ]

            console.log('开始创建默认分类，用户ID:', userId)

            // 用于存储分类ID映射（显示ID -> 实际ID）
            const categoryMap = new Map()

            for (const [displayId, parentDisplayId, name, level, sortOrder] of defaultCategories) {
                // 生成唯一的实际分类ID，但保留显示ID的结构
                const uniquePrefix = uuidv4().replace(/-/g, '').substr(0, 8)
                const categoryId = `${uniquePrefix}_${displayId}`
                let parentId = null

                if (parentDisplayId) {
                    parentId = categoryMap.get(parentDisplayId)
                    if (!parentId) {
                        console.error('未找到父分类:', parentDisplayId)
                        continue
                    }
                }

                try {
                    await dbService.executeSql(
                        'INSERT INTO categories (category_id, user_id, parent_id, name, level, sort_order) VALUES (?, ?, ?, ?, ?, ?)',
                        [categoryId, userId, parentId, name, level, sortOrder]
                    )
                    console.log('分类创建成功:', { categoryId, displayId, name })

                    // 保存分类ID映射
                    categoryMap.set(displayId, categoryId)
                } catch (error) {
                    console.error('创建单个分类失败:', {
                        categoryId,
                        displayId,
                        parentId,
                        name,
                        level,
                        sortOrder,
                        error: JSON.stringify(error, null, 2)
                    })
                    throw error
                }
            }

            console.log('所有默认分类创建完成')
        } catch (error) {
            console.error('创建默认分类失败:', error)
            throw error
        }
    }

    // 添加新分类
    async addCategory(userId, name, parentId, level, sortOrder) {
        try {
            // 生成唯一ID
            const categoryId = uuidv4().replace(/-/g, '').substr(0, 13)

            // 插入数据库
            await dbService.executeSql(
                'INSERT INTO categories (category_id, user_id, parent_id, name, level, sort_order) VALUES (?, ?, ?, ?, ?, ?)',
                [categoryId, userId, parentId, name, level, sortOrder]
            )

            // 返回新创建的分类
            return {
                category_id: categoryId,
                user_id: userId,
                parent_id: parentId,
                name,
                level,
                sort_order: sortOrder,
                total_amount: 0,
                children: []
            }
        } catch (error) {
            console.error('添加分类失败:', error)
            throw error
        }
    }

    // 修改分类
    async updateCategory(categoryId, name) {
        try {
            await dbService.executeSql(
                'UPDATE categories SET name = ? WHERE category_id = ?',
                [name, categoryId]
            )
            return { success: true }
        } catch (error) {
            console.error('修改分类失败:', error)
            throw error
        }
    }

    // 删除分类
    async deleteCategory(categoryId) {
        try {
            // 检查该分类是否有子分类
            const children = await dbService.selectSql(
                'SELECT category_id FROM categories WHERE parent_id = ?',
                [categoryId]
            )

            if (children && children.length > 0) {
                throw new Error('该分类下有子分类，无法删除')
            }

            // 检查该分类是否有关联的支出记录
            const expenses = await dbService.selectSql(
                'SELECT record_id FROM expenses WHERE category_id = ?',
                [categoryId]
            )

            if (expenses && expenses.length > 0) {
                throw new Error('该分类下有支出记录，无法删除')
            }

            // 执行删除
            await dbService.executeSql(
                'DELETE FROM categories WHERE category_id = ?',
                [categoryId]
            )

            return { success: true }
        } catch (error) {
            console.error('删除分类失败:', error)
            throw error
        }
    }

    // 更新分类排序
    async updateCategoryOrder(categoryId, newSortOrder) {
        try {
            await dbService.executeSql(
                'UPDATE categories SET sort_order = ? WHERE category_id = ?',
                [newSortOrder, categoryId]
            )
            return { success: true }
        } catch (error) {
            console.error('更新分类排序失败:', error)
            throw error
        }
    }

    // 清空用户的支出记录
    async clearUserExpenses(userId) {
        try {
            console.log('开始清空用户支出记录:', userId)

            // 先备份数据，以防意外
            const timestamp = new Date().toISOString().slice(0, 10)
            const backupFileName = `expenses_backup_${timestamp}.json`
            let backupFilePath = ''

            // 获取所有支出记录
            const expenses = await dbService.selectSql(
                'SELECT * FROM expenses WHERE user_id = ?',
                [userId]
            )

            console.log(`找到 ${expenses ? expenses.length : 0} 条支出记录待清空`)

            // 如果有记录，则备份
            if (expenses && expenses.length > 0) {
                try {
                    console.log('开始请求存储权限')

                    // 请求文件系统权限
                    await new Promise((resolve, reject) => {
                        plus.android.requestPermissions(
                            ['android.permission.WRITE_EXTERNAL_STORAGE'],
                            function(resultObj) {
                                console.log('权限请求结果:', resultObj)
                                if (resultObj.granted.length === 1) {
                                    resolve()
                                } else {
                                    reject(new Error('未授予存储权限'))
                                }
                            },
                            function(error) {
                                console.error('权限请求失败:', error)
                                reject(error)
                            }
                        )
                    })

                    console.log('已获得存储权限，准备导出数据')

                    // 使用原生 Java 方法
                    const Environment = plus.android.importClass("android.os.Environment")
                    const File = plus.android.importClass("java.io.File")

                    // 获取当前应用的主Activity
                    const main = plus.android.runtimeMainActivity()

                    // 获取外部存储路径
                    const externalStorageDir = Environment.getExternalStorageDirectory()
                    const filePath = externalStorageDir.getAbsolutePath() + "/Download/" + backupFileName
                    console.log('使用外部存储路径:', filePath)

                    // 创建目录
                    const fileDir = new File(filePath).getParentFile()
                    if (!fileDir.exists()) {
                        fileDir.mkdirs()
                    }

                    // 将数据转为JSON字符串
                    const backupData = JSON.stringify(expenses, null, 2)

                    // 写入文件
                    const FileOutputStream = plus.android.importClass("java.io.FileOutputStream")
                    const OutputStreamWriter = plus.android.importClass("java.io.OutputStreamWriter")
                    const BufferedWriter = plus.android.importClass("java.io.BufferedWriter")

                    const fos = new FileOutputStream(filePath)
                    const osw = new OutputStreamWriter(fos, "UTF-8")
                    const bw = new BufferedWriter(osw)

                    bw.write(backupData)
                    bw.flush()
                    bw.close()
                    osw.close()
                    fos.close()

                    console.log('数据备份成功:', filePath)
                    backupFilePath = filePath
                } catch (backupError) {
                    console.error('备份数据失败:', backupError)
                    // 备份失败不影响清空操作继续
                }
            }

            // 清空支出记录
            await dbService.executeSql(
                'DELETE FROM expenses WHERE user_id = ?',
                [userId]
            )

            // 重置所有分类的总金额
            await dbService.executeSql(
                'UPDATE categories SET total_amount = 0 WHERE user_id = ?',
                [userId]
            )

            console.log('清空支出记录成功')
            return {
                success: true,
                message: '清空成功',
                backupFile: backupFilePath || backupFileName
            }
        } catch (error) {
            console.error('清空支出记录失败:', error)
            throw error
        }
    }



}

export const localDataService = new LocalDataService()

