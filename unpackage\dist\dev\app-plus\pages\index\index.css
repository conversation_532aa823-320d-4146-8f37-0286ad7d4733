
.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		background-color: #007AFF;
		padding: 0.625rem 0.9375rem;
		padding-top: var(--status-bar-height);
}
.header-title {
		color: #FFFFFF;
		font-size: 1.125rem;
		font-weight: bold;
}
.header-version {
		position: absolute;
		right: 0.9375rem;
		top: calc(var(--status-bar-height) + 0.625rem);
		color: rgba(255, 255, 255, 0.7);
		font-size: 0.75rem;
}
.table-list {
		flex: 1;
		padding: 0.9375rem;
}
.table-list-header {
		margin-bottom: 0.625rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.table-list-title {
		font-size: 1rem;
		font-weight: bold;
}
.header-buttons {
		display: flex;
		flex-direction: row;
		align-items: center;
		gap: 0.625rem;
}
.help-button {
		width: 1.5625rem;
		height: 1.5625rem;
		background-color: #007AFF;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
}
.help-icon {
		color: #FFFFFF;
		font-size: 1rem;
		font-weight: bold;
}
.clear-button {
		width: 1.5625rem;
		height: 1.5625rem;
		background-color: #DD524D;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
}
.clear-icon {
		color: #FFFFFF;
		font-size: 0.875rem;
}
.empty-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 9.375rem;
		color: #999999;
		font-size: 0.875rem;
}
.table-items {
		display: flex;
		flex-direction: column;
		gap: 0.625rem;
}
.table-item {
		background-color: #FFFFFF;
		border-radius: 0.3125rem;
		padding: 0.625rem;
		box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.table-item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 0.3125rem;
}
.table-name {
		font-size: 1rem;
		font-weight: bold;
}
.table-column-count {
		font-size: 0.75rem;
		color: #666666;
}
.table-description {
		font-size: 0.8125rem;
		color: #666666;
}
.fab-button {
		position: fixed;
		right: 0.9375rem;
		bottom: 0.9375rem;
		width: 3.125rem;
		height: 3.125rem;
		background-color: #007AFF;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 0.125rem 0.3125rem rgba(0, 0, 0, 0.2);
}
.fab-icon {
		color: #FFFFFF;
		font-size: 1.875rem;
		font-weight: bold;
}
