/**
 * 表格通用样式
 * 统一管理所有表格的样式定义
 */

/* 基础表格样式 */
.data-table {
  background-color: #FFFFFF;
  border-radius: 8rpx;
  overflow: hidden;
  width: 100%;
}

.table-scroll {
  width: 100%;
  height: 1000rpx;
  background-color: #FFFFFF;
}

.table-header-container {
  width: 100%;
  border-bottom: 1rpx solid #EEEEEE;
}

.table-header-cell {
  background-color: #F8F8F8;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #EEEEEE;
  transition: background-color 0.2s;
  width: 100%;
  min-width: fit-content;
}

.table-row:active {
  background-color: #E0F0FF;
}

.table-row-even {
  background-color: #FFFFFF;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row-selected {
  background-color: #E0F0FF;
}

.table-cell {
  min-width: 200rpx;
  padding: 20rpx;
  border-right: 1rpx solid #EEEEEE;
}

.table-cell:last-child {
  border-right: none;
}

.table-header-text {
  font-size: 26rpx;
  color: #333333;
}

.table-cell-text {
  font-size: 26rpx;
  color: #666666;
  display: block;
  width: 100%;
}

/* 文本样式 */
.text-primary {
  color: #007AFF;
  font-weight: bold;
}

.text-highlight {
  background-color: #FFFF00;
  padding: 0 4rpx;
  border-radius: 4rpx;
}

.text-wrap {
  white-space: normal;
  word-break: break-word;
  overflow: visible;
}

/* 复选框样式 */
.table-cell-checkbox {
  width: 60rpx;
  flex: none;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border-radius: 4rpx;
  border: 1rpx solid #CCCCCC;
  background-color: #FFFFFF;
}

.checkbox-selected {
  background-color: #007AFF;
  border-color: #007AFF;
  position: relative;
}

.checkbox-selected::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 3rpx solid #FFFFFF;
  border-bottom: 3rpx solid #FFFFFF;
  transform: rotate(-45deg);
  top: 8rpx;
  left: 6rpx;
}

/* 批量操作样式 */
.table-batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #F0F0F0;
  border-bottom: 1rpx solid #EEEEEE;
  width: 100%;
}

.batch-select-all {
  display: flex;
  align-items: center;
}

.batch-select-text {
  font-size: 28rpx;
  margin-left: 10rpx;
}

.batch-actions {
  display: flex;
  align-items: center;
}

.batch-action-text {
  font-size: 28rpx;
  color: #FF6B6B;
  margin-right: 20rpx;
}

.batch-action-cancel {
  font-size: 28rpx;
  color: #999999;
}

/* 特定表格列的样式 */
.column-filename {
  min-width: 150rpx;
  max-width: 200rpx;
}

.column-articletype {
  min-width: 120rpx;
  max-width: 150rpx;
}

.column-articlecontent {
  min-width: 400rpx;
  width: auto;
  flex: 1;
}

.column-keywords {
  min-width: 150rpx;
  max-width: 200rpx;
}

.column-category {
  min-width: 150rpx;
  max-width: 200rpx;
}

.column-docnumber {
  min-width: 200rpx;
  max-width: 250rpx;
}

.column-publishunit {
  min-width: 200rpx;
  max-width: 300rpx;
}

/* 项目表列样式 */
.column-projectname {
  min-width: 300rpx;
  width: auto;
  flex: 1;
}

.column-legalperson {
  min-width: 200rpx;
  max-width: 300rpx;
}

.column-constructionlocation {
  min-width: 200rpx;
  max-width: 300rpx;
}

.column-constructionscale {
  min-width: 400rpx;
  width: auto;
  flex: 2;
}

/* 子项目表列样式 */
.column-subprojectname {
  min-width: 120rpx;
  max-width: 150rpx;
}

.column-constructionunit {
  min-width: 100rpx;
  max-width: 130rpx;
}

.column-agentunit {
  min-width: 80rpx;
  max-width: 100rpx;
}

.column-surveyunit {
  min-width: 70rpx;
  max-width: 90rpx;
}

.column-designunit {
  min-width: 70rpx;
  max-width: 90rpx;
}

.column-supervisionunit {
  min-width: 80rpx;
  max-width: 100rpx;
}

.column-constructorunit {
  min-width: 100rpx;
  max-width: 130rpx;
}

.column-projectdescription {
  min-width: 120rpx;
  width: auto;
  flex: 1;
}
