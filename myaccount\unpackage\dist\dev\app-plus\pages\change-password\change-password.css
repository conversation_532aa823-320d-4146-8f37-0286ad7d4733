/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  min-height: 100vh;
  background: #f5f5f5;
}
.header {
  background: #4a90e2;
  padding: 8px;
  color: white;
}
.header .header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 960px;
  margin: 0 auto;
  padding: 0 8px;
  position: relative;
}
.header .title {
  color: white;
  font-size: 16px;
  font-weight: 500;
}
.form-container {
  margin: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.input-group {
  margin-bottom: 16px;
}
.input-group .input-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}
.input-group .input-field {
  width: 100%;
  height: 40px;
  padding: 0 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}
.input-group .input-field:focus {
  border-color: #4a90e2;
  outline: none;
}
.input-group .input-field::-webkit-input-placeholder {
  color: #999;
}
.input-group .input-field::placeholder {
  color: #999;
}
.submit-button {
  width: 100%;
  height: 40px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  margin-top: 8px;
  transition: background-color 0.3s ease;
}
.submit-button:active {
  background: #357abd;
}
.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}