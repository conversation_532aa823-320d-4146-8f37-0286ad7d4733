/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
  box-sizing: border-box;
}
.header {
  background: #4a90e2;
  padding: 30px 16px;
}
.header .header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding-top: 15px;
}
.header .logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 12px;
}
.header .title {
  color: white;
  font-size: 20px;
  font-weight: 500;
}
.filter-section {
  background: white;
  padding: 16px;
  margin: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.filter-item {
  margin-bottom: 12px;
}
.filter-item:last-child {
  margin-bottom: 16px;
}
.filter-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}
.date-range {
  display: flex;
  align-items: center;
}
.date-separator {
  margin: 0 8px;
  color: #666;
}
.picker-item {
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
}
.filter-input {
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
}
.filter-button {
  width: 100%;
  height: 40px;
  line-height: 40px;
  background: #4a90e2;
  color: white;
  border-radius: 4px;
  font-size: 16px;
  text-align: center;
}
.table-container {
  flex: 1;
  background: white;
  margin: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  width: auto;
  box-sizing: border-box;
}
.table {
  width: 100%;
  box-sizing: border-box;
}
.table-header {
  display: flex;
  background: #4a90e2;
  color: white;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 8px 8px 0 0;
  width: 100%;
  box-sizing: border-box;
}
.table-row {
  display: flex;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  width: 100%;
  box-sizing: border-box;
}
.table-row:last-child {
  border-bottom: none;
}
.table-row:hover {
  background-color: #f8f9fa;
}
.table-cell {
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 4px;
  box-sizing: border-box;
}
.date-cell {
  width: 85px;
  flex: none;
}
.category-cell {
  width: 100px;
  flex: none;
}
.target-cell {
  flex: 1;
  min-width: 0;
  padding-right: 16px;
  word-break: break-all;
  white-space: normal;
  overflow: visible;
}
.amount-cell {
  width: 80px;
  flex: none;
  text-align: right;
  padding-right: 16px;
  white-space: nowrap;
}
.empty-state {
  padding: 24px;
  text-align: center;
  color: #999;
  font-size: 14px;
}