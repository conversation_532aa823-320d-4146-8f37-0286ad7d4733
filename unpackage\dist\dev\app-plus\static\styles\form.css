/**
 * 表单通用样式
 * 统一管理所有表单的样式定义
 */

/* 基础容器样式 */
.content {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.form-container {
  flex: 1;
  padding: 30rpx;
}

.form-content {
  padding: 30rpx;
  padding-bottom: 300rpx; /* 增加底部空间，避免输入法遮挡 */
}

/* 表单项样式 */
.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.input-container {
  background-color: #FFFFFF;
  border-radius: 8rpx;
  border: 2rpx solid #E5E5E5;
  overflow: hidden;
}

.form-input {
  width: 100%;
  padding: 24rpx 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: transparent;
  border: none;
  outline: none;
}

.form-input:disabled {
  background-color: #F0F0F0;
  color: #999999;
}

.form-textarea {
  width: 100%;
  padding: 24rpx 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: transparent;
  border: none;
  outline: none;
  min-height: 120rpx;
  resize: none;
}

/* 选择器样式 */
.picker-view {
  padding: 24rpx 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #FFFFFF;
}

.picker-placeholder {
  color: #999999;
}

/* 错误和提示样式 */
.form-error {
  display: block;
  color: #FF3B30;
  font-size: 24rpx;
  margin-top: 10rpx;
}

.form-hint {
  display: block;
  color: #666666;
  font-size: 24rpx;
  margin-top: 10rpx;
  font-style: italic;
}

/* 按钮样式 */
.button-group {
  display: flex;
  gap: 20rpx;
  margin-top: 60rpx;
  padding-bottom: 40rpx;
}

.save-button {
  background-color: #007AFF;
  color: #FFFFFF;
  font-size: 32rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 30rpx;
  border: none;
}

.save-button:disabled {
  background-color: #CCCCCC;
  color: #999999;
}

.cancel-button {
  flex: 1;
  height: 88rpx;
  background-color: #F2F2F7;
  color: #666666;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
}

/* 提示信息样式 */
.empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999999;
  font-size: 28rpx;
}

.loading-tip {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  color: #999999;
  font-size: 28rpx;
}

/* 搜索和操作栏样式 */
.search-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  width: 100%;
  padding: 0 10rpx;
}

.search-input-container {
  position: relative;
  width: 100%;
}

.search-input {
  background-color: #FFFFFF;
  border-radius: 8rpx;
  padding: 20rpx 60rpx 20rpx 20rpx;
  font-size: 30rpx;
  border: 1rpx solid #EEEEEE;
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
  height: 80rpx;
}

.search-clear {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40rpx;
  color: #999999;
  width: 50rpx;
  height: 50rpx;
  text-align: center;
  line-height: 50rpx;
  z-index: 10;
}

/* 操作按钮行样式 */
.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  flex-wrap: nowrap;
  padding: 0 10rpx;
  overflow-x: auto;
}

.action-button {
  background-color: #F0F0F0;
  border-radius: 8rpx;
  padding: 15rpx 10rpx;
  margin: 0 8rpx 0 0;
  min-width: 100rpx;
  text-align: center;
  flex: 1;
  max-width: 140rpx;
  white-space: nowrap;
}

.action-button:last-child {
  margin-right: 0;
}

.action-button-text {
  font-size: 24rpx;
  color: #333333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 智能输入按钮样式 */
.smart-button {
  background-color: #007AFF;
}

.smart-button .action-button-text {
  color: #FFFFFF;
}

/* 筛选标签样式 */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.filter-tag {
  background-color: #E0F0FF;
  border-radius: 8rpx;
  padding: 6rpx 10rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.filter-tag-text {
  font-size: 24rpx;
  color: #007AFF;
}

.filter-tag-remove {
  font-size: 28rpx;
  color: #007AFF;
  margin-left: 10rpx;
  width: 30rpx;
  height: 30rpx;
  text-align: center;
  line-height: 30rpx;
}

.clear-all-filters {
  font-size: 24rpx;
  color: #FF6B6B;
  margin-left: 10rpx;
  padding: 6rpx 10rpx;
}
