<template>
  <view class="container">
    <view class="header">
      <view class="header-content">
        <text class="title">分类管理</text>
      </view>
    </view>

    <!-- 加载状态指示器 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 分类列表 -->
    <view class="category-list">
      <!-- 一级分类 -->
      <view v-for="(category, index) in categories" :key="category.category_id" class="category-item level-1">
        <view class="category-info">
          <text class="category-name">{{ category.name }}</text>
          <view class="category-actions">
            <button class="action-btn edit-btn" @tap="showEditDialog(category)">编辑</button>
            <button class="action-btn add-btn" @tap="showAddDialog(category.category_id, 2)">添加子类</button>
            <button class="action-btn delete-btn" @tap="confirmDelete(category.category_id)">删除</button>
          </view>
        </view>

        <!-- 二级分类 -->
        <view v-if="category.children && category.children.length > 0" class="subcategory-list">
          <view v-for="subCategory in category.children" :key="subCategory.category_id" class="category-item level-2">
            <view class="category-info">
              <text class="category-name">{{ subCategory.name }}</text>
              <view class="category-actions">
                <button class="action-btn edit-btn" @tap="showEditDialog(subCategory)">编辑</button>
                <button class="action-btn add-btn" @tap="showAddDialog(subCategory.category_id, 3)">添加子类</button>
                <button class="action-btn delete-btn" @tap="confirmDelete(subCategory.category_id)">删除</button>
              </view>
            </view>

            <!-- 三级分类 -->
            <view v-if="subCategory.children && subCategory.children.length > 0" class="subcategory-list">
              <view v-for="item in subCategory.children" :key="item.category_id" class="category-item level-3">
                <view class="category-info">
                  <text class="category-name">{{ item.name }}</text>
                  <view class="category-actions">
                    <button class="action-btn edit-btn" @tap="showEditDialog(item)">编辑</button>
                    <button class="action-btn delete-btn" @tap="confirmDelete(item.category_id)">删除</button>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加一级分类按钮 -->
    <view class="add-category-btn" @tap="showAddDialog(null, 1)">
      <text>添加一级分类</text>
    </view>

    <!-- 编辑分类弹窗 -->
    <view class="modal" v-if="showEditModal">
      <view class="modal-mask" @tap="closeEditDialog"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">编辑分类</text>
          <text class="modal-close" @tap="closeEditDialog">×</text>
        </view>
        <view class="modal-body">
          <input
            class="modal-input"
            v-model="editForm.name"
            placeholder="请输入分类名称"
          />
        </view>
        <view class="modal-footer">
          <button class="modal-button cancel" @tap="closeEditDialog">取消</button>
          <button class="modal-button confirm" @tap="handleEditConfirm">确定</button>
        </view>
      </view>
    </view>

    <!-- 添加分类弹窗 -->
    <view class="modal" v-if="showAddModal">
      <view class="modal-mask" @tap="closeAddDialog"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">添加分类</text>
          <text class="modal-close" @tap="closeAddDialog">×</text>
        </view>
        <view class="modal-body">
          <input
            class="modal-input"
            v-model="addForm.name"
            placeholder="请输入分类名称"
          />
        </view>
        <view class="modal-footer">
          <button class="modal-button cancel" @tap="closeAddDialog">取消</button>
          <button class="modal-button confirm" @tap="handleAddConfirm">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'

export default {
  setup() {
    const store = useStore()
    const editForm = ref({ categoryId: '', name: '' })
    const addForm = ref({ parentId: null, level: 1, name: '' })
    const isLoading = ref(false)
    const showEditModal = ref(false)
    const showAddModal = ref(false)

    // 获取分类数据
    const categories = computed(() => store.state.categories)

    // 显示编辑弹窗
    const showEditDialog = (category) => {
      editForm.value = {
        categoryId: category.category_id,
        name: category.name
      }
      showEditModal.value = true
    }

    // 关闭编辑弹窗
    const closeEditDialog = () => {
      showEditModal.value = false
    }

    // 处理编辑确认
    const handleEditConfirm = async () => {
      try {
        if (!editForm.value.name.trim()) {
          uni.showToast({
            title: '分类名称不能为空',
            icon: 'none'
          })
          return
        }

        await store.dispatch('updateCategory', {
          categoryId: editForm.value.categoryId,
          name: editForm.value.name.trim()
        })

        // 关闭弹窗
        closeEditDialog()

        // 刷新分类数据
        await store.dispatch('fetchCategories')

        uni.showToast({
          title: '修改成功',
          icon: 'success'
        })
      } catch (error) {
        uni.showToast({
          title: error.message || '修改失败',
          icon: 'none'
        })
      }
    }

    // 显示添加弹窗
    const showAddDialog = (parentId, level) => {
      addForm.value = {
        parentId,
        level,
        name: ''
      }
      showAddModal.value = true
    }

    // 关闭添加弹窗
    const closeAddDialog = () => {
      showAddModal.value = false
    }

    // 处理添加确认
    const handleAddConfirm = async () => {
      try {
        if (!addForm.value.name.trim()) {
          uni.showToast({
            title: '分类名称不能为空',
            icon: 'none'
          })
          return
        }

        // 计算新分类的排序值
        let sortOrder = 1
        if (addForm.value.parentId) {
          // 如果有父分类，找到同级分类的最大排序值
          const parent = findCategoryById(addForm.value.parentId)
          if (parent && parent.children) {
            const maxSortOrder = Math.max(...parent.children.map(c => c.sort_order || 0), 0)
            sortOrder = maxSortOrder + 1
          }
        } else {
          // 如果是一级分类，找到所有一级分类的最大排序值
          const maxSortOrder = Math.max(...categories.value.map(c => c.sort_order || 0), 0)
          sortOrder = maxSortOrder + 1
        }

        await store.dispatch('addCategory', {
          name: addForm.value.name.trim(),
          parentId: addForm.value.parentId,
          level: addForm.value.level,
          sortOrder
        })

        // 关闭弹窗
        closeAddDialog()

        // 刷新分类数据
        await store.dispatch('fetchCategories')

        uni.showToast({
          title: '添加成功',
          icon: 'success'
        })
      } catch (error) {
        uni.showToast({
          title: error.message || '添加失败',
          icon: 'none'
        })
      }
    }

    // 确认删除
    const confirmDelete = (categoryId) => {
      uni.showModal({
        title: '确认删除',
        content: '删除分类后无法恢复，确定要删除吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await store.dispatch('deleteCategory', categoryId)

              // 刷新分类数据
              await store.dispatch('fetchCategories')

              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
            } catch (error) {
              uni.showToast({
                title: error.message || '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    }

    // 根据ID查找分类
    const findCategoryById = (categoryId) => {
      const searchInCategories = (categories) => {
        for (const category of categories) {
          if (category.category_id === categoryId) return category
          if (category.children && category.children.length > 0) {
            const found = searchInCategories(category.children)
            if (found) return found
          }
        }
        return null
      }
      return searchInCategories(categories.value)
    }



    // 页面加载时初始化
    onMounted(async () => {
      if (!store.state.user) {
        uni.reLaunch({
          url: '/pages/login/login'
        })
        return
      }

      await store.dispatch('fetchCategories')
    })

    return {
      categories,
      editForm,
      addForm,
      isLoading,
      showEditModal,
      showAddModal,
      showEditDialog,
      closeEditDialog,
      handleEditConfirm,
      showAddDialog,
      closeAddDialog,
      handleAddConfirm,
      confirmDelete
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: #4a90e2;
  padding: 30px 16px;
  color: white;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-top: 15px;
  }

  .title {
    color: white;
    font-size: 20px;
    font-weight: 500;
  }
}

.category-list {
  padding: 16px;
}

.category-item {
  margin-bottom: 12px;
  background: white;
  border-radius: 8px;
  overflow: hidden;

  &.level-1 {
    border-left: 4px solid #4a90e2;
  }

  &.level-2 {
    border-left: 4px solid #67c23a;
    margin-left: 16px;
  }

  &.level-3 {
    border-left: 4px solid #e6a23c;
    margin-left: 16px;
  }
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

.category-name {
  font-size: 16px;
  font-weight: 500;
}

.category-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  font-size: 12px;
  padding: 4px 8px;
  margin: 0;
  line-height: 1.2;

  &.edit-btn {
    background: #4a90e2;
    color: white;
  }

  &.add-btn {
    background: #67c23a;
    color: white;
  }

  &.delete-btn {
    background: #f56c6c;
    color: white;
  }
}

.subcategory-list {
  padding: 0 0 8px 0;
}

.add-category-btn {
  margin: 20px 16px;
  background: #4a90e2;
  color: white;
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  font-weight: 500;
}

.dialog-input {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 12px;
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.8);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.loading-text {
  font-size: 16px;
  color: #333;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 600px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  z-index: 1;
  margin: 0 auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.modal-close {
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 4px;
}

.modal-body {
  padding: 20px;
  box-sizing: border-box;
}

.modal-input {
  width: 100%;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  box-sizing: border-box;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  border-top: 1px solid #eee;
  gap: 12px;
  box-sizing: border-box;
}

.modal-button {
  font-size: 14px;
  padding: 8px 24px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  white-space: nowrap;
  min-width: 80px;
  text-align: center;
}

.modal-button.cancel {
  background-color: #f5f5f5;
  color: #606266;
  border: 1px solid #dcdfe6;
}

.modal-button.cancel:active {
  background-color: #e9e9e9;
}

.modal-button.confirm {
  background-color: #4a90e2;
  color: #fff;
  border: none;
}

.modal-button.confirm:active {
  background-color: #357abd;
}
</style>
