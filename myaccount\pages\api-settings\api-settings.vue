<template>
  <view class="container">
    <view class="header">
      <view class="header-content">
        <text class="title">API 设置</text>
      </view>
    </view>

    <view class="form-container">
      <view class="form-content">
        <view class="form-item">
          <text class="label">DeepSeek API 密钥</text>
          <input 
            type="text" 
            v-model="apiKey" 
            placeholder="请输入 DeepSeek API 密钥"
            class="input-field"
            @blur="validateApiKey"
          />
          <text v-if="errors.apiKey" class="error-text">{{ errors.apiKey }}</text>
          <text class="hint-text">格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</text>
        </view>
        
        <view class="form-item">
          <text class="label">选择模型</text>
          <view class="model-selector">
            <view 
              class="model-option" 
              :class="{ active: selectedModel === 'deepseek-chat' }"
              @tap="selectedModel = 'deepseek-chat'"
            >
              <text class="model-name">DeepSeek-V3</text>
              <text class="model-id">deepseek-chat</text>
              <text class="model-desc">通用对话模型，适合日常对话和分析</text>
            </view>
            
            <view 
              class="model-option" 
              :class="{ active: selectedModel === 'deepseek-reasoner' }"
              @tap="selectedModel = 'deepseek-reasoner'"
            >
              <text class="model-name">DeepSeek-R1</text>
              <text class="model-id">deepseek-reasoner</text>
              <text class="model-desc">推理模型，适合复杂逻辑和数学计算</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">温度 (Temperature)</text>
          <view class="slider-container">
            <slider 
              :value="temperature * 10" 
              min="0" 
              max="10" 
              show-value 
              @change="handleTemperatureChange"
            />
            <text class="slider-value">{{ temperature.toFixed(1) }}</text>
          </view>
          <text class="hint-text">较低的值使输出更确定，较高的值使输出更随机</text>
        </view>

        <button 
          class="submit-button" 
          @tap="saveSettings"
          :disabled="isLoading || hasErrors"
        >
          {{ isLoading ? '保存中...' : '保存设置' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'

export default {
  setup() {
    const store = useStore()
    const apiKey = ref('')
    const selectedModel = ref('deepseek-chat')
    const temperature = ref(0.2)
    const isLoading = ref(false)
    const errors = ref({
      apiKey: ''
    })

    // 验证 API 密钥
    const validateApiKey = () => {
      if (!apiKey.value) {
        errors.value.apiKey = 'API 密钥不能为空'
      } else if (!apiKey.value.startsWith('sk-')) {
        errors.value.apiKey = 'API 密钥格式不正确，应以 sk- 开头'
      } else {
        errors.value.apiKey = ''
      }
    }

    // 处理温度滑块变化
    const handleTemperatureChange = (e) => {
      temperature.value = e.detail.value / 10
    }

    const hasErrors = computed(() => {
      return Object.values(errors.value).some(error => error !== '')
    })

    // 保存设置
    const saveSettings = async () => {
      validateApiKey()
      
      if (hasErrors.value) {
        return
      }

      isLoading.value = true
      try {
        await store.dispatch('saveApiSettings', {
          apiKey: apiKey.value,
          model: selectedModel.value,
          temperature: temperature.value
        })
        
        uni.showToast({
          title: '设置已保存',
          icon: 'success'
        })
        
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        console.error('保存设置失败:', error)
        uni.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        })
      } finally {
        isLoading.value = false
      }
    }

    // 页面加载时获取当前设置
    onMounted(() => {
      const settings = store.state.apiSettings
      if (settings) {
        apiKey.value = settings.apiKey || ''
        selectedModel.value = settings.model || 'deepseek-chat'
        temperature.value = settings.temperature !== undefined ? settings.temperature : 0.2
      }
    })

    return {
      apiKey,
      selectedModel,
      temperature,
      isLoading,
      errors,
      hasErrors,
      validateApiKey,
      handleTemperatureChange,
      saveSettings
    }
  }
}
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0;
  box-sizing: border-box;
}

.header {
  background: #4a90e2;
  padding: 30px 16px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 30px;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-top: 15px;
  }

  .title {
    color: white;
    font-size: 20px;
    font-weight: 500;
  }
}

.form-container {
  flex: 1;
  padding: 0 16px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.form-content {
  width: 100%;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.form-item {
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
  
  .label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
    font-weight: 500;
  }

  .input-field {
    width: 100%;
    height: 40px;
    padding: 0 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;

    &:focus {
      border-color: #4a90e2;
      outline: none;
      box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
    }
  }

  .error-text {
    display: block;
    margin-top: 4px;
    color: #ff4d4f;
    font-size: 12px;
  }
  
  .hint-text {
    display: block;
    margin-top: 4px;
    color: #999;
    font-size: 12px;
  }
}

.model-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.model-option {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  transition: all 0.3s ease;
  
  &.active {
    border-color: #4a90e2;
    background-color: rgba(74, 144, 226, 0.05);
  }
  
  .model-name {
    display: block;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }
  
  .model-id {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
  }
  
  .model-desc {
    display: block;
    font-size: 12px;
    color: #999;
  }
}

.slider-container {
  display: flex;
  align-items: center;
  margin-top: 8px;
  
  slider {
    flex: 1;
  }
  
  .slider-value {
    width: 40px;
    text-align: right;
    font-size: 14px;
    color: #333;
    margin-left: 8px;
  }
}

.submit-button {
  width: 100%;
  height: 40px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  margin-top: 24px;
  transition: background-color 0.3s ease;

  &:hover {
    background: #357abd;
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
}
</style>
