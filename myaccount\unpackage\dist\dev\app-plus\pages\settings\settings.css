/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  min-height: 100vh;
  background: #f5f5f5;
}
.header {
  background: #4a90e2;
  padding: 30px 16px;
  color: white;
}
.header .header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding-top: 15px;
}
.header .title {
  color: white;
  font-size: 20px;
  font-weight: 500;
}
.settings-list {
  margin: 12px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
  transition: background-color 0.3s ease;
}
.settings-item:last-child {
  border-bottom: none;
}
.settings-item:active {
  background-color: #f5f5f5;
}
.settings-item .item-label {
  font-size: 14px;
  color: #333;
}
.settings-item .item-arrow {
  color: #999;
  font-size: 14px;
}
.settings-item.danger {
  background-color: rgba(245, 108, 108, 0.05);
}
.settings-item.danger:active {
  background-color: rgba(245, 108, 108, 0.1);
}
.danger-text {
  color: #f56c6c !important;
  font-weight: 500;
}