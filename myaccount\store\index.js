import { createStore } from 'vuex'
import { localDataService } from '../services/localDataService'
import { dbService } from '../utils/db'

const store = createStore({
  state: {
    user: uni.getStorageSync('user') ? JSON.parse(uni.getStorageSync('user')) : null,
    categories: [],
    isLoading: false,
    error: null,
    categoryMap: new Map(),
    apiSettings: uni.getStorageSync('apiSettings') ? JSON.parse(uni.getStorageSync('apiSettings')) : {
      apiKey: 'sk-38ff2a6b9f754b059fa42839d5a4b426',
      model: 'deepseek-chat',
      temperature: 0.2
    }
  },
  mutations: {
    setUser(state, user) {
      state.user = user
      if (user) {
        uni.setStorageSync('user', JSON.stringify(user))
      } else {
        uni.removeStorageSync('user')
      }
    },

    setApiSettings(state, settings) {
      state.apiSettings = settings
      uni.setStorageSync('apiSettings', JSON.stringify(settings))
    },
    clearUserInfo(state) {
      state.user = null
      state.categories = []
      state.error = null
      uni.removeStorageSync('user')
    },
    setLoading(state, isLoading) {
      state.isLoading = isLoading
    },
    setError(state, error) {
      state.error = error
    },
    updateCategories(state, categories) {
      // 创建一个新的 Map 来存储所有分类
      const categoryMap = new Map()

      // 第一遍遍历：处理基本数据并建立 Map
      const processCategories = (items, parentId = null) => {
        items.forEach(category => {
          // 确保数值类型正确
          category.total_amount = Number(category.total_amount || 0)
          category.parent_id = parentId

          // 存入 Map
          categoryMap.set(category.category_id, category)

          if (category.children && category.children.length > 0) {
            processCategories(category.children, category.category_id)
          }
        })
      }

      processCategories(categories)

      // 第二遍遍历：建立父子关系
      categoryMap.forEach(category => {
        if (category.parent_id) {
          const parent = categoryMap.get(category.parent_id)
          if (parent) {
            if (!parent.children) {
              parent.children = []
            }
            // 避免重复添加
            if (!parent.children.find(child => child.category_id === category.category_id)) {
              parent.children.push(category)
            }
          }
        }
      })

      // 更新 state
      state.categories = categories.filter(c => !c.parent_id) // 只保留顶级分类
      state.categoryMap = categoryMap
    },
    updateCategoryAmount(state, { categoryId, amount }) {
      const category = state.categoryMap.get(categoryId)
      if (!category) return

      // 更新当前分类的金额
      category.total_amount = (category.total_amount || 0) + amount

      // 更新父分类的金额
      const updateParentAmount = (parentId, deltaAmount) => {
        if (!parentId) return
        const parent = state.categoryMap.get(parentId)
        if (parent) {
          parent.total_amount = (parent.total_amount || 0) + deltaAmount
          updateParentAmount(parent.parent_id, deltaAmount)
        }
      }

      updateParentAmount(category.parent_id, amount)
    }
  },
  actions: {
    async init({ commit }) {
      try {
        await dbService.open()
        await dbService.initTables()
      } catch (error) {
        console.error('初始化失败:', error)
        commit('setError', error.message)
      }
    },

    async register({ commit }, { username, password, email }) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        const user = await localDataService.register(username, password, email)
        commit('setUser', user)
        return user
      } catch (error) {
        console.error('注册失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    async login({ commit }, { username, password }) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        const user = await localDataService.login(username, password)
        commit('setUser', user)

        // 登录成功后获取分类
        const categories = await localDataService.getCategories(user.user_id)
        console.log('登录后获取的分类数据:', categories)
        commit('updateCategories', categories)
        return user
      } catch (error) {
        console.error('登录失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    logout({ commit }) {
      commit('clearUserInfo')
    },

    async fetchCategories({ commit, state }) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        if (!state.user) {
          throw new Error('用户未登录')
        }

        const categories = await localDataService.getCategories(state.user.user_id)

        commit('updateCategories', categories)
        return categories
      } catch (error) {
        console.error('获取分类失败:', error)
        commit('setError', error.message)
        return []
      } finally {
        commit('setLoading', false)
      }
    },

    async saveExpense({ commit, state, dispatch }, { date, records }) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        if (!state.user) {
          throw new Error('用户未登录')
        }

        await localDataService.saveExpense(state.user.user_id, date, records)

        // 重新获取更新后的分类数据
        await dispatch('fetchCategories')

        return { success: true }
      } catch (error) {
        console.error('保存支出失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    async changePassword({ commit, state }, { oldPassword, newPassword }) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        if (!state.user) {
          throw new Error('用户未登录')
        }

        const result = await localDataService.changePassword(
          state.user.user_id,
          oldPassword,
          newPassword
        )

        if (result.success) {
          // 密码修改成功后，清除用户信息并退出登录
          commit('clearUserInfo')
          // 跳转到登录页面
          uni.reLaunch({
            url: '/pages/login/login'
          })
        }

        return result
      } catch (error) {
        console.error('修改密码失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    updateCategories({ commit }, categories) {
      commit('updateCategories', categories)
    },

    async addCategory({ commit, state, dispatch }, { name, parentId, level, sortOrder }) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        if (!state.user) {
          throw new Error('用户未登录')
        }

        const newCategory = await localDataService.addCategory(
          state.user.user_id,
          name,
          parentId,
          level,
          sortOrder
        )

        // 重新获取所有分类
        await dispatch('fetchCategories')

        return newCategory
      } catch (error) {
        console.error('添加分类失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    async updateCategory({ commit, dispatch }, { categoryId, name }) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        await localDataService.updateCategory(categoryId, name)

        // 重新获取所有分类
        await dispatch('fetchCategories')

        return { success: true }
      } catch (error) {
        console.error('修改分类失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    async deleteCategory({ commit, dispatch }, categoryId) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        await localDataService.deleteCategory(categoryId)

        // 重新获取所有分类
        await dispatch('fetchCategories')

        return { success: true }
      } catch (error) {
        console.error('删除分类失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    async updateCategoryOrder({ commit, dispatch }, { categoryId, newSortOrder }) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        await localDataService.updateCategoryOrder(categoryId, newSortOrder)

        // 重新获取所有分类
        await dispatch('fetchCategories')

        return { success: true }
      } catch (error) {
        console.error('更新分类排序失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    // 清空用户的支出记录
    async clearUserData({ commit, state, dispatch }) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        if (!state.user) {
          throw new Error('用户未登录')
        }

        const result = await localDataService.clearUserExpenses(state.user.user_id)

        // 重新获取所有分类
        await dispatch('fetchCategories')

        return result
      } catch (error) {
        console.error('清空支出记录失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    // 验证用户邮箱（用于密码重置）
    async verifyUserEmail({ commit }, { username, email }) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        const result = await localDataService.verifyUserEmail(username, email)
        return result
      } catch (error) {
        console.error('验证用户邮箱失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    // 重置密码（忘记密码时使用）
    async resetPassword({ commit }, { username, email, newPassword }) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        const result = await localDataService.resetPassword(username, email, newPassword)
        return result
      } catch (error) {
        console.error('重置密码失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    // 保存 API 设置
    async saveApiSettings({ commit }, settings) {
      try {
        commit('setLoading', true)
        commit('setError', null)

        // 验证设置
        if (!settings.apiKey) {
          throw new Error('API 密钥不能为空')
        }

        if (!settings.model) {
          throw new Error('模型不能为空')
        }

        // 保存设置
        commit('setApiSettings', settings)

        return { success: true }
      } catch (error) {
        console.error('保存 API 设置失败:', error)
        commit('setError', error.message)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },


  },
  getters: {
    isLoggedIn: state => !!state.user,
    categories: state => state.categories,
    isLoading: state => state.isLoading,
    error: state => state.error,
    getCategoryById: (state) => (categoryId) => {
      return state.categoryMap.get(categoryId) || null
    },
    getCategoryTotal: (state) => (categoryId) => {
      const calculateTotal = (category) => {
        if (!category.children || category.children.length === 0) {
          return category.total_amount || 0
        }
        return category.children.reduce((total, child) => {
          return total + calculateTotal(child)
        }, category.total_amount || 0)
      }
      const category = state.categoryMap.get(categoryId)
      return category ? calculateTotal(category) : 0
    }
  }
})

export default store