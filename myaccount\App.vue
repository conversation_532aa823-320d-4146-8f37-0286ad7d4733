<script>
import { dbService } from './utils/db'

export default {
	onLaunch: async function() {
		console.log('App Launch')
    	console.log('当前应用图标路径:', plus.runtime.appIcon)
		try {
			await dbService.initDatabase()
			console.log('数据库初始化成功')
		} catch (error) {
			console.error('数据库初始化失败:', error)
		}
	},
	onShow: function() {
		console.log('App Show')
	},
	onHide: function() {
		console.log('App Hide')
	},
	onUnload: async function() {
		try {
			await dbService.closeDatabase()
			console.log('数据库关闭成功')
		} catch (error) {
			console.error('数据库关闭失败:', error)
		}
	}
}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import './static/styles/common.scss';
</style>
