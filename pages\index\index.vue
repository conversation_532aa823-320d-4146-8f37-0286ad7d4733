<template>
	<view class="content">
		<view class="header">
			<text class="header-title">{{ appInfo.name }}</text>
			<text class="header-version">v{{ appInfo.version }}</text>
		</view>

		<view class="table-list">
			<view class="table-list-header">
				<text class="table-list-title">数据表列表</text>
				<view class="header-buttons">
					<view class="clear-button" @click="confirmClearDatabase">
						<text class="clear-icon">🗑️</text>
					</view>
					<view class="help-button" @click="showHelp">
						<text class="help-icon">?</text>
					</view>
				</view>
			</view>

			<view v-if="tables.length === 0" class="empty-tip">
				<text>暂无数据表，点击右下角按钮创建</text>
			</view>

			<view v-else class="table-items">
				<view
					v-for="(table, index) in tables"
					:key="table.id"
					class="table-item"
					@click="openTableDetail(table)"
				>
					<view class="table-item-header">
						<text class="table-name">{{ table.name }}</text>
						<text class="table-column-count">列数: {{ table.columnCount }}</text>
					</view>
					<text class="table-description">{{ table.description || '无描述' }}</text>
				</view>
			</view>
		</view>

		<view class="fab-button" @click="createTable">
			<text class="fab-icon">+</text>
		</view>
	</view>
</template>

<script>
	import {
		getAllTables,
		getTableColumns,
		clearDatabase
	} from '@/utils/sqlite.js';
	import { APP_INFO, HELP_INFO } from '@/config/index.js';
	import { showLoading, hideLoading, showToast } from '@/utils/common.js';
import { initDocumentAndArticleTables } from './init-tables.js';

	export default {
		data() {
			return {
				tables: [],
				isLoading: false,
				appInfo: APP_INFO,
				helpInfo: HELP_INFO
			}
		},
		onLoad() {
			// 初始化数据库
			this.initDatabase();
		},
		onShow() {
			// 每次显示页面时刷新表格列表
			this.loadTables();
		},
		methods: {
			// 初始化数据库
			async initDatabase() {
				this.isLoading = true;

				try {
					// 初始化文档表和条文表（不强制重新创建）
					console.log('开始初始化文档表和条文表');
					const success = await initDocumentAndArticleTables(false);
					console.log('初始化文档表和条文表结果:', success);

					// 加载表格列表
					await this.loadTables();
				} catch (e) {
					console.error('初始化数据库失败', e);
					showToast('初始化数据库失败，请重试');
				} finally {
					this.isLoading = false;
				}
			},

			// 加载表格列表
			async loadTables() {
				this.isLoading = true;
				showLoading('加载中...');

				try {
					// 获取所有表
					const tables = await getAllTables();

					// 获取每个表的列数
					const tablesWithColumnCount = [];
					for (const table of tables) {
						const columns = await getTableColumns(table.id);
						tablesWithColumnCount.push({
							...table,
							columnCount: columns.length
						});
					}

					this.tables = tablesWithColumnCount;
				} catch (e) {
					console.error('加载表格列表失败', e);
					showToast('加载表格列表失败');
				} finally {
					hideLoading();
					this.isLoading = false;
				}
			},

			// 创建表
			createTable() {
				uni.navigateTo({
					url: '/pages/table/create'
				});
			},

			// 打开表详情
			openTableDetail(table) {
				uni.navigateTo({
					url: `/pages/table/detail?id=${table.id}&name=${table.name}`
				});
			},

			// 显示帮助信息
			showHelp() {
				uni.showModal({
					title: this.helpInfo.title,
					content: this.helpInfo.content + '\n\n版本：v' + this.appInfo.version,
					showCancel: false,
					confirmText: '我知道了'
				});
			},

			// 确认清空数据库
			confirmClearDatabase() {
				uni.showModal({
					title: '清空数据库',
					content: '确定要清空数据库吗？此操作将删除所有表中的数据，且无法恢复！',
					confirmText: '确定清空',
					confirmColor: '#DD524D',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.clearDatabase();
						}
					}
				});
			},

			// 清空数据库
			async clearDatabase() {
				showLoading('正在清空数据库...');

				try {
					// 清空数据库
					const success = await clearDatabase();

					if (success) {
						showToast('数据库已清空');

						// 重新加载表格列表
						await this.loadTables();

						// 重新初始化文档表和条文表（强制重新创建）
						console.log('重新初始化文档表和条文表');
						const initResult = await initDocumentAndArticleTables(true);
						console.log('初始化结果:', initResult);

						// 再次重新加载表格列表
						await this.loadTables();
					} else {
						showToast('清空数据库失败');
					}
				} catch (e) {
					console.error('清空数据库失败:', e);
					showToast('清空数据库失败');
				} finally {
					hideLoading();
				}
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		background-color: #007AFF;
		padding: 20rpx 30rpx;
		padding-top: var(--status-bar-height);
	}

	.header-title {
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: bold;
	}

	.header-version {
		position: absolute;
		right: 30rpx;
		top: calc(var(--status-bar-height) + 20rpx);
		color: rgba(255, 255, 255, 0.7);
		font-size: 24rpx;
	}

	.table-list {
		flex: 1;
		padding: 30rpx;
	}

	.table-list-header {
		margin-bottom: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.table-list-title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.header-buttons {
		display: flex;
		flex-direction: row;
		align-items: center;
		gap: 20rpx;
	}

	.help-button {
		width: 50rpx;
		height: 50rpx;
		background-color: #007AFF;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.help-icon {
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: bold;
	}

	.clear-button {
		width: 50rpx;
		height: 50rpx;
		background-color: #DD524D;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.clear-icon {
		color: #FFFFFF;
		font-size: 28rpx;
	}

	.empty-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 300rpx;
		color: #999999;
		font-size: 28rpx;
	}

	.table-items {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.table-item {
		background-color: #FFFFFF;
		border-radius: 10rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.table-item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.table-name {
		font-size: 32rpx;
		font-weight: bold;
	}

	.table-column-count {
		font-size: 24rpx;
		color: #666666;
	}

	.table-description {
		font-size: 26rpx;
		color: #666666;
	}

	.fab-button {
		position: fixed;
		right: 30rpx;
		bottom: 30rpx;
		width: 100rpx;
		height: 100rpx;
		background-color: #007AFF;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
	}

	.fab-icon {
		color: #FFFFFF;
		font-size: 60rpx;
		font-weight: bold;
	}
</style>
