
.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 0.625rem 0.9375rem;
		padding-top: var(--status-bar-height);
}
.header-left, .header-right {
		width: 3.75rem;
}
.header-back {
		color: #FFFFFF;
		font-size: 0.875rem;
}
.header-title {
		color: #FFFFFF;
		font-size: 1.125rem;
		font-weight: bold;
		max-width: 12.5rem;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
}
.form-container {
		flex: 1;
		padding: 0.9375rem;
}
.form-item {
		margin-bottom: 0.9375rem;
}
.form-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 0.3125rem;
}
.form-label {
		font-size: 1rem;
		font-weight: bold;
}
.form-action {
		background-color: #007AFF;
		padding: 0.3125rem 0.625rem;
		border-radius: 0.1875rem;
}
.form-action-text {
		color: #FFFFFF;
		font-size: 0.75rem;
}
.empty-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 6.25rem;
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		color: #999999;
		font-size: 0.875rem;
}
.column-list {
		display: flex;
		flex-direction: column;
		gap: 0.625rem;
}
.column-item {
		background-color: #FFFFFF;
		border-radius: 0.25rem;
		padding: 0.625rem;
}
.column-item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 0.3125rem;
}
.column-name {
		font-size: 0.9375rem;
		font-weight: bold;
}
.column-actions {
		display: flex;
		gap: 0.3125rem;
}
.column-action {
		background-color: #FF3B30;
		padding: 0.1875rem 0.375rem;
		border-radius: 0.125rem;
}
.column-action-text {
		color: #FFFFFF;
		font-size: 0.6875rem;
}
.column-details {
		display: flex;
		flex-direction: column;
		gap: 0.1875rem;
}
.column-type {
		font-size: 0.8125rem;
		color: #333333;
}
.column-attributes {
		display: flex;
		flex-wrap: wrap;
		gap: 0.3125rem;
}
.column-attribute {
		background-color: #F0F0F0;
		padding: 0.125rem 0.375rem;
		border-radius: 0.125rem;
		font-size: 0.6875rem;
		color: #666666;
}
.column-foreign-key {
		font-size: 0.6875rem;
		color: #666666;
		font-style: italic;
		margin-top: 0.1875rem;
}
.tips {
		margin-top: 0.9375rem;
		background-color: #FFF9E6;
		border-radius: 0.25rem;
		padding: 0.625rem;
}
.tips-text {
		font-size: 0.75rem;
		color: #996633;
		line-height: 1.5;
		display: block;
}
