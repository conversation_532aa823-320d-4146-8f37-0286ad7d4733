<template>
	<view class="content">
		<view class="header">
			<view class="header-left" @click="goBack">
				<text class="header-back">返回</text>
			</view>
			<text class="header-title">编辑表结构: {{ tableName }}</text>
			<view class="header-right"></view>
		</view>

		<view class="form-container">
			<!-- 列定义 -->
			<view class="form-item">
				<view class="form-header">
					<text class="form-label">列定义</text>
					<view class="form-action" @click="addColumn">
						<text class="form-action-text">添加列</text>
					</view>
				</view>

				<view v-if="columns.length === 0" class="empty-tip">
					<text>加载中...</text>
				</view>

				<view v-else class="column-list">
					<view
						v-for="column in columns"
						:key="column.id"
						class="column-item"
					>
						<view class="column-item-header">
							<text class="column-name">{{ column.name }}</text>
							<view class="column-actions">
								<view
									v-if="!isPrimaryKey(column)"
									class="column-action"
									@click="deleteColumn(column)"
								>
									<text class="column-action-text">删除</text>
								</view>
							</view>
						</view>

						<view class="column-details">
							<text class="column-type">{{ getColumnTypeText(column.type) }}</text>
							<view class="column-attributes">
								<text v-if="column.is_primary_key === 1" class="column-attribute">主键</text>
								<text v-if="column.is_not_null === 1" class="column-attribute">不允许为空</text>
								<text v-if="column.is_unique === 1" class="column-attribute">唯一</text>
							</view>
							<text v-if="column.is_foreign_key === 1" class="column-foreign-key">
								外键关联: 表ID={{ column.reference_table_id }}, 列ID={{ column.reference_column_id }}
							</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 提示信息 -->
			<view class="tips">
				<text class="tips-text">提示：</text>
				<text class="tips-text">1. 主键列不能删除</text>
				<text class="tips-text">2. 添加列后，原有数据的新列值将为空</text>
				<text class="tips-text">3. 删除列会永久删除该列的所有数据</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getTableColumns,
		addColumnToTable,
		deleteColumnFromTable
	} from '@/utils/sqlite.js';

	export default {
		data() {
			return {
				tableId: 0,
				tableName: '',
				columns: []
			}
		},
		onLoad(options) {
			this.tableId = parseInt(options.id) || 0;
			this.tableName = options.name || '';

			// 加载表格列信息
			this.loadColumns();
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 加载列信息
			async loadColumns() {
				try {
					const columns = await getTableColumns(this.tableId);
					this.columns = columns;
				} catch (e) {
					console.error('加载列信息失败', e);
					uni.showToast({
						title: '加载列信息失败',
						icon: 'none'
					});
				}
			},

			// 判断是否为主键
			isPrimaryKey(column) {
				return column.is_primary_key === 1;
			},

			// 获取列类型文本
			getColumnTypeText(type) {
				switch (type) {
					case 'TEXT': return '文本 (TEXT)';
					case 'INTEGER': return '整数 (INTEGER)';
					case 'REAL': return '小数 (REAL)';
					case 'BLOB': return '二进制 (BLOB)';
					default: return type;
				}
			},

			// 添加列
			addColumn() {
				uni.navigateTo({
					url: '/pages/column/create?editMode=true',
					events: {
						// 监听列创建页面返回的数据
						columnCreated: async (column) => {
							// 检查列名是否重复
							if (this.columns.some(c => c.name === column.name)) {
								uni.showToast({
									title: `列名 '${column.name}' 已存在`,
									icon: 'none'
								});
								return;
							}

							// 不允许添加主键列
							if (column.isPrimaryKey) {
								uni.showToast({
									title: '不能添加主键列到已有表',
									icon: 'none'
								});
								return;
							}

							try {
								// 添加列到表
								const success = await addColumnToTable(
									this.tableId,
									this.tableName,
									column
								);

								if (success) {
									uni.showToast({
										title: '添加列成功',
										icon: 'success'
									});

									// 重新加载列信息
									this.loadColumns();
								} else {
									uni.showToast({
										title: '添加列失败',
										icon: 'none'
									});
								}
							} catch (e) {
								console.error('添加列失败', e);
								uni.showToast({
									title: '添加列失败: ' + (e.message || e),
									icon: 'none'
								});
							}
						}
					}
				});
			},

			// 删除列
			deleteColumn(column) {
				// 确认删除
				uni.showModal({
					title: '删除列',
					content: `确定要删除列 '${column.name}' 吗？此操作不可撤销，该列的所有数据都将丢失。`,
					confirmText: '删除',
					confirmColor: '#FF0000',
					success: async (res) => {
						if (res.confirm) {
							try {
								// 删除列
								await deleteColumnFromTable(
									this.tableId,
									this.tableName,
									column.id,
									column.name
								);

								uni.showToast({
									title: '删除列成功',
									icon: 'success'
								});

								// 重新加载列信息
								this.loadColumns();
							} catch (e) {
								console.error('删除列失败', e);
								uni.showToast({
									title: '删除列失败: ' + (e.message || e),
									icon: 'none'
								});
							}
						}
					}
				});
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #007AFF;
		padding: 20rpx 30rpx;
		padding-top: var(--status-bar-height);
	}

	.header-left, .header-right {
		width: 120rpx;
	}

	.header-back {
		color: #FFFFFF;
		font-size: 28rpx;
	}

	.header-title {
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: bold;
		max-width: 400rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.form-container {
		flex: 1;
		padding: 30rpx;
	}

	.form-item {
		margin-bottom: 30rpx;
	}

	.form-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.form-label {
		font-size: 32rpx;
		font-weight: bold;
	}

	.form-action {
		background-color: #007AFF;
		padding: 10rpx 20rpx;
		border-radius: 6rpx;
	}

	.form-action-text {
		color: #FFFFFF;
		font-size: 24rpx;
	}

	.empty-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 200rpx;
		background-color: #FFFFFF;
		border-radius: 8rpx;
		color: #999999;
		font-size: 28rpx;
	}

	.column-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.column-item {
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 20rpx;
	}

	.column-item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.column-name {
		font-size: 30rpx;
		font-weight: bold;
	}

	.column-actions {
		display: flex;
		gap: 10rpx;
	}

	.column-action {
		background-color: #FF3B30;
		padding: 6rpx 12rpx;
		border-radius: 4rpx;
	}

	.column-action-text {
		color: #FFFFFF;
		font-size: 22rpx;
	}

	.column-details {
		display: flex;
		flex-direction: column;
		gap: 6rpx;
	}

	.column-type {
		font-size: 26rpx;
		color: #333333;
	}

	.column-attributes {
		display: flex;
		flex-wrap: wrap;
		gap: 10rpx;
	}

	.column-attribute {
		background-color: #F0F0F0;
		padding: 4rpx 12rpx;
		border-radius: 4rpx;
		font-size: 22rpx;
		color: #666666;
	}

	.column-foreign-key {
		font-size: 22rpx;
		color: #666666;
		font-style: italic;
		margin-top: 6rpx;
	}

	.tips {
		margin-top: 30rpx;
		background-color: #FFF9E6;
		border-radius: 8rpx;
		padding: 20rpx;
	}

	.tips-text {
		font-size: 24rpx;
		color: #996633;
		line-height: 1.5;
		display: block;
	}
</style>
