// 本地数据库服务
const DB_NAME = 'myaccount.db'
const DB_PATH = '_doc/myaccount.db'

let isDatabaseOpen = false

// 打开数据库
const openDatabase = async () => {
    if (isDatabaseOpen) {
        console.log('数据库已经打开')
        return
    }

    return new Promise((resolve, reject) => {
        console.log('尝试打开数据库')
        plus.sqlite.openDatabase({
            name: DB_NAME,
            path: DB_PATH,
            success: () => {
                console.log('数据库打开成功')
                isDatabaseOpen = true
                resolve()
            },
            fail: (e) => {
                // 如果是因为数据库已经打开而失败，我们认为这是成功的情况
                if (e.code === -1402) {
                    console.log('数据库已经打开')
                    isDatabaseOpen = true
                    resolve()
                    return
                }
                console.error('数据库打开失败，错误详情:', e)
                reject(new Error('数据库打开失败: ' + JSON.stringify(e)))
            }
        })
    })
}

// 关闭数据库
const closeDatabase = async () => {
    if (!isDatabaseOpen) {
        console.log('数据库已经关闭')
        return
    }

    return new Promise((resolve, reject) => {
        console.log('尝试关闭数据库')
        plus.sqlite.closeDatabase({
            name: DB_NAME,
            success: () => {
                console.log('数据库关闭成功')
                isDatabaseOpen = false
                resolve()
            },
            fail: (e) => {
                // 如果是因为数据库已经关闭而失败，我们认为这是成功的情况
                if (e.code === -1401) {
                    console.log('数据库已经关闭')
                    isDatabaseOpen = false
                    resolve()
                    return
                }
                console.error('数据库关闭失败，错误详情:', e)
                reject(new Error('数据库关闭失败: ' + JSON.stringify(e)))
            }
        })
    })
}

// 处理SQL参数并执行SQL操作的通用函数
const executeSqlOperation = async (operation, sql, params = [], dbName = DB_NAME) => {
    try {
        const operationType = operation === 'execute' ? 'SQL' : '查询'
        console.log(`准备执行${operationType}:`, { sql, params })

        if (!isDatabaseOpen && dbName === DB_NAME) {
            await openDatabase()
        }

        // 确保参数是数组
        const values = Array.isArray(params) ? params : [params]

        // 打印详细的参数信息
        console.log(`${operationType}详情:`, {
            sql,
            params: values,
            paramsType: values.map(p => typeof p),
            paramsLength: values.length
        })

        // 直接替换参数
        let finalSql = sql
        values.forEach((value) => {
            const placeholder = '?'
            const position = finalSql.indexOf(placeholder)
            if (position !== -1) {
                const quotedValue = typeof value === 'string' ? `'${value}'` : value
                finalSql = finalSql.substring(0, position) + quotedValue + finalSql.substring(position + 1)
            }
        })

        console.log(`最终执行的${operationType}:`, finalSql)

        return new Promise((resolve, reject) => {
            const method = operation === 'execute' ? 'executeSql' : 'selectSql'
            plus.sqlite[method]({
                name: dbName,
                sql: finalSql,
                success: (res) => {
                    console.log(`${operationType}执行成功:`, { sql: finalSql, result: res })
                    resolve(res)
                },
                fail: (e) => {
                    console.error(`${operationType}执行失败:`, {
                        sql: finalSql,
                        error: e,
                        errorCode: e.code,
                        errorMessage: e.message
                    })
                    reject(new Error(`${operationType}执行失败: ` + JSON.stringify(e)))
                }
            })
        })
    } catch (error) {
        console.error(`执行${operation === 'execute' ? 'SQL' : '查询SQL'}失败:`, error)
        throw error
    }
}

// 执行SQL语句
const executeSql = async (sql, params = [], dbName = DB_NAME) => {
    return executeSqlOperation('execute', sql, params, dbName)
}

// 查询SQL
const selectSql = async (sql, params = [], dbName = DB_NAME) => {
    return executeSqlOperation('select', sql, params, dbName)
}

// 初始化数据库
const initDatabase = async () => {
    try {
        console.log('开始初始化数据库')
        await openDatabase()

        // 创建用户表
        await executeSql(`
            CREATE TABLE IF NOT EXISTS users (
                user_id TEXT PRIMARY KEY,
                username TEXT NOT NULL CHECK(username != ''),
                password TEXT NOT NULL,
                email TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(username),
                UNIQUE(email)
            )
        `)

        // 创建分类表
        await executeSql(`
            CREATE TABLE IF NOT EXISTS categories (
                category_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                parent_id TEXT,
                name TEXT NOT NULL,
                level INTEGER NOT NULL,
                sort_order INTEGER NOT NULL,
                total_amount REAL DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `)

        // 创建支出表
        await executeSql(`
            CREATE TABLE IF NOT EXISTS expenses (
                record_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                category_id TEXT NOT NULL,
                target TEXT,
                amount REAL NOT NULL,
                record_date DATE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `)

        console.log('数据库表初始化成功')
    } catch (error) {
        console.error('数据库初始化失败:', error)
        throw error
    }
}

// 获取所有表名
const getAllTables = async () => {
    try {
        console.log('开始获取所有表名')
        const results = await selectSql(
            "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
        )
        console.log('获取到的表名:', results)
        return results.map(row => row.name)
    } catch (error) {
        console.error('获取表名失败:', error)
        throw error
    }
}

// 获取指定表的所有数据
const getAllFromTable = async (tableName) => {
    try {
        console.log(`开始获取表 ${tableName} 的所有数据`)
        const results = await selectSql(`SELECT * FROM ${tableName}`)
        console.log(`表 ${tableName} 数据获取成功:`, results.length, '条记录')
        return results
    } catch (error) {
        console.error(`获取表 ${tableName} 数据失败:`, error)
        throw error
    }
}

// 获取表的创建语句
const getTableCreateStatement = async (tableName) => {
    try {
        console.log(`开始获取表 ${tableName} 的创建语句`)
        const results = await selectSql(
            "SELECT sql FROM sqlite_master WHERE type='table' AND name=?",
            [tableName]
        )
        if (results.length > 0) {
            console.log(`表 ${tableName} 创建语句:`, results[0].sql)
            return results[0].sql
        }
        throw new Error(`表 ${tableName} 不存在`)
    } catch (error) {
        console.error(`获取表 ${tableName} 创建语句失败:`, error)
        throw error
    }
}

// 创建备份数据库
const createBackupDatabase = async (filePath) => {
    try {
        console.log('开始创建备份数据库:', filePath)

        // 先尝试删除已存在的文件
        try {
            await new Promise((resolve) => {
                plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
                    entry.remove(() => {
                        console.log('已删除旧的备份数据库文件')
                        resolve()
                    }, (error) => {
                        console.log('删除旧文件失败，可能不存在:', error)
                        resolve()
                    })
                }, (error) => {
                    console.log('旧文件不存在，继续创建新文件')
                    resolve()
                })
            })

            // 等待一段时间确保文件删除操作完成
            await new Promise(resolve => setTimeout(resolve, 200))
        } catch (error) {
            console.log('处理旧文件时出错，继续创建新文件:', error)
        }

        // 创建新的备份数据库
        await new Promise((resolve, reject) => {
            plus.sqlite.openDatabase({
                name: 'backup_db',
                path: filePath,
                success: function(e) {
                    console.log('备份数据库创建成功')
                    resolve()
                },
                fail: function(e) {
                    console.error('备份数据库创建失败:', e)
                    reject(new Error('创建备份数据库失败: ' + JSON.stringify(e)))
                }
            })
        })

        // 初始化数据库设置
        const initSettings = [
            // 设置同步模式为NORMAL，在写入数据时更高效
            {
                sql: 'PRAGMA synchronous=NORMAL',
                message: '已设置同步模式为NORMAL'
            },
            // 设置日志模式为DELETE，避免WAL模式的问题
            {
                sql: 'PRAGMA journal_mode=DELETE',
                message: '已设置日志模式为DELETE'
            },
            // 设置页面缓存大小
            {
                sql: 'PRAGMA page_size=4096',
                message: '已设置页面大小为4KB'
            },
            // 设置缓存大小
            {
                sql: 'PRAGMA cache_size=2000',
                message: '已设置缓存大小'
            }
        ]

        // 执行初始化设置
        for (const setting of initSettings) {
            try {
                await new Promise((resolve) => {
                    plus.sqlite.executeSql({
                        name: 'backup_db',
                        sql: setting.sql,
                        success: function() {
                            console.log(setting.message)
                            resolve()
                        },
                        fail: function(e) {
                            console.log(`设置 ${setting.sql} 失败，继续执行:`, e)
                            resolve()
                        }
                    })
                })
            } catch (error) {
                console.log(`执行 ${setting.sql} 时出错，继续执行:`, error)
            }
        }

        return 'backup_db'
    } catch (error) {
        console.error('创建备份数据库失败:', error)
        throw error
    }
}

// 解析CREATE TABLE语句以获取列信息
function parseCreateStatement(createSql) {
    const columnInfo = {}

    // 提取列定义部分
    const match = createSql.match(/CREATE TABLE \w+ \(([\s\S]+)\)/)
    if (!match) return columnInfo

    // 分割列定义，但要正确处理括号内的逗号
    const columnDefs = []
    let currentDef = ''
    let bracketCount = 0

    for (const char of match[1]) {
        if (char === '(') bracketCount++
        if (char === ')') bracketCount--

        if (char === ',' && bracketCount === 0) {
            columnDefs.push(currentDef.trim())
            currentDef = ''
        } else {
            currentDef += char
        }
    }
    if (currentDef.trim()) {
        columnDefs.push(currentDef.trim())
    }

    // 处理主键和唯一约束
    let primaryKey = null
    const uniqueColumns = new Set()

    for (const def of columnDefs) {
        if (def.toUpperCase().startsWith('PRIMARY KEY')) {
            const pkMatch = def.match(/PRIMARY KEY\s*\(([^)]+)\)/)
            if (pkMatch) {
                primaryKey = pkMatch[1].split(',').map(col => col.trim())
            }
            continue
        }

        if (def.toUpperCase().startsWith('UNIQUE')) {
            const uniqueMatch = def.match(/UNIQUE\s*\(([^)]+)\)/)
            if (uniqueMatch) {
                uniqueMatch[1].split(',').map(col => col.trim()).forEach(col => uniqueColumns.add(col))
            }
            continue
        }

        // 处理列定义
        const parts = def.split(/\s+/)
        const columnName = parts[0]
        const columnType = parts[1]

        // 检查列级约束
        const isPrimaryKey = def.toUpperCase().includes('PRIMARY KEY')
        const isUnique = def.toUpperCase().includes('UNIQUE')
        const isNotNull = def.toUpperCase().includes('NOT NULL')

        // 提取默认值
        let defaultValue = null
        const defaultMatch = def.match(/DEFAULT\s+([^,\s]+)/)
        if (defaultMatch) {
            defaultValue = defaultMatch[1]
            if (defaultValue === 'CURRENT_TIMESTAMP') {
                defaultValue = () => new Date().toISOString()
            }
        }

        columnInfo[columnName] = {
            type: columnType,
            notNull: isNotNull || isPrimaryKey, // 主键列自动为NOT NULL
            primaryKey: isPrimaryKey,
            unique: isUnique,
            defaultCurrentTimestamp: def.includes('DEFAULT CURRENT_TIMESTAMP'),
            default: defaultValue
        }
    }

    // 更新表级主键信息
    if (primaryKey) {
        primaryKey.forEach(col => {
            if (columnInfo[col]) {
                columnInfo[col].primaryKey = true
                columnInfo[col].notNull = true
            }
        })
    }

    // 更新表级唯一约束信息
    uniqueColumns.forEach(col => {
        if (columnInfo[col]) {
            columnInfo[col].unique = true
        }
    })

    return columnInfo
}

// 在备份数据库中重建表并插入数据
const recreateTableInBackup = async (backupDbName, tableName, data) => {
    try {
        console.log(`开始在备份数据库中重建表 ${tableName}`)

        // 获取表的创建语句
        const createStatement = await getTableCreateStatement(tableName)
        console.log(`表 ${tableName} 的创建语句:`, createStatement)

        // 先尝试删除已存在的表
        await executeSql(`DROP TABLE IF EXISTS ${tableName}`, [], backupDbName)
            .then(() => console.log(`已删除旧的 ${tableName} 表`))
            .catch(e => console.log(`删除表 ${tableName} 时出错:`, e))

        // 创建表
        await executeSql(createStatement, [], backupDbName)
            .then(() => console.log(`表 ${tableName} 创建成功`))
            .catch(e => {
                console.error(`创建表 ${tableName} 失败:`, e)
                throw e
            })

        // 如果有数据，插入数据
        if (data && data.length > 0) {
            console.log(`开始插入表 ${tableName} 的数据:`, data.length, '条记录')

            // 开始事务
            await executeSql('BEGIN TRANSACTION', [], backupDbName)
                .then(() => console.log(`开始事务: 表 ${tableName}`))
                .catch(e => {
                    console.error(`开始事务失败:`, e)
                    throw e
                })

            try {
                // 批量插入数据，每批100条
                const BATCH_SIZE = 100;
                let batchCount = 0;

                for (let i = 0; i < data.length; i += BATCH_SIZE) {
                    const batch = data.slice(i, i + BATCH_SIZE);
                    batchCount++;
                    console.log(`开始处理第 ${batchCount} 批数据，共 ${batch.length} 条`);

                    // 逐条插入数据
                    for (const row of batch) {
                    // 构建INSERT语句，使用具体的值而不是占位符
                    const columns = Object.keys(row)
                    const values = columns.map(col => {
                        const value = row[col]
                        if (value === null || value === undefined) {
                            return 'NULL'
                        }
                        if (typeof value === 'string') {
                            // 转义字符串中的单引号
                            return `'${value.replace(/'/g, "''")}'`
                        }
                        if (value instanceof Date) {
                            return `'${value.toISOString()}'`
                        }
                        return value.toString()
                    })

                    const insertSql = `INSERT INTO ${tableName} (${columns.join(',')}) VALUES (${values.join(',')})`
                    console.log(`执行SQL:`, insertSql)

                    await new Promise((resolve, reject) => {
                        plus.sqlite.executeSql({
                            name: backupDbName,
                            sql: insertSql,
                            success: function() {
                                resolve()
                            },
                            fail: function(e) {
                                console.error(`插入数据失败:`, {
                                    error: e,
                                    sql: insertSql
                                })
                                reject(e)
                            }
                        })
                    })
                    }

                    // 每批数据完成后提交事务并开始新事务
                    if (i + BATCH_SIZE < data.length) {
                        // 提交当前批次的事务
                        await executeSql('COMMIT', [], backupDbName)
                            .then(() => console.log(`提交第 ${batchCount} 批数据事务`))
                            .catch(e => {
                                console.error(`提交第 ${batchCount} 批数据事务失败:`, e)
                                throw e
                            })

                        // 开始新的事务
                        await executeSql('BEGIN TRANSACTION', [], backupDbName)
                            .then(() => console.log(`开始第 ${batchCount + 1} 批数据事务`))
                            .catch(e => {
                                console.error(`开始第 ${batchCount + 1} 批数据事务失败:`, e)
                                throw e
                            })

                        // 每批数据完成后等待一小段时间
                        await new Promise(resolve => setTimeout(resolve, 100))
                    }
                }

                // 提交最后一批数据的事务
                await executeSql('COMMIT', [], backupDbName)
                    .then(() => console.log(`提交最后一批数据事务: 表 ${tableName}`))
                    .catch(e => {
                        console.error(`提交最后一批数据事务失败:`, e)
                        throw e
                    })

                console.log(`表 ${tableName} 数据插入完成`)
            } catch (error) {
                // 如果出错，回滚事务
                console.error(`插入数据出错，准备回滚:`, error)
                await executeSql('ROLLBACK', [], backupDbName)
                    .then(() => console.log(`事务已回滚: 表 ${tableName}`))
                    .catch(e => console.error(`回滚事务失败:`, e))
                throw error
            }
        }
    } catch (error) {
        console.error(`重建表 ${tableName} 失败:`, error)
        throw error
    }
}

// 关闭备份数据库连接
const closeBackupDatabase = async (backupDbName) => {
    try {
        console.log('开始关闭备份数据库')

        // 确保所有数据已写入
        const syncSteps = [
            // 步骤1: 提交所有活跃事务
            {
                sql: 'COMMIT',
                message: '确保没有活跃事务',
                errorMessage: '没有活跃事务需要提交'
            },
            // 步骤2: 强制写入所有数据
            {
                sql: 'PRAGMA locking_mode=EXCLUSIVE',
                message: '已设置独占锁定模式',
                errorMessage: '设置锁定模式失败'
            },
            // 步骤3: 设置同步模式为FULL
            {
                sql: 'PRAGMA synchronous=FULL',
                message: '已设置同步模式为FULL',
                errorMessage: '设置同步模式失败'
            },
            // 步骤4: 切换日志模式为DELETE
            {
                sql: 'PRAGMA journal_mode=DELETE',
                message: '已切换到DELETE日志模式',
                errorMessage: '切换日志模式失败'
            },
            // 步骤5: 执行检查点
            {
                sql: 'PRAGMA wal_checkpoint(FULL)',
                message: '检查点完成',
                errorMessage: '检查点操作失败'
            }
        ]

        // 执行所有同步步骤
        for (const step of syncSteps) {
            try {
                await executeSql(step.sql, [], backupDbName).catch(e => {
                    console.log(`${step.errorMessage}，继续执行:`, e)
                })
                console.log(step.message)
                // 添加短暂延迟，确保操作完成
                await new Promise(resolve => setTimeout(resolve, 100))
            } catch (error) {
                console.log(`执行 ${step.sql} 时出错，继续执行:`, error)
            }
        }

        // 再次执行一些关键操作，确保数据已写入
        try {
            await executeSql('PRAGMA optimize', [], backupDbName).catch(e => {
                console.log('数据库优化失败，继续执行:', e)
            })
            console.log('数据库优化完成')
        } catch (error) {
            console.log('数据库优化时出错，继续执行:', error)
        }

        // 关闭数据库前再次等待
        await new Promise(resolve => setTimeout(resolve, 500))

        // 关闭数据库
        let closeAttempts = 0
        const maxAttempts = 3

        while (closeAttempts < maxAttempts) {
            try {
                await new Promise((resolve, reject) => {
                    plus.sqlite.closeDatabase({
                        name: backupDbName,
                        success: function() {
                            console.log(`备份数据库关闭成功 (尝试 ${closeAttempts + 1}/${maxAttempts})`)
                            resolve()
                        },
                        fail: function(e) {
                            console.error(`备份数据库关闭失败 (尝试 ${closeAttempts + 1}/${maxAttempts}):`, e)
                            if (closeAttempts === maxAttempts - 1) {
                                // 最后一次尝试失败时才拒绝
                                reject(new Error('关闭备份数据库失败: ' + JSON.stringify(e)))
                            } else {
                                resolve(false) // 表示需要重试
                            }
                        }
                    })
                })
                // 如果成功关闭，跳出循环
                break
            } catch (error) {
                if (closeAttempts === maxAttempts - 1) {
                    throw error // 最后一次尝试失败时抛出错误
                }
            }
            closeAttempts++
            // 在重试前等待
            await new Promise(resolve => setTimeout(resolve, 300))
        }
    } catch (error) {
        console.error('关闭备份数据库失败:', error)
        throw error
    }
}

// 获取数据库文件的实际路径
const getDatabaseFilePath = async () => {
    try {
        console.log('开始获取数据库文件路径')

        // 确保数据库已经初始化
        if (!isDatabaseOpen) {
            await openDatabase()
        }

        // 获取数据库文件的实际路径
        const fullPath = plus.io.convertLocalFileSystemURL(DB_PATH)
        console.log('数据库文件的实际路径:', fullPath)

        return fullPath
    } catch (error) {
        console.error('获取数据库文件路径失败:', error)
        throw error
    }
}

// 检测是否为鸿蒙系统
const isHarmonyOS = () => {
    try {
        const Build = plus.android.importClass("android.os.Build")
        const manufacturer = Build.MANUFACTURER
        const model = Build.MODEL
        const brand = Build.BRAND

        console.log('设备信息:', { manufacturer, model, brand })

        // 检测是否为华为设备或鸿蒙系统
        return manufacturer.toLowerCase().includes('huawei') ||
               brand.toLowerCase().includes('huawei') ||
               model.toLowerCase().includes('harmony') ||
               model.toLowerCase().includes('hongmeng')
    } catch (error) {
        console.error('检测设备系统时出错:', error)
        return false
    }
}

// 安全关闭数据库
const safeCloseDatabase = async () => {
    if (isDatabaseOpen) {
        await closeDatabase()
        // 等待一段时间确保数据库完全关闭
        await new Promise(resolve => setTimeout(resolve, 1000))
    }
}

// 安全重新打开数据库
const safeOpenDatabase = async () => {
    try {
        await openDatabase()
    } catch (e) {
        console.error('重新打开数据库失败:', e)
    }
}

// 安全关闭文件通道
const safeCloseChannels = (sourceChannel, destChannel) => {
    try {
        if (sourceChannel) sourceChannel.close()
        if (destChannel) destChannel.close()
    } catch (e) {
        console.error('关闭通道失败:', e)
    }
}

// 直接复制数据库文件
const backupDatabaseByFileCopy = async (targetPath) => {
    let sourceChannel = null
    let destChannel = null
    let sourceFis = null
    let targetFos = null

    try {
        console.log('开始直接复制数据库文件到:', targetPath)

        // 获取原始数据库文件路径
        const sourceFilePath = await getDatabaseFilePath()
        console.log('原始数据库文件路径:', sourceFilePath)

        // 先关闭数据库，确保文件可以被完整读取
        await safeCloseDatabase()

        // 使用原生 Java 方法
        const File = plus.android.importClass("java.io.File")
        const FileInputStream = plus.android.importClass("java.io.FileInputStream")
        const FileOutputStream = plus.android.importClass("java.io.FileOutputStream")
        const FileChannel = plus.android.importClass("java.nio.channels.FileChannel")

        // 创建源文件对象
        const sourceFile = new File(sourceFilePath)
        if (!sourceFile.exists()) {
            throw new Error('源数据库文件不存在')
        }

        // 创建目标文件对象
        const targetFile = new File(targetPath)

        // 如果目标文件已存在，先删除
        if (targetFile.exists()) {
            targetFile.delete()
        }

        // 创建目标文件的父目录
        const parentDir = targetFile.getParentFile()
        if (!parentDir.exists()) {
            parentDir.mkdirs()
        }

        // 使用FileChannel复制文件（更高效）
        sourceFis = new FileInputStream(sourceFile)
        targetFos = new FileOutputStream(targetFile)

        sourceChannel = sourceFis.getChannel()
        destChannel = targetFos.getChannel()

        // 使用transferFrom方法复制文件
        destChannel.transferFrom(sourceChannel, 0, sourceChannel.size())

        console.log('文件复制成功，大小:', targetFile.length())

        return targetFile.length()
    } catch (error) {
        console.error('直接复制数据库文件失败:', error)
        throw error
    } finally {
        // 确保资源被正确关闭
        safeCloseChannels(sourceChannel, destChannel)
        if (sourceFis) sourceFis.close()
        if (targetFos) targetFos.close()

        // 确保数据库被重新打开
        await safeOpenDatabase()
    }
}

// 检查数据库是否已打开
const checkDatabaseOpen = () => {
    return isDatabaseOpen
}

// 检查是否支持VACUUM INTO命令
const isSupportVacuumInto = async () => {
    try {
        const versionResult = await selectSql('SELECT sqlite_version() as version')
        const sqliteVersion = versionResult[0].version
        console.log('SQLite版本:', sqliteVersion)

        // 将版本字符串转换为数字进行比较
        const versionParts = sqliteVersion.split('.')
        const majorVersion = parseInt(versionParts[0])
        const minorVersion = parseInt(versionParts[1])

        // VACUUM INTO需要SQLite 3.27.0及以上版本
        return (majorVersion > 3) || (majorVersion === 3 && minorVersion >= 27)
    } catch (error) {
        console.error('检查SQLite版本失败:', error)
        return false
    }
}

export const dbService = {
    openDatabase,
    closeDatabase,
    executeSql,
    selectSql,
    initDatabase,
    getAllTables,
    getAllFromTable,
    getTableCreateStatement,
    createBackupDatabase,
    recreateTableInBackup,
    closeBackupDatabase,
    getDatabaseFilePath,
    isDatabaseOpen: checkDatabaseOpen,
    backupDatabaseByFileCopy,
    isSupportVacuumInto,
    isHarmonyOS
}

